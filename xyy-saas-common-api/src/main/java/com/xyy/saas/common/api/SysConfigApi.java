package com.xyy.saas.common.api;

import com.xyy.saas.common.dto.SystemConfigDto;
import com.xyy.saas.common.dto.SystemConfigPOSDto;
import com.xyy.saas.common.dto.SystemConfigWEBDto;
import com.xyy.saas.common.util.ResultVO;

/**
 * 
 * <AUTHOR>
 *
 */
public interface SysConfigApi {

	/**
	 * 更新控制面板
	 * @param config
	 * @return
	 */
	boolean updateSystemConfig(SystemConfigDto config);

	/**
	 * 机构控制面板初始化
	 * @param organSign
	 * @return
	 */
	boolean initSystemConfig(String organSign);

	/**
	 * 根据机构标识，查询机构控制面板
	 * @param organSign
	 * @return
	 */
	SystemConfigDto querySystemConfig(String organSign);
	/**
	 * 获取POS配置
	 * @return
	 */
	ResultVO<SystemConfigPOSDto> getPOSSysConfig(String organSign) ;
	/**
	 * 获取WEB配置
	 * @return
	 */
	ResultVO<SystemConfigWEBDto> getWEBSysConfig(String organSign) ;
	/**
	 * 更新配置POS
	 * @param config
	 * @return
	 */
	ResultVO<Boolean> updateForPOS(SystemConfigPOSDto config,String organSign);
	/**
	 * 更新配置WEB
	 * @param config
	 * @return
	 */
	ResultVO<Boolean> updateForWEB(SystemConfigWEBDto config,String organSign);
}
