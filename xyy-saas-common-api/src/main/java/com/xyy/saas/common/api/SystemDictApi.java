package com.xyy.saas.common.api;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.datasync.api.DataSyncApi;
import com.xyy.saas.common.dto.QueryDictByOrgTypeDto;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.dto.SystemDictQueryDto;
import com.xyy.saas.common.util.ResultVO;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface SystemDictApi extends DataSyncApi<SystemDictDto>{

    /**
     * 根据id查询
     * @param id
     * @param organSign
     * @return
     */
	SystemDictDto findById(Integer id,String organSign);

    /**
     * 插入
     * @param systemDictDto
     * @return
     */
    Integer insert(SystemDictDto systemDictDto);

	/**
	 * 新版保存-网关调用
	 * @param systemDictDto
	 * @return
	 */
	ResultVO insertNew(SystemDictDto systemDictDto);


	/**
	 * 新版更新-网关调用
	 * @param systemDictDto
	 * @return
	 */
	ResultVO updateNew(SystemDictDto systemDictDto);

    /**
     * 更新
     * @param systemDictDto
     * @return
     */
	int update(SystemDictDto systemDictDto);

    /**
     * 查询最大排序值
     * @param bussinessId
     * @param organSign
     * @return
     */
    Integer findMaxSort(Integer bussinessId,String organSign);

	/**
	 * 根据下拉框类型查询对应字典数据   启用且未删除的
	 * @param bussinessId
	 * @param organSign
	 */
	List<SystemDictDto> findSystemDictDto(Integer bussinessId,String organSign);

	/**
	 * 根据类型查询对应字典数据  未删除的
	 * @param bussinessId
	 * @param organSign
	 */
	List<SystemDictDto> findSystemDictDtoNotDelete(Integer bussinessId,String organSign);

	/**
	 * 根据下拉框类型查询对应字典数据
	 *
	 * @param bussinessId
	 * @return
	 */
	List<SystemDictDto> findSystemDictDtoByBussinessId(Integer bussinessId);

	/**
	 * 分页查询 最多1000条数据
	 * @param queryDto 查询条件
	 * @param organSign 机构号
	 * @return
	 */
	PageInfo<SystemDictDto> findPageByCondition(SystemDictQueryDto queryDto, String organSign);

    /**
     * 批量插入
     * @param systemDictDtos
     */
	void batchInsert(List<SystemDictDto> systemDictDtos);

	/**
	 * 批量插入-无id
	 * @param systemDictDtos
	 */
	void batchInsertNoId(List<SystemDictDto> systemDictDtos);

    /**
     * 批量删除
     * @param ids
     * @param organSign
     */
    int batchDelete(List<String> ids,String organSign);

    /**
     * 根据业务id列表查询  启用且未删除的
     * @param bussinessIdList
     * @param organSign
     * @return
     */
    List<SystemDictDto> findSystemDictDtoBybussinessIds(List<Integer> bussinessIdList,String organSign);

	/**
	 * 根据业务id列表查询
	 * @param bussinessIdList
	 * @param organSign
	 * @param yn  是否删除 1 有效、0 删除
	 * @param status 是否启用 1 启用、0 禁用
	 * @return
	 */
	List<SystemDictDto> findSystemDictDtoBybussinessIds(List<Integer> bussinessIdList,String organSign,Integer yn,Integer status);

	/**
	 * 基础信息列表页下拉框
	 * @param bussinessId
	 * @param organSign
	 * @return
	 */
	List<SystemDictDto> findSystemDictBussinessTypeForList(Integer bussinessId,String organSign);

	/**
	 * 分页查询字典信息
	 * @param pageInfo
	 * @param systemDictDto
	 * @return
	 */
	PageInfo<SystemDictDto> findPageInfo( PageInfo pageInfo, SystemDictDto systemDictDto);

	/**
	 * 查询字典值
	 * @param bussinessId 业务id
	 * @param idList 字典值
	 * @param organSign 字典值
	 * @return
	 */
	List<Map<String,Object>> findListBybussinessIdAndIds(Integer bussinessId, List<Integer> idList,String organSign);

	/**
	 * 查询字典单值
	 * @param bussinessId 业务id
	 * @param value 字典值
	 * @param organSign 字典值
	 * @return
	 */
	SystemDictDto findBybussinessIdAndValue(Integer bussinessId, Integer value,String organSign);

	/**
	 * 查询字典map
	 * @param bussinessIds
	 * @param organSign
	 * @return
	 */
	Map<Integer, Map<String,Integer>> findByBussinessIds(List<Integer> bussinessIds,String organSign);

	/**
	 * 查询字典map
	 * @param bussinessIds
	 * @param organSign
	 * @return
	 */
	Map<Integer, Map<Integer,String>> findByBussinessIdsToMap(List<Integer> bussinessIds,String organSign);

	/**
	 * 查询字典map
	 * @param bussinessIds
	 * @return
	 */
	Map<Integer, Map<Integer,String>> findByBussinessIdsToMapAll(List<Integer> bussinessIds);

	/**
	 *
	 * @param map key为业务id value为字典表中不存在的name集合
	 * @return
	 */
	Map<Integer, Map<String,Integer>> batchInsertSystemDict(Map<Integer, Set<String>> map,String organSign);

	/**
	 * 查询字典map 不存在该name 直接添加
	 * @param bussinessId
	 * @param name
	 * @param organSign
	 * @return
	 */
    SystemDictDto insertByBussinessIdAndName(Integer bussinessId,String name,String organSign);
    /**
     * 2.0数据迁移至3.0数据
     * @param pos
     * @param user
     * @param organSign
     * @return
     */
    String insertSysDictData(List<SystemDictDto> pos,String organSign,String user);

	/**
	 * 查询字典列表
	 * @param bussinessId 业务id
	 * @param name 名称
	 * @param organSign 药店标识
	 * @param flag 0精准查询，1模糊查询，2=，3<>
	 * @return
	 */
	List<SystemDictDto> findBybussinessIdAndLikeName(Integer bussinessId,String name,String organSign,int flag);

	/**
	 * dubbo监控api方法
	 * @return true字符串
	 */
	public String heartBeat();

    /**
     * 根据下拉框类型查询对应字典数据(支持循环查询，直到查询出全部)
     *
     * @param bussinessId
     * @param startCount
     * @param endCount
     * @return
     */
    List<SystemDictDto> findSystemDictDtoByBussinessId(Integer bussinessId,Integer startCount,Integer endCount);

	/**
	 * 根据下拉框类型查询对应字典数据(支持循环查询，直到查询出全部)
	 *
	 * @param bussinessId
	 * @param organSign
	 * @param status
	 * @return
	 */
	List<SystemDictDto> findSystemDictDtoByBussinessId(Integer bussinessId, String organSign, Integer status);

    void InsertProductType() throws  IOException;

	/**
	 * 连锁需要忽略的经营范围类型（医疗器械,生物制品,精神药品）
	 * @return
	 */
	List<String> ignoreSystemDictIds();

	/**
	 * 将系统默认字典的baseVersion刷成字典表最大baseVersion自增
	 * 批量更新字典并发mq给pos端
	 * @param organSigns
	 * @return
	 */
	List<SystemDictDto> batchUpdateSystemDictAndSendMq(List<String> organSigns);

	/**
	 * 批量增加默认字典
	 * @param systemDictDtoList
	 * @param organSigns
	 * @return
	 */
	ResultVO batchInsertCommonDict(List<SystemDictDto> systemDictDtoList, List<String> organSigns);

	/**
	 * 增加系统默认字典
	 * @param systemDictDto
	 * @param organSigns
	 * @return
	 */
	ResultVO insertCommonDict(SystemDictDto systemDictDto, List<String> organSigns);

	/**
	 * 根据机构类型查询字典项
	 * @param queryDictByOrgTypeDto
	 * @return
	 */
	ResultVO<PageInfo<SystemDictDto>> queryDictByOrgType(QueryDictByOrgTypeDto queryDictByOrgTypeDto);

	ResultVO<PageInfo<SystemDictDto>> queryDictNoDefault(QueryDictByOrgTypeDto queryDictByOrgTypeDto);

	/**
	 * 刷数据：将自定义的经营范围的parentId更新为"自定义"
	 * 给pos端发mq
	 * @param organSigns 需要发mq的机构列表
	 * @return
	 */
	ResultVO updateCustomDictParentId(List<String> organSigns);

	/**
	 * 根据机构号查询自定义经营范围(刷数据用)
	 * @param organSign
	 * @return
	 */
	List<Long> getCustomType(String organSign);

	void batchUpdate(List<SystemDictDto> dictList);

	List<SystemDictDto> findBybussinessIdAndValues(Integer orderSource, List<Integer> codes);
}
