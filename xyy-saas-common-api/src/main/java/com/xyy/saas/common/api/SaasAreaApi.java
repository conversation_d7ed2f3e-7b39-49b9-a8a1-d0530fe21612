package com.xyy.saas.common.api;

import com.xyy.saas.common.dto.SaasRegionBusinessDto;
import com.xyy.saas.common.dto.XyySaasRegionParamsDto;
import com.xyy.saas.common.dto.SaasAreaDto;
import com.xyy.saas.common.util.ResultVO;

import java.util.List;

/**
 * @annotation:
 * <AUTHOR>
 * @create 2019-05-23 9:41
 **/
public interface SaasAreaApi {

    @Deprecated
    List<SaasAreaDto> findByAreaName(List<String> areaName);

    @Deprecated
    List<SaasAreaDto> getNameListByParentId(SaasAreaDto saasAreaDto);

    @Deprecated
    List<SaasAreaDto> getProvinceList();

    /**
     * 不传parentCode默认查全部省份,传parentCode查询单个省份
     */
    List<SaasRegionBusinessDto> getAllByParentCode(XyySaasRegionParamsDto districtDto);

    /**
     * 查询单个AreaCode
     */
    SaasRegionBusinessDto queryRegionByAreaCode(XyySaasRegionParamsDto districtDto);

    /**
     * 查询多个AreaCode
     */
    List<SaasRegionBusinessDto> queryRegionByAreaCodeList(XyySaasRegionParamsDto districtDto);

    /**
     * 查询多个LevelList
     */
    List<SaasRegionBusinessDto> queryRegionByLevelList(XyySaasRegionParamsDto districtDto);

    /**
     * 查询全部省市区集合
     */
    List<SaasRegionBusinessDto> mergeAllRegionWithLevel();

    /**
     * 查询单个Level
     */
    List<SaasRegionBusinessDto> queryRegionByLevel(XyySaasRegionParamsDto districtDto);

    /**
     * 查询多个ParentCodeList
     */
    List<SaasRegionBusinessDto> queryRegionByParentCodeList(XyySaasRegionParamsDto districtDto);

    /**
     * 根据条件查询
     */
    List<SaasRegionBusinessDto> queryRegion(XyySaasRegionParamsDto districtDto);

}

