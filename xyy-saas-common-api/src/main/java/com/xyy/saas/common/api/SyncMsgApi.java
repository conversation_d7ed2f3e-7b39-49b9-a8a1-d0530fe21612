package com.xyy.saas.common.api;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.SyncMsgDto;

/**
 * @author: pengfei
 * @date: 2019/07/01 17:04
 */
public interface SyncMsgApi {

    /**
     * 插入同步信息
     * @param syncMsgDtos
     * @return
     */
	Map<String, Object> insertSyncMsg(List<SyncMsgDto> syncMsgDtos);

    /**
     * 查询同步信息
     * @param syncMsgDto
     * @return
     */
	PageInfo<SyncMsgDto> selectSyncMsgList(PageInfo pageInfo, SyncMsgDto syncMsgDto);
    
    /**
     * 更改同步信息
     * @param syncMsgDto
     * @return
     */
    Map<String, Object> updateSyncMsgList(SyncMsgDto syncMsgDto);
    
    /**
     * 推送MQ
     * @param syncMsgDto
     * @return
     */
    Map<String, Object> sendSyncMsg2MQ(SyncMsgDto syncMsgDto);

}
