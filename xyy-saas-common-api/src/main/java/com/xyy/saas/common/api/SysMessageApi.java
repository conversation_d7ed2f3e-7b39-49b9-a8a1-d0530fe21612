package com.xyy.saas.common.api;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.dto.SysMessageDto;
import com.xyy.saas.common.dto.SysMessageParamDto;

public interface SysMessageApi {
    /**
     * 按分页查询通知消息
     * @param pageInfo
     * @param paramDto
     * @return
     */
    PageInfo queryNotifyMessages(PageInfo pageInfo, SysMessageParamDto paramDto);

    int saveNotifyMessage(SysMessageDto message);

    int changeNotifyMessageStatus(String msgId, int newStatus);
}
