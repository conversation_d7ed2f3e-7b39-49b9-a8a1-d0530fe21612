package com.xyy.saas.emule.Constants;

/**
 * 返回结果状态枚举类
 *
 * <AUTHOR>
 */
public enum ResultCodeEnum {
    SUCCESS(0, "success"),
    ERROR(1, "error"),
    NO_FILE(17001, "参数file不能为空"),
    NO_NULL_BEAN(17001, "参数对象不能为空"),
    NO_FILE_TWENTY(17002, "上传失败，导入最大不能超过5M!"),
    FAIL_FILE(17003, "上传文件失败"),
    NO_BUSINESS_TYPE(17004, "业务类别不能为空"),
    NO_FILE_NAME(17005, "文件名称不能为空"),
    NO_FILE_PATH(17006, "文件路径不能为空"),
    NO_TASK_TYPE(17007, "业务分类不能为空"),
    NO_PARAM_INFO(17008, "查询参数不能为空"),
    NO_HEAD_INFO(17009, "头信息不能为空"),
    NO_HEAD_COLUMN(17010, "属性列不能为空"),
    NO_FILE_NAME_REPEAT(170011, "文件名称不能重复"),
    NO_TASK_ID(17012, "任务ID不能为空"),
    NO_TASK_STATUS(17013, "任务状态不能为空"),
    FAIL_FORMAT_FILE(17014, "上传文件格式不正确,请重新下载模板"),
    NO_FAIL_SIZE(17015, "Excel数据不能为空"),
    SAME_PRODUCT_MATCH(17016, "已创建商品匹配任务"),
    CHECK_SIZE_MORE(17017, "每次最多导出50条"),
    PARAM_SIZE_MORE(17018, "选择条数过多,请重新选择"),
    NO_FORM_NAME(17019, "表单名称不能为空"),
    NO_FORM_FIELD(17020, "表单字段不能为空"),
    NO_PARAM_ID(17021, "参数ID不能为空"),
    NO_QUERY_DATA(17022, "未查询到对应数据"),
    NO_GREATER_TWENTY_THOUSAND_DATA(17023, "导入数据最多仅支持20000条，剩余数据请进行分批导入"),
    NO_INFORMATION_MATCHING(17024, "文件头信息与模板头信息不符，请重新检查"),
    NO_SERVICE_OPEN(17025, "服务未开通"),
    NO_TEMPLATE_OUTDATED(17026, "模板已过期，请重新创建模板并上传数据"),
    FILE_SIZE_OVER(17027, "上传失败，导入最大不能超过%sM!");
    private int code;
    private String msg;

    ResultCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
