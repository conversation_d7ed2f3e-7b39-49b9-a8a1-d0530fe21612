package com.xyy.saas.emule.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.xyy.emule.api.InitSubTaskApi;
import com.xyy.emule.api.InitTaskApi;
import com.xyy.emule.dto.ExcelDtoV2;
import com.xyy.emule.dto.InitTaskDto;
import com.xyy.emule.dto.OperModuleDto;
import com.xyy.saas.base.AbstractCommonController;
import com.xyy.saas.common.api.FileUploadApi;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.emule.Constants.BusinessLineCodeEnum;
import com.xyy.saas.emule.Constants.ResultCodeEnum;
import com.xyy.saas.emule.ServiceImplEnum;
import com.xyy.saas.emule.model.InitTaskVo;
import com.xyy.saas.emule.service.QueryImportIdService;
import com.xyy.saas.emule.utils.*;
import com.xyy.saas.util.ExcelUtil;
import com.xyy.saas.util.SpringUtil;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.util.StringUtil;
import com.xyy.util.UUIDUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.ClassUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/cloud/emule")
@Api(value = "emule", description = "期初服务")
public class InitTaskApiController extends AbstractCommonController {

    private static final Logger logger = LogManager.getLogger(InitTaskApiController.class);

    private static String[] fieldNames = new String[]{"column1", "column2", "column3", "column4", "column5", "column6", "column7",
            "column8", "column9", "column10", "column11", "column12", "column13", "column14", "column15", "column16", "column17",
            "column18", "column19", "column20", "column21", "column22", "column23", "column24", "column25", "column26", "column27",
            "column28", "column29", "column30", "column31", "column32", "column33", "column34", "column35", "column36", "column37",
            "column38", "column39", "column40", "column41", "column42", "column43", "column44", "column45", "column46", "column47",
            "column48", "column49", "column50", "column51", "column52", "column53", "column54", "column55", "column56", "column57",
            "column58", "column59", "column60", "column61", "column62", "column63", "column64", "column65", "column66", "column67",
            "column68", "column69", "column70", "column71", "column72", "column73", "column74", "column75", "column76", "column77",
            "column78", "column79", "column80", "errorColumn"};

    @Reference(version = "0.0.1")
    private InitTaskApi initTaskApi;

    @Reference(version = "0.0.1")
    private FileUploadApi fileUploadApi;

    @Reference(version = "0.0.1")
    private InitSubTaskApi initSubTaskApi;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Resource
    private DfsUploadUtil dfsUploadUtil;

    @Value("${upload.file.max.size:10}")
    private Long fileMaxSize;

    @Autowired
    private Map<String,QueryImportIdService> queryImportIdServiceMap;

    public static final String TEMPLATE = "template/";

    public static final String organSign = "XYY00000001";

    //医保云目录单独处理
    public static final String MEDICAL = "nationalCloudImport";

    @ApiOperation(value = "上传文件", notes = "上传文件", response = Boolean.class, tags = {"InitTaskVo",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> uploadFile(@RequestParam(value = "file", required = true) MultipartFile file,
                                               @RequestParam(value = "allowEmptyFile", defaultValue = "false") Boolean allowEmptyFile) {
        Long fileMax = 5 * 1024 * 1024l;
        if (file == null) {
            return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_FILE.getCode(), ResultCodeEnum.NO_FILE.getMsg(), false), HttpStatus.OK);
        }
        if (file.getSize() > fileMax) {
            return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_FILE_TWENTY.getCode(), ResultCodeEnum.NO_FILE_TWENTY.getMsg(), false), HttpStatus.OK);
        }
        logger.info("上传文件开始");
        Workbook wb;
        String filename = file.getOriginalFilename();
        try {
            InputStream inputStream = file.getInputStream();
            //这里判断会有问题，如果强制改excel扩展名就会出现问题，后面的导出异常信息就是强制更换了扩展名，导致异常信息文件再次上传时就会报错
            if (filename.matches("^.+\\.(?i)(xlsx)$")) {
                logger.info("上传文件开始--xlsx");
                wb = new XSSFWorkbook(inputStream);
            } else {
                logger.info("上传文件开始--xls");
                wb = new HSSFWorkbook(inputStream);
            }
            Sheet sheet = wb.getSheetAt(0);
            if (sheet == null) {
                return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_FAIL_SIZE.getCode(), ResultCodeEnum.NO_FAIL_SIZE.getMsg(), false), HttpStatus.OK);
            }
            // 行数
            int rows = sheet.getLastRowNum();
            logger.info("sheet.getLastRowNum:::::::::::::::::" + rows);
            if (!allowEmptyFile && rows == 0) {
                return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_FAIL_SIZE.getCode(), ResultCodeEnum.NO_FAIL_SIZE.getMsg(), false), HttpStatus.OK);
            }
            String filePath = dfsUploadUtil.saveFile(file);
            logger.info("调用common服务前autoDownloadWithToken,返回url:" + filePath);
            if (StringUtils.isEmpty(filePath)) {
                return new ResponseEntity(new ResultVO(ResultCodeEnum.FAIL_FILE.getCode(), ResultCodeEnum.FAIL_FILE.getMsg(), false), HttpStatus.OK);
            }
            Map<String, Object> map = Maps.newHashMap();
            map.put("filePath", filePath);
            map.put("fileName", file.getOriginalFilename());
            return new ResponseEntity(new ResultVO(ResultCodeEnum.SUCCESS.getCode(), ResultCodeEnum.SUCCESS.getMsg(), map), HttpStatus.OK);
        } catch (Exception e) {
            logger.error("读取error:" + e);
            return new ResponseEntity(new ResultVO(ResultCodeEnum.FAIL_FORMAT_FILE.getCode(), ResultCodeEnum.FAIL_FORMAT_FILE.getMsg(), false), HttpStatus.OK);
        }
    }

    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/deviceUpload", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> deviceUploadFile(@RequestParam(value = "file", required = true) MultipartFile file) {
        Long fileMax = 5 * 1024 * 1024l;
        if (file == null) {
            return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_FILE.getCode(), ResultCodeEnum.NO_FILE.getMsg(), false), HttpStatus.OK);
        }
        if (file.getSize() > fileMax) {
            return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_FILE_TWENTY.getCode(), ResultCodeEnum.NO_FILE_TWENTY.getMsg(), false), HttpStatus.OK);
        }
        logger.info("上传文件开始");
        Workbook wb;
        String filename = file.getOriginalFilename();
        try {
            InputStream inputStream = file.getInputStream();
            //这里判断会有问题，如果强制改excel扩展名就会出现问题，后面的导出异常信息就是强制更换了扩展名，导致异常信息文件再次上传时就会报错
            if (filename.matches("^.+\\.(?i)(xlsx)$")) {
                logger.info("上传文件开始--xlsx");
                wb = new XSSFWorkbook(inputStream);
            } else {
                logger.info("上传文件开始--xls");
                wb = new HSSFWorkbook(inputStream);
            }
            Sheet sheet = wb.getSheetAt(0);
            if (sheet == null) {
                return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_FAIL_SIZE.getCode(), ResultCodeEnum.NO_FAIL_SIZE.getMsg(), false), HttpStatus.OK);
            }
            // 检查列数
            Row headerRow = sheet.getRow(0);
            if (headerRow == null || headerRow.getLastCellNum() != 23) {
                return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_INFORMATION_MATCHING.getCode(), ResultCodeEnum.NO_INFORMATION_MATCHING.getMsg(), false), HttpStatus.OK);
            }

            // 行数
            int rows = sheet.getLastRowNum();
            logger.info("sheet.getLastRowNum:::::::::::::::::" + rows);
            if (rows == 0) {
                return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_FAIL_SIZE.getCode(), ResultCodeEnum.NO_FAIL_SIZE.getMsg(), false), HttpStatus.OK);
            }
            String filePath = dfsUploadUtil.saveFile(file);
            logger.info("调用common服务前autoDownloadWithToken,返回url:" + filePath);
            if (StringUtils.isEmpty(filePath)) {
                return new ResponseEntity(new ResultVO(ResultCodeEnum.FAIL_FILE.getCode(), ResultCodeEnum.FAIL_FILE.getMsg(), false), HttpStatus.OK);
            }
            Map<String, Object> map = Maps.newHashMap();
            map.put("filePath", filePath);
            map.put("fileName", file.getOriginalFilename());
            return new ResponseEntity(new ResultVO(ResultCodeEnum.SUCCESS.getCode(), ResultCodeEnum.SUCCESS.getMsg(), map), HttpStatus.OK);
        } catch (Exception e) {
            logger.error("读取error:" + e);
            return new ResponseEntity(new ResultVO(ResultCodeEnum.FAIL_FORMAT_FILE.getCode(), ResultCodeEnum.FAIL_FORMAT_FILE.getMsg(), false), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "上传文件", notes = "上传文件", response = Boolean.class, tags = {"InitTaskVo",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/upload/medical", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> uploadFileMedical(@RequestParam(value = "file", required = true) MultipartFile file) {
        if (file == null) {
            return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_FILE.getCode(), ResultCodeEnum.NO_FILE.getMsg(), false), HttpStatus.OK);
        }
        Long fileSize = file.getSize();
        logger.info("上传文件大小为:{}" + fileSize);
        if (fileSize > fileMaxSize*1024*1024) {
            return new ResponseEntity(new ResultVO(ResultCodeEnum.FILE_SIZE_OVER.getCode(), String.format(ResultCodeEnum.FILE_SIZE_OVER.getMsg(),fileMaxSize), false), HttpStatus.OK);
        }
        try {
            String filePath = dfsUploadUtil.saveFile(file);
            logger.info("调用common服务前autoDownloadWithToken,返回url:" + filePath);
            if (StringUtils.isEmpty(filePath)) {
                return new ResponseEntity(new ResultVO(ResultCodeEnum.FAIL_FILE.getCode(), ResultCodeEnum.FAIL_FILE.getMsg(), false), HttpStatus.OK);
            }
            Map<String, Object> map = Maps.newHashMap();
            map.put("filePath", filePath);
            map.put("fileName", file.getOriginalFilename());
            return new ResponseEntity(new ResultVO(ResultCodeEnum.SUCCESS.getCode(), ResultCodeEnum.SUCCESS.getMsg(), map), HttpStatus.OK);
        } catch (Exception e) {
            logger.error("读取error:" + e);
            return new ResponseEntity(new ResultVO(ResultCodeEnum.FAIL_FORMAT_FILE.getCode(), ResultCodeEnum.FAIL_FORMAT_FILE.getMsg(), false), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "创建任务", notes = "创建任务", response = Boolean.class, tags = {"InitTaskVo",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> saveInitTask(@RequestBody InitTaskVo initTaskVo) {
        String employeeId = getLoginUserId().toString();
        Byte organSignType = 6;//云平台
        logger.info("业务线:" + BusinessLineCodeEnum.CLOUD_PLAT.getCode() + ",创建任务InitTaskVo为:" + JSONObject.toJSON(initTaskVo));
        if (initTaskVo == null) {
            return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_NULL_BEAN.getCode(), ResultCodeEnum.NO_NULL_BEAN.getMsg(), false), HttpStatus.OK);
        }
        if (StringUtils.isEmpty(initTaskVo.getBusinessType())) {
            return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_BUSINESS_TYPE.getCode(), ResultCodeEnum.NO_BUSINESS_TYPE.getMsg(), false), HttpStatus.OK);
        }
        // 1:上传，2:下载，3:匹配
        Integer taskType = initTaskVo.getTaskType();
        if (taskType == null) {
            return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_TASK_TYPE.getCode(), ResultCodeEnum.NO_TASK_TYPE.getMsg(), false), HttpStatus.OK);
        }
        if (taskType == 1) {
            if (StringUtils.isEmpty(initTaskVo.getFileName())) {
                return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_FILE_NAME.getCode(), ResultCodeEnum.NO_FILE_NAME.getMsg(), false), HttpStatus.OK);
            }
            if (StringUtils.isEmpty(initTaskVo.getFilePath())) {
                return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_FILE_PATH.getCode(), ResultCodeEnum.NO_FILE_PATH.getMsg(), false), HttpStatus.OK);
            }
        }
        if (taskType == 2) {
            String paramInfo = initTaskVo.getParamInfo();
            if (StringUtils.isEmpty(paramInfo)) {
                return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_PARAM_INFO.getCode(), ResultCodeEnum.NO_PARAM_INFO.getMsg(), false), HttpStatus.OK);
            }
            if (StringUtils.isEmpty(initTaskVo.getHeadInfo())) {
                return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_HEAD_INFO.getCode(), ResultCodeEnum.NO_HEAD_INFO.getMsg(), false), HttpStatus.OK);
            }
            if (StringUtils.isEmpty(initTaskVo.getHeadColumn())) {
                return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_HEAD_COLUMN.getCode(), ResultCodeEnum.NO_HEAD_COLUMN.getMsg(), false), HttpStatus.OK);
            }
            if (StringUtils.isEmpty(initTaskVo.getFileName())) {
                return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_FILE_NAME.getCode(), ResultCodeEnum.NO_FILE_NAME.getMsg(), false), HttpStatus.OK);
            }

            boolean check= checkTaskParamInfo(paramInfo);
            if(!check){
                return new ResponseEntity(new ResultVO(ResultCodeEnum.CHECK_SIZE_MORE.getCode(), ResultCodeEnum.CHECK_SIZE_MORE.getMsg(), false), HttpStatus.OK);
            }
            if (paramInfo.length() > 2048) {
                return new ResponseEntity(new ResultVO(ResultCodeEnum.PARAM_SIZE_MORE.getCode(), ResultCodeEnum.PARAM_SIZE_MORE.getMsg(), false), HttpStatus.OK);
            }
        }
        long millis = System.currentTimeMillis();
        InitTaskDto initTaskDto = new InitTaskDto();
        Boolean backId = initTaskVo.getBackId() == null ? false : initTaskVo.getBackId();
        BeanUtils.copyProperties(initTaskVo, initTaskDto);
        initTaskDto.setTaskStatus(1);
        initTaskDto.setOrganSignType(organSignType);
        initTaskDto.setOrganSign(organSign);
        initTaskDto.setCreateUser(employeeId);
        initTaskDto.setCreateTime(millis);
        initTaskDto.setUpdateTime(millis);
        initTaskDto.setBusinessLine(BusinessLineCodeEnum.CLOUD_PLAT.getCode());
        if (initTaskVo.getTaskType()== 1 && backId) {
            Long taskId = initTaskApi.insertCustomTask(initTaskDto);
            Map<String, Object> map = Maps.newHashMap();
            map.put("id", taskId);
            if (taskId != null && taskId > 0) {
                return new ResponseEntity(new ResultVO(ResultCodeEnum.SUCCESS.getCode(), ResultCodeEnum.SUCCESS.getMsg(), map), HttpStatus.OK);
            }
        } else {
            boolean flag = initTaskApi.insertSelective(initTaskDto);
            if (flag) {
                return new ResponseEntity(new ResultVO(ResultCodeEnum.SUCCESS.getCode(), ResultCodeEnum.SUCCESS.getMsg(), true), HttpStatus.OK);
            }
        }
        return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_SERVICE_OPEN.getCode(), ResultCodeEnum.NO_SERVICE_OPEN.getMsg(), false), HttpStatus.OK);
    }

    @ApiOperation(value = "查询导入文件列表", notes = "查询导入文件列表", response = Boolean.class, tags = {"InitTaskVo",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/import/query", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryImportTask(@RequestBody InitTaskVo initTaskVo) {
        String businessType = initTaskVo.getBusinessType();
        if (StringUtils.isEmpty(businessType)) {
            return new ResponseEntity(new ResultVO(ResultCodeEnum.NO_BUSINESS_TYPE.getCode(), ResultCodeEnum.NO_BUSINESS_TYPE.getMsg(), false), HttpStatus.OK);
        }
        InitTaskDto initTaskDto = new InitTaskDto();
        initTaskDto.setPageSize(initTaskVo.getPageSize() == null ? 10 : initTaskVo.getPageSize());
        initTaskDto.setPageNum(initTaskVo.getPageNum() == null ? 1 : initTaskVo.getPageNum());
        initTaskDto.setOrganSign(organSign);
        initTaskDto.setTaskType(1);
        initTaskDto.setBusinessLine(BusinessLineCodeEnum.CLOUD_PLAT.getCode());
        initTaskDto.setBusinessType(businessType);
        logger.info("业务线:" + BusinessLineCodeEnum.CLOUD_PLAT.getCode() + ",查询导入文件列表initTaskDto为:" + JSONObject.toJSON(initTaskDto));
        PageInfo<InitTaskDto> dtoPageInfo = initTaskApi.queryInitTaskPager(initTaskDto);
        return new ResponseEntity(ResultVO.createSuccess(dtoPageInfo), HttpStatus.OK);
    }

    @ApiOperation(value = "查询下载文件列表", notes = "查询下载文件列表", response = Boolean.class, tags = {"InitTaskVo",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/export/query", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> queryExportTask(@RequestBody InitTaskVo initTaskVo) {
        InitTaskDto initTaskDto = new InitTaskDto();
        if (!StringUtils.isEmpty(initTaskVo.getBusinessType())) {
            initTaskDto.setBusinessType(initTaskVo.getBusinessType());
        }
        if (!StringUtils.isEmpty(initTaskVo.getFileName())) {
            initTaskDto.setFileName(initTaskVo.getFileName());
        }
        if (initTaskVo.getTaskStatus() != null) {
            initTaskDto.setTaskStatus(initTaskVo.getTaskStatus());
        }
        if (initTaskVo.getBeginCreateTime() != null) {
            initTaskDto.setBeginCreateTime(initTaskVo.getBeginCreateTime());
        }
        if (initTaskVo.getEndCreateTime() != null) {
            initTaskDto.setEndCreateTime(initTaskVo.getEndCreateTime());
        }
        initTaskDto.setPageNum(initTaskVo.getPageNum() == null ? 1 : initTaskVo.getPageNum());
        initTaskDto.setPageSize(initTaskVo.getPageSize() == null ? 10 : initTaskVo.getPageSize());
        initTaskDto.setOrganSign(organSign);
        initTaskDto.setTaskType(2);
        initTaskDto.setBusinessLine(BusinessLineCodeEnum.CLOUD_PLAT.getCode());
        initTaskDto.setCode(initTaskVo.getCode());
        logger.info("业务线:" + BusinessLineCodeEnum.CLOUD_PLAT.getCode() + ",查询下载文件列表InitTaskVo为:" + JSONObject.toJSON(initTaskDto));
        PageInfo<InitTaskDto> dtoPageInfo = initTaskApi.queryInitTaskPager(initTaskDto);
        return new ResponseEntity(ResultVO.createSuccess(dtoPageInfo), HttpStatus.OK);
    }


    @ApiOperation(value = "下载源文件", notes = "下载源文件", response = Boolean.class, tags = {"InitTaskVo",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/download/source/file", method = RequestMethod.POST)
    public void downloadFile(HttpServletResponse response, HttpServletRequest request, @RequestBody InitTaskVo initTaskVo) {
        try {
            logger.info("下载源文件InitTaskVo为:" + JSONObject.toJSON(initTaskVo));
            String filePath = initTaskVo.getFilePath();
            String fileName = initTaskVo.getFileName();
            //添加token
            String fileUrl = fileUploadApi.autoDownloadWithToken(filePath);
            URL url = new URL(fileUrl);
            URLConnection conn = url.openConnection();
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("conent-type", "application/octet-stream");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            OutputStream os = response.getOutputStream();
            BufferedOutputStream bos = new BufferedOutputStream(os);
            InputStream is = conn.getInputStream();
            BufferedInputStream bis = new BufferedInputStream(is);
            int length = 0;
            byte[] temp = new byte[1 * 1024 * 10];
            while ((length = bis.read(temp)) != -1) {
                bos.write(temp, 0, length);
            }
            bos.flush();
            bis.close();
            bos.close();
            is.close();
        } catch (Exception e) {
            logger.error("下载源文件error:" + e.getMessage());
        }
    }

    @ApiOperation(value = "导出异常信息", notes = "导出异常信息", response = Boolean.class, tags = {"InitTaskVo",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/export/errorInfo", method = RequestMethod.POST)
    public void exportRecordExcel(HttpServletResponse response, HttpServletRequest request,@RequestBody InitTaskVo queryVo) {
        logger.info("导出异常信息InitTaskVo为:" + JSONObject.toJSON(queryVo));
        InitTaskDto initTaskDto = new InitTaskDto();
        BeanUtils.copyProperties(queryVo, initTaskDto);
        initTaskDto.setOrganSign(organSign);
        InitTaskDto taskDto = initTaskApi.selectByPrimaryKey(initTaskDto);
        String oldHeader = taskDto.getHeadInfo();
        String[] headers = oldHeader.split(",");
        String[] columnNames = Arrays.copyOf(fieldNames, headers.length);
        columnNames[headers.length - 1] = fieldNames[80];
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String extFileName = "异常数据" + df.format(new Date()) + ".xls";
        String sheetName = "异常数据";
        try {
            if(Objects.equals(taskDto.getBusinessType(),MEDICAL)){
                this.downExportDataAndCreateExcel(taskDto,response, sheetName, headers, columnNames, false,"yyyy-MM-dd");
            }else {
                List<ExcelDtoV2> excelDtoList = initSubTaskApi.selectSubTaskDtoByTaskId(taskDto);
                ExportExcelUtil.createExcelWithDatePatternV2(response, request, extFileName, sheetName, headers, columnNames, excelDtoList, false, "yyyy-MM-dd");
            }
            logger.info("期初服务:导出异常信息成功。");
        } catch (Exception e) {
            logger.error("期初服务:导出异常信息error:{}", e);
        }
    }

    @ApiOperation(value = "下载中心操作模块", notes = "下载中心操作模块", response = ResultVO.class, tags = {"InitTaskVo"})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "查询成功", response = ResultVO.class)})
    @ResponseBody
    @RequestMapping(value = "/export/module", method = RequestMethod.POST)
    public ResponseEntity<ResultVO> selectStandardVersionIdsList() {
        InitTaskDto dto = new InitTaskDto();
        dto.setOrganSignType(6);
        List<OperModuleDto> moduleDtos = initTaskApi.queryNewOperPages(dto);
        return new ResponseEntity(ResultVO.createSuccess(moduleDtos), HttpStatus.OK);
    }


    @ApiOperation(value = "下载execl模板", notes = "下载execl模板", response = Boolean.class, tags = {"InitTaskVo",})
    @ApiResponses(value = {@ApiResponse(code = 200, message = "操作成功", response = Boolean.class)})
    @RequestMapping(value = "/download/execl/template", method = RequestMethod.POST)
    public void downloadExeclTemplate(HttpServletResponse response, @RequestBody InitTaskVo initTaskVo) {
        try {
            logger.info("下载execl模板InitTaskVo为:" + JSONObject.toJSON(initTaskVo));
            logger.info("storehouseYn:"+initTaskVo.getStorehouseYn());
            if (initTaskVo != null && initTaskVo.getBusinessType() != null) {
                StringBuffer sb = new StringBuffer();
                sb.append(initTaskVo.getBusinessType());
                String storehouseYn = initTaskVo.getStorehouseYn();
                if ("inventoryBalanceImportM".equals(initTaskVo.getBusinessType()) && !StringUtil.isEmpty(storehouseYn)){
                    sb.append("-").append(storehouseYn);
                }
                sb.append(".xls");
                String excelName = sb.toString();
                String newName = URLEncoder.encode(excelName, "UTF-8");
                response.setHeader("conent-type", "application/octet-stream");
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment; filename=" + newName);
                OutputStream os = response.getOutputStream();
                BufferedOutputStream bos = new BufferedOutputStream(os);
                InputStream is = ClassUtils.class.getClassLoader().getResourceAsStream("exceltemplet/" + excelName);
                BufferedInputStream bis = new BufferedInputStream(is);
                int length = 0;
                byte[] temp = new byte[1 * 1024 * 10];
                while ((length = bis.read(temp)) != -1) {
                    bos.write(temp, 0, length);
                }
                bos.flush();
                bis.close();
                bos.close();
                is.close();
            }
        } catch (IOException e) {
            logger.error("模板异常信息error:{}", e);
        }
    }

    @RequestMapping("/queryImportInfo")
    @ResponseBody
    public ResultVO<InitTaskDto> queryImportInfo(@RequestBody String param){
        if (StringUtils.isEmpty(param)){
            logger.error("QualityModifyController#queryImportInfo 参数不合法 param:"+param);
            return ResultVO.createError("参数为空");
        }
        JSONObject jo = JSON.parseObject(param);
        String businessType = jo.getString("businessType");
        String model = jo.getString("model");
        if (StringUtils.isEmpty(businessType) || StringUtils.isEmpty(model)){
            logger.error("【查询导入信息】参数不合法 businessType:{}"+businessType+"model:{}"+model);
            return ResultVO.createError("参数不合法");
        }

        QueryImportIdService queryImportIdService = queryImportIdServiceMap.get(ServiceImplEnum.findByBusinessType(businessType).getQueryImportInfoService());
        //QueryImportIdService queryImportIdService = SpringUtil.getBean(ServiceImplEnum.findByBusinessType(businessType).getService());
        //QueryImportIdService queryImportIdService = queryImportMap.get(businessType);
        if (null == queryImportIdService){
            logger.error("【查询导入信息】 queryImportIdService为空 businessType"+businessType);
            return ResultVO.createError("未配置的业务类型");
        }

        //拓展字段,按需使用
        Map<String, Object> extend = null;
        //想要其他的机构员工参数继承AbstractCommonController即可
        Long taskId = queryImportIdService.getId(String.valueOf(getCurrentUserId()),model,extend);

        if (null == taskId){
            return ResultVO.createSuccess(null);
        }else {
            InitTaskDto initTaskDto = new InitTaskDto();
            initTaskDto.setId(taskId);
            ResultVO<InitTaskDto> resultVO = queryOneTask(initTaskDto);
            //如果执行完成，删除key
            if(resultVO.getResult()!=null && resultVO.getResult().getTaskStatus() == 7){
                queryImportIdService.deleteImportTaskIdKey(String.valueOf(getCurrentUserId()),model,extend);
            }
            return resultVO;
        }
    }


    /**
     * 根据条件导出质量变更 连锁
     */
    @RequestMapping("/oneTask")
    @ResponseBody
    public ResultVO<InitTaskDto> queryOneTask(@RequestBody InitTaskDto initTaskDto){
        logger.info("QualityModifyController#queryOneTask id:{}"+initTaskDto.getId());
        InitTaskDto exportExcelDto = initTaskApi.selectByPrimaryKey(initTaskDto);
//        List<Integer> createUserIds = new ArrayList<>();
//        createUserIds.add(Integer.valueOf(exportExcelDto.getCreateUser()));
//        Map<Integer, EmployeeDto> employeeMap = new HashMap<>();
//        if (!CollectionUtils.isEmpty(createUserIds)){
//            List<EmployeeDto> employeeDtos = employeeApi.queryAllEmployeeByIds(createUserIds);
//            if (!CollectionUtils.isEmpty(employeeDtos)){
//                employeeMap = employeeDtos.stream().collect(Collectors.toMap(EmployeeDto::getId, a -> a, (k1, k2) -> k1));
//            }
//        }
//        if (null != exportExcelDto.getCreateUser()){
//            EmployeeDto employeeDto = employeeMap.get(Integer.parseInt(exportExcelDto.getCreateUser()));
//            if (null != employeeDto){
//                exportExcelDto.setCreateUser(employeeDto.getName());
//            }
//        }
        return ResultVO.createSuccess(exportExcelDto);
    }

    private static String generateStr() {
        String uuid = UUIDUtil.newUUID();
        long time = new Date().getTime();
        String str = time + uuid.lastIndexOf(8) + ".xls";
        return str;
    }

    private boolean checkTaskParamInfo(String paramInfo) {
        try {
            if (!StringUtils.isEmpty(paramInfo)) {
                JSONObject paramJson = JSONObject.parseObject(paramInfo);
                String ids = paramJson.getString("ids");
                String idList = paramJson.getString("idList");
                String billIdList = paramJson.getString("billIdList");
                String uuids = paramJson.getString("uuids");
                if (!StringUtils.isEmpty(ids)) {
                    String[] split = ids.split(",");
                    if (split != null && split.length > 50) {
                        return false;
                    }
                }
                if (!StringUtils.isEmpty(idList)) {
                    String[] split = idList.split(",");
                    if (split != null && split.length > 50) {
                        return false;
                    }
                }
                if (!StringUtils.isEmpty(billIdList)) {
                    String[] split = billIdList.split(",");
                    if (split != null && split.length > 50) {
                        return false;
                    }
                }
                if (!StringUtils.isEmpty(uuids)) {
                    String[] split = uuids.split(",");
                    if (split != null && split.length > 50) {
                        return false;
                    }
                }
            }
        } catch (Exception e) {
            return true;
        }
        return true;
    }


    private void downExportDataAndCreateExcel(InitTaskDto initTaskDto, HttpServletResponse response, String sheetName, String[] headers, String[] fieldNames, boolean flag, String datePattern) throws Exception {
        //数据分页大小 / SXSSFWorkbook窗口大小
        Integer exportBatchSize = 100;
        SXSSFWorkbook work = new SXSSFWorkbook(exportBatchSize);
        //存放对应小数位数的CellStyle
        Map<Integer, CellStyle> numberCellStyleMap = new HashMap<>();
        CellStyle integerCellStyle = work.createCellStyle();
        integerCellStyle.setDataFormat(work.createDataFormat().getFormat("#0"));
        numberCellStyleMap.put(0, integerCellStyle);
        OutputStream out;
        try {
            out = response.getOutputStream();
            //创建工作表
            Sheet sheet = work.createSheet(sheetName);
            //显示标题
            Row title_row = sheet.createRow(0);
            title_row.setHeight((short) (40 * 20));
            Row header_row = sheet.createRow(0);
            //创建单元格的 显示样式
            CellStyle style = work.createCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER); //水平方向上的对其方式
            style.setVerticalAlignment(VerticalAlignment.CENTER);  //垂直方向上的对其方式
            int headcell_index = 0;
            if (flag) {//有序号
                sheet.setColumnWidth(headcell_index, 1600);
                Cell headcell = header_row.createCell(headcell_index);
                //应用样式到  单元格上
                headcell.setCellStyle(style);
                headcell.setCellValue("序号");
                headcell_index = 1;
            }
            for (int i = 0; i < headers.length; i++) {
                sheet.setColumnWidth(headcell_index, 5000);
                Cell cell = header_row.createCell(headcell_index);
                cell.setCellStyle(style);
                cell.setCellValue(headers[i]);
                headcell_index++;
            }

            int page = 1;
            int size = 1000;
            Integer totalCount = initTaskDto.getErrorCount();
            int pages = totalCount % size == 0 ? totalCount / size : totalCount / size + 1;
            logger.info("任务id:"+initTaskDto.getId()+"错误总数为:"+initTaskDto.getErrorCount()+"总页数为:"+pages);
            while (page <= pages) {
                List<ExcelDtoV2> resultList = initSubTaskApi.selectSubTaskListPage(initTaskDto, page, size);
                logger.info("任务id:"+initTaskDto.getId()+"第几次查询:"+page+"返回数量为:"+resultList.size());
                if (!CollectionUtils.isEmpty(resultList)) {
                    List<JSONObject> jsonObjectList = resultList.stream().map(obi -> JSONObject.parseObject(JSONObject.toJSONString(obi))).collect(Collectors.toList());
                    ExcelUtil.writeDataToWorkbook(work, sheet, fieldNames, flag, datePattern, jsonObjectList, page, size, numberCellStyleMap);
                }
                page += 1;
            }
            work.write(out);
            out.flush();
            out.close();
        } finally {
            // 从磁盘上释放临时文件
            work.dispose();
        }
    }

}
