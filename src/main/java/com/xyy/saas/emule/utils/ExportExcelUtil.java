package com.xyy.saas.emule.utils;


import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.logging.log4j.LogManager;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFDataFormat;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Method;
import java.net.SocketException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by zhengding on 2018/4/15.
 */
public class ExportExcelUtil {


    private static final org.apache.logging.log4j.Logger logger = LogManager.getLogger(ExportExcelUtil.class);


    /**
     * Excel文档的构成
     *
     * 在工作簿(WorkBook)里面包含了工作表(Sheet) 在工作表里面包含了行(Row) 行里面包含了单元格(Cell)
     *
     *
     * 创建一个工作簿的基本步骤
     *
     * 第一步 创建一个 工作簿 第二步 创建一个 工作表 第三步 创建一行 第四步 创建单元格 第五步 写数据 第六步
     * 将内存中生成的workbook写到文件中 然后释放资源
     *
     */
    public static XSSFWorkbook createExcelOfUsers(String[] headers,String title,String sheetName,List<String[]> dataList, XSSFWorkbook old) throws Exception{
        XSSFWorkbook work = old == null ? new XSSFWorkbook() : old;

        //创建工作表
        Sheet sheet = work.createSheet(sheetName);

        //显示标题
        Row title_row = sheet.createRow(0);
        title_row.setHeight((short)(40*20));

//        Cell title_cell = title_row.createCell(0);

        Row header_row = sheet.createRow(0);
//        header_row.setHeight((short)(20*24));

        //创建单元格的 显示样式
        CellStyle style = work.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER); //水平方向上的对其方式
        style.setVerticalAlignment(VerticalAlignment.CENTER);  //垂直方向上的对其方式


//        title_cell.setCellStyle(style);
//        title_cell.setCellValue(title);

        //sheet.addMergedRegion(new CellRangeAddress(0,0,0,0));

        for(int i=0;i<headers.length;i++){
            //设置列宽   基数为256
            sheet.setColumnWidth(i, 30*256);
            Cell cell = header_row.createCell(i);
            //应用样式到  单元格上
            cell.setCellStyle(style);
            cell.setCellValue(headers[i]);
        }

        //插入需导出的数据
        for(int i=0;i<dataList.size();i++){
            Row row = sheet.createRow(i+1);
            row.setHeight((short)(20*20)); //设置行高  基数为20
            String[] data1 = dataList.get(i);
            for(int j=0;j<data1.length;j++){
//                System.out.println(data1[j].toString());
                Cell cell = row.createCell(j);
                cell.setCellValue(data1[j]);
            }
        }

//        FileOutputStream fileOut = new FileOutputStream("/Users/<USER>/Downloads/excel201804151.xlsx");
//        wb.write(fileOut);
//        fileOut.close();
        return work;
    }
    /**
     * 导出excel
     * @param response
     * @param request
     * @param extfilename 文件名
     * @param sheetName   sheet页名
     * @param headers     表头集合
     * @param fieldNames  表头对应的实体属性集合
     * @param dataList    数据
     * @param flag        第一列是否为序号列
     * @throws IOException
     */
    public static void createExcel(HttpServletResponse response, HttpServletRequest request, String extfilename, String sheetName, String[] headers, String[] fieldNames, List dataList, boolean flag) throws IOException {
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            XSSFWorkbook work = new XSSFWorkbook();
            //创建工作表
            Sheet sheet = work.createSheet(sheetName);
            //显示标题
            Row title_row = sheet.createRow(0);
            title_row.setHeight((short) (40 * 20));
            Row header_row = sheet.createRow(0);
            //创建单元格的 显示样式
            CellStyle style = work.createCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER); //水平方向上的对其方式
            style.setVerticalAlignment(VerticalAlignment.CENTER);  //垂直方向上的对其方式
            int headcell_index=0;
            if(flag){//有序号
                sheet.setColumnWidth(headcell_index, 1600);
                Cell headcell = header_row.createCell(headcell_index);
                //应用样式到  单元格上
                headcell.setCellStyle(style);
                headcell.setCellValue("序号");
                headcell_index=1;
            }
            for (int i = 0; i < headers.length; i++) {
                sheet.setColumnWidth(headcell_index, 5000);
                Cell cell = header_row.createCell(headcell_index);
                cell.setCellStyle(style);
                cell.setCellValue(headers[i]);
                headcell_index++;
            }
            //插入需导出的数据
            for (int i = 0; i < dataList.size(); i++) {
                Row row = sheet.createRow(i + 1);
                row.setHeight((short) (20 * 20)); //设置行高  基数为20
                Object obj = dataList.get(i);
                Class classType = obj.getClass();
                int cell_index=0;
                if(flag){//有序号
                    Cell cell = row.createCell(cell_index);
                    cell.setCellValue(i+1);
                    cell_index=1;
                }
                for (int j = 0; j < fieldNames.length; j++) {
                    Cell cell = row.createCell(cell_index);
                    String fieldName = fieldNames[j];
                    String firstLetter = fieldName.substring(0, 1).toUpperCase();
                    String getMethodName = "get" + firstLetter + fieldName.substring(1);
                    Method getMethod = classType.getMethod(getMethodName, new Class[]{});
                    Object value = getMethod.invoke(obj, new Object[]{});
                    if(value!=null){
                        if (value instanceof Date) {//日期类型
                            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
                            cell.setCellValue(df.format(value));
                        } else {
                            cell.setCellValue(value.toString());
                        }
                    }
                    cell_index++;
                }
            }
         //   String agent = request.getHeader("USER-AGENT").toLowerCase();
            //根据浏览器类型处理文件名称
           // if (agent.indexOf("msie") > -1) {
                extfilename = URLEncoder.encode(extfilename, "UTF-8");
        //    } else {  //firefox/safari不转码
         //       extfilename = new String(extfilename.getBytes("UTF-8"), "ISO8859-1");
         //   }
            response.setContentType("application/msexcel");
            response.setHeader("Content-disposition", "attachment;filename=\"" + extfilename + "\"");
            work.write(out);
            out.flush();
            out.close();
        } catch (Exception e) {
            logger.error("ExportExcelUtil createExcel",e);
        } finally {
            out.close();
        }
    }

    /**
     * 导出excel
     * @param response
     * @param request
     * @param extfilename 文件名
     * @param sheetName   sheet页名
     * @param headers     表头集合
     * @param fieldNames  表头对应的实体属性集合
     * @param dataList    数据
     * @param flag        第一列是否为序号列
     * @throws IOException
     */
    public static void createExcelMap(HttpServletResponse response, HttpServletRequest request, String extfilename, String sheetName, String[] headers, String[] fieldNames, List dataList, boolean flag) throws IOException {
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            XSSFWorkbook work = new XSSFWorkbook();
            //创建工作表
            Sheet sheet = work.createSheet(sheetName);
            //显示标题
            Row title_row = sheet.createRow(0);
            title_row.setHeight((short) (40 * 20));
            Row header_row = sheet.createRow(0);
            //创建单元格的 显示样式
            CellStyle style = work.createCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER); //水平方向上的对其方式
            style.setVerticalAlignment(VerticalAlignment.CENTER);  //垂直方向上的对其方式
            int headcell_index=0;
            if(flag){//有序号
                sheet.setColumnWidth(headcell_index, 1600);
                Cell headcell = header_row.createCell(headcell_index);
                //应用样式到  单元格上
                headcell.setCellStyle(style);
                headcell.setCellValue("序号");
                headcell_index=1;
            }
            for (int i = 0; i < headers.length; i++) {
                sheet.setColumnWidth(headcell_index, 5000);
                Cell cell = header_row.createCell(headcell_index);
                cell.setCellStyle(style);
                cell.setCellValue(headers[i]);
                headcell_index++;
            }
            //插入需导出的数据
            for (int i = 0; i < dataList.size(); i++) {
                Row row = sheet.createRow(i + 1);
                row.setHeight((short) (20 * 20)); //设置行高  基数为20
                Map map = (Map) dataList.get(i);
//                Class classType = obj.getClass();
                int cell_index=0;
                if(flag){//有序号
                    Cell cell = row.createCell(cell_index);
                    cell.setCellValue(i+1);
                    cell_index=1;
                }
                for (int j = 0; j < fieldNames.length; j++) {
                    Cell cell = row.createCell(cell_index);
                    String fieldName = fieldNames[j];
                    Object value =map.get(fieldName);
                    if(value!=null){
                        if (value instanceof Date) {//日期类型
                            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");//设置日期格式
                            cell.setCellValue(df.format(value));
                        } else {
                            cell.setCellValue(value.toString());
                        }
                    }
                    cell_index++;
                }
            }
            String agent = request.getHeader("USER-AGENT").toLowerCase();
            //根据浏览器类型处理文件名称
            if (agent.indexOf("msie") > -1) {
                extfilename = URLEncoder.encode(extfilename, "UTF-8");
            } else {  //firefox/safari不转码
                extfilename = new String(extfilename.getBytes("UTF-8"), "ISO8859-1");
            }
            response.setContentType("application/msexcel");
            response.setHeader("Content-disposition", "attachment;filename=\"" + extfilename + "\"");
            work.write(out);
            out.flush();
            out.close();
        } catch (Exception e) {
            logger.error("ExportExcelUtil createExcelMap",e);
        } finally {
            out.close();
        }
    }

    public static void createExcelWithDatePattern(HttpServletResponse response, HttpServletRequest request, String extfilename, String sheetName, String[] headers, String[] fieldNames, List dataList, boolean flag, String datePattern) throws IOException {
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            XSSFWorkbook work = new XSSFWorkbook();
            //创建工作表
            Sheet sheet = work.createSheet(sheetName);
            //显示标题
            Row title_row = sheet.createRow(0);
            title_row.setHeight((short) (40 * 20));
            Row header_row = sheet.createRow(0);
            //创建单元格的 显示样式
            CellStyle style = work.createCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER); //水平方向上的对其方式
            style.setVerticalAlignment(VerticalAlignment.CENTER);  //垂直方向上的对其方式
            int headcell_index=0;
            if(flag){//有序号
                sheet.setColumnWidth(headcell_index, 1600);
                Cell headcell = header_row.createCell(headcell_index);
                //应用样式到  单元格上
                headcell.setCellStyle(style);
                headcell.setCellValue("序号");
                headcell_index=1;
            }
            for (int i = 0; i < headers.length; i++) {
                sheet.setColumnWidth(headcell_index, 5000);
                Cell cell = header_row.createCell(headcell_index);
                cell.setCellStyle(style);
                cell.setCellValue(headers[i]);
                headcell_index++;
            }
            //插入需导出的数据
            for (int i = 0; i < dataList.size(); i++) {
                Row row = sheet.createRow(i + 1);
                row.setHeight((short) (20 * 20)); //设置行高  基数为20
                Object obj = dataList.get(i);
                Class classType = obj.getClass();
                int cell_index=0;
                if(flag){//有序号
                    Cell cell = row.createCell(cell_index);
                    cell.setCellValue(i+1);
                    cell_index=1;
                }
                for (int j = 0; j < fieldNames.length; j++) {
                    Cell cell = row.createCell(cell_index);
                    String fieldName = fieldNames[j];
                    String firstLetter = fieldName.substring(0, 1).toUpperCase();
                    String getMethodName = "get" + firstLetter + fieldName.substring(1);
                    Method getMethod = classType.getMethod(getMethodName, new Class[]{});
                    Object value = getMethod.invoke(obj, new Object[]{});
                    if(value!=null){
                        if (value instanceof Date) {//日期类型
                            SimpleDateFormat df = new SimpleDateFormat(datePattern);//设置日期格式
                            cell.setCellValue(df.format(value));
                        } else {
                            cell.setCellValue(value.toString());
                        }
                    }
                    cell_index++;
                }
            }
//            String agent = request.getHeader("USER-AGENT").toLowerCase();
//            //根据浏览器类型处理文件名称
//            if (agent.indexOf("msie") > -1) {
            extfilename = URLEncoder.encode(extfilename, "UTF-8");
//            } else {  //firefox/safari不转码
//                extfilename = new String(extfilename.getBytes("UTF-8"), "ISO8859-1");
//            }
            response.setContentType("application/msexcel");
            response.setHeader("Content-disposition", "attachment;filename=\"" + extfilename + "\"");
            work.write(out);
            out.flush();
            out.close();
        } catch (Exception e) {
            logger.error("导出excel出错: ",e);
        } finally {
            out.close();
        }
    }

    public static void writeExcel(HttpServletResponse response, String fileName, String headers[], String title, List<String[]> dataList, String sheetName) throws IOException {
        OutputStream out = null;
        try {
            XSSFWorkbook work = ExportExcelUtil.createExcelOfUsers( headers, title, sheetName, dataList, null);
            out = response.getOutputStream();
            response.setContentType("application/ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName , "UTF-8"))));
            work.write(out);
            out.flush();
            out.close();
        } catch (Exception e) {
            logger.error("ExcelportExcelUtil writeExcel",e);
        } finally {
            out.close();
        }
    }

    public static void writeFtpExcel( String extfilename,String sheetName,String title, String headers[],List<String[]> dataList, String serverName,String port,String username,String password,String remotePath) throws IOException {
        //OutputStream out = null;
        try {
            XSSFWorkbook work = ExportExcelUtil.createExcelOfUsers( headers, title, sheetName, dataList, null);
            //out = response.getOutputStream();
           // response.setContentType("application/ms-excel;charset=UTF-8");
            //response.setHeader("Content-Disposition", "attachment;filename=".concat(String.valueOf(URLEncoder.encode(fileName , "UTF-8"))));
            //work.write(out);
            //out.flush();
            //out.close();

            ByteArrayOutputStream os = new ByteArrayOutputStream();
            work.write(os);
            byte[] b = os.toByteArray();
            ByteArrayInputStream inputStream = new ByteArrayInputStream(b);
            FTPClient ftpClient=new FTPClient();
            extfilename = new String(extfilename.getBytes("UTF-8"), "ISO8859-1"); //只有这种服务器不乱码
            boolean boo = storeFile(username, password, port, serverName, remotePath, extfilename,inputStream,ftpClient);
            logger.info("writeFtpExcel,将生成的excel文件上传到ftp,extfilename:"+extfilename+",上传结果boo:"+boo);
        } catch (Exception e) {
            logger.error("ExcelportExcelUtil writeFtpExcel",e);
        } finally {
            //out.close();
        }
    }

    /**
     * 导出excel
     * @param extfilename 文件名
     * @param sheetName   sheet页名
     * @param headers     表头集合
     * @param fieldNames  表头对应的实体属性集合
     * @param dataList    数据
     * @param flag        第一列是否为序号列
     * @throws IOException
     */
    public static void createExcelUpFtp(String extfilename, String sheetName, String[] headers, String[] fieldNames, List dataList, boolean flag,
                                        String serverName,String port,String username,String password,String remotePath) throws IOException {
        logger.info("导出excel方法createExcelUpFtp,extfilename="+extfilename+",sheetName="+sheetName+",serverName="+serverName+",port="+port+",username="+username+",remotePath="+remotePath);
        //OutputStream out = null;
        try {
            //out = response.getOutputStream();
            XSSFWorkbook work = new XSSFWorkbook();
            //创建工作表
            Sheet sheet = work.createSheet(sheetName);
            //显示标题
            Row title_row = sheet.createRow(0);
            title_row.setHeight((short) (40 * 20));
            Row header_row = sheet.createRow(0);
            //创建单元格的 显示样式
            CellStyle style = work.createCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER); //水平方向上的对其方式
            style.setVerticalAlignment(VerticalAlignment.CENTER);  //垂直方向上的对其方式
            int headcell_index=0;
            if(flag){  //有序号
                sheet.setColumnWidth(headcell_index, 1600);
                Cell headcell = header_row.createCell(headcell_index);
                //应用样式到  单元格上
                headcell.setCellValue("序号");
                headcell.setCellStyle(style);
                headcell_index=1;
            }
            for (int i = 0; i < headers.length; i++) {
                sheet.setColumnWidth(headcell_index, 5000);
                Cell cell = header_row.createCell(headcell_index);
                cell.setCellStyle(style);
                cell.setCellValue(headers[i]);
                headcell_index++;
            }
            //插入需导出的数据
            for (int i = 0; i < dataList.size(); i++) {
                Row row = sheet.createRow(i + 1);
                row.setHeight((short) (20 * 20)); //设置行高  基数为20
                Object obj = dataList.get(i);
                Class classType = obj.getClass();
                int cell_index=0;
                if(flag){//有序号
                    Cell cell = row.createCell(cell_index);
                    cell.setCellValue(i+1);
                    cell_index=1;
                }
                for (int j = 0; j < fieldNames.length; j++) {
                    Cell cell = row.createCell(cell_index);
                    String fieldName = fieldNames[j];
                    String firstLetter = fieldName.substring(0, 1).toUpperCase();
                    String getMethodName = "get" + firstLetter + fieldName.substring(1);
                    Method getMethod = classType.getMethod(getMethodName, new Class[]{});
                    Object value = getMethod.invoke(obj, new Object[]{});
                    if(value!=null){
                        if (value instanceof Date) {//日期类型
                            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd"); //设置日期格式
                            cell.setCellValue(df.format(value));
                        } else {
                            cell.setCellValue(value.toString());
                        }
                    }
                    cell_index++;
                }
            }


             ByteArrayOutputStream os = new ByteArrayOutputStream();
             work.write(os);
             byte[] b = os.toByteArray();
             ByteArrayInputStream inputStream = new ByteArrayInputStream(b);
             FTPClient ftpClient=new FTPClient();
              extfilename = new String(extfilename.getBytes("UTF-8"), "ISO8859-1"); //只有这种服务器不乱码
             //extfilename =new String(extfilename.getBytes("GBK"), "iso-8859-1");


             //extfilename=new String(extfilename.getBytes("iso-8859-1"),"utf8");

             boolean boo = storeFile(username, password, port, serverName, remotePath, extfilename,inputStream,ftpClient);
             logger.info("将生成的excel文件上传到ftp,extfilename:"+extfilename+",上传结果boo:"+boo);
            //boolean b1 = instance.connectToTheServer("ftp.test.ybm100.top", "/saas");
            //boolean b1 = instance.connectToTheServers(SNIServerName);
            //boolean a=instance.storeFile("ftp.test.ybm100.top", "/saas", "test.xml", inputStream);
            //System.out.println(b1);
/*            String agent = request.getHeader("USER-AGENT").toLowerCase();
            //根据浏览器类型处理文件名称
            if (agent.indexOf("msie") > -1) {
                extfilename = URLEncoder.encode(extfilename, "UTF-8");
            } else {  //firefox/safari不转码
                extfilename = new String(extfilename.getBytes("UTF-8"), "ISO8859-1");
            }
            response.setContentType("application/msexcel");
            response.setHeader("Content-disposition", "attachment;filename=\"" + extfilename + "\"");
            work.write(out);
            out.flush();
            out.close();*/

        } catch (Exception e) {
            logger.error("---createExcelUpFtp--此方法有异常---",e);
        } finally {
            //out.close();
        }
    }



    /**
     * 连接（配置通用连接属性）至服务器
     *
     * @param serverName 服务器名称
     * @param remotePath 当前访问目录
     * @return <b>true</b>：连接成功
     * <br/>
     * <b>false</b>：连接失败
     */
    public static boolean connectToTheServer(String username,String password,String port,String serverName, String remotePath,FTPClient ftpClient) {
        // 定义返回值
        boolean result = false;
        try {
            // 连接至服务器，端口默认为21时，可直接通过URL连接
            ftpClient.connect(serverName, Integer.parseInt(port));
            // 登录服务器
            ftpClient.login(username, password);
            // 判断返回码是否合法
            if (!FTPReply.isPositiveCompletion(ftpClient.getReplyCode())) {
                // 不合法时断开连接
                ftpClient.disconnect();
                // 结束程序
                return result;
            }
            // 检查上传路径是否存在 如果不存在返回false
            boolean flags = ftpClient.changeWorkingDirectory(remotePath);
            logger.info("ftp检查上传路径是否存在,"+flags);
            if (!flags) {
                // 创建上传的路径 该方法只能创建一级目录，在这里如果/home/<USER>
                //ftpClient.makeDirectory(remotePath);
                mkDir(remotePath,ftpClient);
                logger.info("ftp获取链接同时创建目录"+remotePath);
            }
            // 设置文件操作目录
            result = ftpClient.changeWorkingDirectory(remotePath);
            // 设置文件类型，二进制
            result = ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
            // 设置缓冲区大小
            ftpClient.setBufferSize(3072);
            // 设置字符编码
            //ftpClient.setControlEncoding("ISO-8859-1");
            ftpClient.setControlEncoding("UTF-8");
            FTPClientConfig conf = new FTPClientConfig(FTPClientConfig.SYST_NT);
            conf.setServerLanguageCode("zh");
            ftpClient.configure(conf);
        } catch (IOException e) {
            logger.error("---connectToTheServer--此方法有异常---",e);
        }
        return result;
    }
    /**
     * 循环创建目录，并且创建完目录后，设置工作目录为当前创建的目录下
     */
    public static boolean mkDir(String ftpPath,FTPClient ftpClient) {
        if (!ftpClient.isConnected()) {
            return false;
        }
        try {
            // 将路径中的斜杠统一
            char[] chars = ftpPath.toCharArray();
            StringBuffer sbStr = new StringBuffer(256);
            for (int i = 0; i < chars.length; i++) {

                if ('\\' == chars[i]) {
                    sbStr.append('/');
                } else {
                    sbStr.append(chars[i]);
                }
            }
            ftpPath = sbStr.toString();

            if (ftpPath.indexOf('/') == -1) {
                // 只有一层目录
                ftpClient.makeDirectory(new String(ftpPath.getBytes(), "iso-8859-1"));
                ftpClient.changeWorkingDirectory(new String(ftpPath.getBytes(), "iso-8859-1"));
            } else {
                // 多层目录循环创建
                String[] paths = ftpPath.split("/");
                for (int i = 0; i < paths.length; i++) {
                    ftpClient.makeDirectory(new String(paths[i].getBytes(), "iso-8859-1"));
                    ftpClient.changeWorkingDirectory(new String(paths[i].getBytes(), "iso-8859-1"));
                }
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 上传文件至FTP服务器
     *
     * @param serverName 服务器名称
     * @param storePath  上传文件存储路径
     * @param fileName   上传文件存储名称
     * @param is         上传文件输入流
     * @return <b>true</b>：上传成功
     * <br/>
     * <b>false</b>：上传失败
     */
    public static boolean storeFile(String username,String password,String port, String serverName, String storePath, String fileName, InputStream is,FTPClient ftpClient) {
        boolean result = false;
        try {
            // 连接至服务器
            result = connectToTheServer(username, password, port,serverName, storePath,ftpClient);
            // 判断服务器是否连接成功
            if (result) {
                // 上传文件
                result = ftpClient.storeFile(fileName, is);
            }
            // 关闭输入流
            is.close();
        } catch (IOException e) {
            logger.error("---storeFile--此方法有异常---",e);
        } finally {
            // 判断输入流是否存在
            if (null != is) {
                try {
                    // 关闭输入流
                    is.close();
                } catch (IOException e) {
                    logger.error("storeFile close",e);
                }
            }
            // 登出服务器并断开连接
            logout(is,ftpClient);
        }
        return result;
    }


    /**
     * 登出服务器并断开连接
     *
     * @return <b>true</b>：操作成功
     * <br/>
     * <b>false</b>：操作失败
     */
    public static boolean logout(InputStream is,FTPClient ftpClient) {
        boolean result = false;
        if (null != is) {
            try {
                // 关闭输入流
                is.close();
            } catch (IOException e) {
                logger.error("logout close",e);
            }
        }
        if (null != ftpClient) {
            try {
                // 登出服务器
                result = ftpClient.logout();
            } catch (IOException e) {
                logger.error("logout2",e);
            } finally {
                // 判断连接是否存在
                if (ftpClient.isConnected()) {
                    try {
                        // 断开连接
                        ftpClient.disconnect();
                    } catch (IOException e) {
                        logger.error("logout3",e);
                    }
                }
            }
        }
        return result;
    }
     /* 下载FTP服务器文件至本地<br/>
      * 操作完成后需调用logout方法与服务器断开连接
      *
      * @param serverName 服务器名称
     * @param remotePath 下载文件存储路径
     * @param fileName   下载文件存储名称
     * @return <b>InputStream</b>：文件输入流
     */

    public static InputStream retrieveFile(String username,String password,String port, String serverName, String storePath, String fileName, FTPClient ftpClient) {
        InputStream is=null;
        try {
            boolean result = false;
            // 连接至服务器
            result = connectToTheServer(username,password, port,serverName,storePath,ftpClient);
            // 判断服务器是否连接成功
            if (result) {
                ftpClient.setControlEncoding("UTF-8"); // 中文支持
                ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
                ftpClient.enterLocalPassiveMode();
                // 获取文件输入流
                is = ftpClient.retrieveFileStream(fileName);
            }
        } catch (IOException e) {
            logger.error("retrieveFile",e);
        }
        return is;
    }


    /*
     * 从FTP服务器下载文件
     *
     * @param ftpHost FTP IP地址
     * @param ftpUserName FTP 用户名
     * @param ftpPassword FTP用户名密码
     * @param ftpPort FTP端口
     * @param ftpPath FTP服务器中文件所在路径 格式： ftptest/aa
     * @param localPath 下载到本地的位置 格式：H:/download
     * @param fileName 文件名称
     */
    public static void downloadFtpFile(String ftpHost, String ftpUserName,
                                       String ftpPassword, String ftpPort, String ftpPath, String localPath,
                                       String fileName,FTPClient ftpClient) {

        //FTPClient ftpClient = null;

        try {
            //ftpClient = getFTPClient(ftpHost, ftpUserName, ftpPassword, ftpPort);
            // 连接至服务器
            boolean result = connectToTheServer(ftpUserName, ftpPassword, ftpPort,ftpHost, ftpPath,ftpClient);
            ftpClient.setControlEncoding("UTF-8"); // 中文支持
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
            ftpClient.enterLocalPassiveMode();
            ftpClient.changeWorkingDirectory(ftpPath);

            File localFile = new File(localPath + File.separatorChar + fileName);
            OutputStream os = new FileOutputStream(localFile);
            ftpClient.retrieveFile(fileName, os);
            os.close();
            ftpClient.logout();

        } catch (FileNotFoundException e) {
            logger.error("没有找到" + ftpPath + "文件", e);
        } catch (SocketException e) {
            logger.error("连接FTP失败",e);
        } catch (IOException e) {
            logger.error("文件读取错误",e);
        }

    }
    public static  boolean checkFile(String username,String password,String port, String serverName, String storePath, String fileName,FTPClient ftpClient) {
        boolean result = false;
        try {
            // 连接至服务器
            result = connectToTheServer(username, password, port,serverName, storePath,ftpClient);
            // 判断服务器是否连接成功
            if (result) {
                // 默认文件不存在
                result = false;
                // 获取文件操作目录下所有文件名称
                String[] remoteNames = ftpClient.listNames();
                // 循环比对文件名称，判断是否含有当前要下载的文件名
                for (String remoteName : remoteNames) {
                    if (fileName.equals(remoteName)) {
                        result = true;
                    }
                }
            }
        } catch (IOException e) {
            logger.error("checkFile",e);
        } finally {
            // 登出服务器并断开连接
           logout(null,ftpClient);
        }
        return result;
    }

    public static String getUUID() {
        String uuid = UUID.randomUUID().toString();
        return uuid.replaceAll("\\-", "");
    }
    //测试
    public static void main(String[] args) throws Exception {

        String headers[] = new String[]{"ID","用户名","年龄","邮箱"};
        String title = "用户详细信息表";
        String sheetName = "用户信息表";

        List<String[]> dataList = new ArrayList<String[]>();
        dataList.add(new String[]{"东邪","17232401001","男","2015年9月"});
        dataList.add(new String[]{"西毒","17232401002","女","2016年9月"});
        dataList.add(new String[]{"南帝","17232401003","男","2017年9月"});
        dataList.add(new String[]{"北丐","17232401004","男","2015年9月"});
        dataList.add(new String[]{"中神通","17232401005","女","2017年9月"});
        createExcelOfUsers(headers,title,sheetName,dataList,null);

    }

    /**
     * 导出excel - 出入库明细导出《专用》
     * @param extfilename 文件名
     * @param sheetName   sheet页名
     * @param headers     表头集合
     * @param fieldNames  表头对应的实体属性集合
     * @param dataList    数据
     * @param flag        第一列是否为序号列
     * @throws IOException
     */
    public static void createExcelUpFtpByStorageDetail(String extfilename, String sheetName, String[] headers, String[] fieldNames, List dataList, boolean flag,
                                                       String serverName,String port,String username,String password,String remotePath) throws IOException {
        logger.info("导出excel方法createExcelUpFtpByStorageDetail,extfilename="+extfilename+",sheetName="+sheetName+",serverName="+serverName+",port="+port+",username="+username+",remotePath="+remotePath);
        //OutputStream out = null;
        try {
            //out = response.getOutputStream();
            XSSFWorkbook work = new XSSFWorkbook();
            //创建工作表
            Sheet sheet = work.createSheet(sheetName);
            //显示标题
            Row title_row = sheet.createRow(0);
            title_row.setHeight((short) (40 * 20));

            //创建单元格的 显示样式
            CellStyle style = work.createCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER); //水平方向上的对其方式
            style.setVerticalAlignment(VerticalAlignment.CENTER);  //垂直方向上的对其方式

            for (int j = 0;j < 2;j++){
                int cell_index = 0;
                Row header_row = sheet.createRow(j);
                if(flag){  //有序号
                    Cell headcell = header_row.createCell(cell_index);
                    //应用样式到  单元格上
                    headcell.setCellValue("序号");
                    headcell.setCellStyle(style);
                    if (j == 1){
                        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
                    }
                    cell_index = 1;
                }
                for (int i = 0; i < headers.length; i++) {
                    Cell cell = header_row.createCell(cell_index);
                    sheet.setColumnWidth(cell_index, 5000);
                    cell.setCellStyle(style);
                    cell.setCellValue(headers[i]);
                    if (j == 0){
                        if (cell_index == 11 || cell_index == 12){
                            cell.setCellValue("出库");
                        }
                        if (cell_index == 13 || cell_index == 14){
                            cell.setCellValue("入库");
                        }
                        if (cell_index == 12 || cell_index == 14) {
                            sheet.addMergedRegion(new CellRangeAddress(0, 0, cell_index - 1, cell_index));
                        }
                    }
                    if (j == 1){
                        if (cell_index != 11 && cell_index!= 12 && cell_index != 13 && cell_index != 14) {
                            sheet.addMergedRegion(new CellRangeAddress(0, 1, cell_index, cell_index));
                        }
                    }
                    cell_index ++;
                }
            }

            //插入需导出的数据
            for (int i = 0; i < dataList.size(); i++) {
                Row row = sheet.createRow(i + 1 + 1);
                row.setHeight((short) (20 * 20)); //设置行高  基数为20
                Object obj = dataList.get(i);
                Class classType = obj.getClass();
                int cell_index=0;
                if(flag){//有序号
                    Cell cell = row.createCell(cell_index);
                    cell.setCellValue(i+1);
                    cell_index=1;
                }
                for (int j = 0; j < fieldNames.length; j++) {
                    Cell cell = row.createCell(cell_index);
                    String fieldName = fieldNames[j];
                    String firstLetter = fieldName.substring(0, 1).toUpperCase();
                    String getMethodName = "get" + firstLetter + fieldName.substring(1);
                    Method getMethod = classType.getMethod(getMethodName, new Class[]{});
                    Object value = getMethod.invoke(obj, new Object[]{});
                    if(value!=null){
                        if (value instanceof Date) {//日期类型
                            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd"); //设置日期格式
                            cell.setCellValue(df.format(value));
                        } else {
                            cell.setCellValue(value.toString());
                        }
                    }
                    cell_index++;
                }
            }
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            work.write(os);
            byte[] b = os.toByteArray();
            ByteArrayInputStream inputStream = new ByteArrayInputStream(b);
            FTPClient ftpClient=new FTPClient();
            extfilename = new String(extfilename.getBytes("UTF-8"), "ISO8859-1"); //只有这种服务器不乱码
            boolean boo = storeFile(username, password, port, serverName, remotePath, extfilename,inputStream,ftpClient);
            logger.info("将生成的excel文件上传到ftp,extfilename:"+extfilename+",上传结果boo:"+boo);
        } catch (Exception e) {
            logger.error("---createExcelUpFtpByStorageDetail--此方法有异常---",e);
        } finally {
            //out.close();
        }
    }

    /**
     * 江西省吉安市医保药品出入库导出excel
     *
     * @param response
     * @param request
     * @param extfilename 文件名
     * @param sheetName   sheet页名
     * @param headers     表头集合
     * @param fieldNames  表头对应的实体属性集合
     * @param dataList    数据
     * @param flag        第一列是否为序号列
     * @throws IOException
     */
    public static void createExcelByJxja(HttpServletResponse response, HttpServletRequest request, String extfilename,
                                         String sheetName, String[] headers, String[] fieldNames, List dataList, boolean flag) throws IOException {
        OutputStream out = null;
        XSSFWorkbook work = new XSSFWorkbook();
        try {
            out = response.getOutputStream();
            //创建工作表
            Sheet sheet = work.createSheet(sheetName);
            //创建单元格的 显示样式
            CellStyle style = work.createCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER); //水平方向上的对其方式
            style.setVerticalAlignment(VerticalAlignment.CENTER);  //垂直方向上的对其方式
            //显示标题
            Row title_row = sheet.createRow(0);
            title_row.setHeight((short) (40 * 20));

            for (int j = 0; j < 2; j++) {
                Row header_row = sheet.createRow(j);
                int headcell_index = 0;
                if(flag){//有序号
                    sheet.setColumnWidth(headcell_index, 1600);
                    Cell headcell = header_row.createCell(headcell_index);
                    //应用样式到  单元格上
                    headcell.setCellStyle(style);
                    headcell.setCellValue("序号");
                    if (j == 1){
                        sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
                    }
                    headcell_index = 1;
                }

                for (int i = 0; i < headers.length; i++) {
                    sheet.setColumnWidth(headcell_index, 5000);
                    Cell cell = header_row.createCell(headcell_index);
                    cell.setCellStyle(style);
                    cell.setCellValue(headers[i]);
                    if (j == 0) {
                        if (headcell_index == 12) {
                            sheet.addMergedRegion(new CellRangeAddress(0, 0, headcell_index - 2, headcell_index));
                        }
                        if (headcell_index == 15) {
                            sheet.addMergedRegion(new CellRangeAddress(0, 0, headcell_index - 2, headcell_index));
                        }
                        if (headcell_index == 20) {
                            sheet.addMergedRegion(new CellRangeAddress(0, 0, headcell_index - 4, headcell_index));
                        }
                        if (headcell_index == 23) {
                            sheet.addMergedRegion(new CellRangeAddress(0, 0, headcell_index - 2, headcell_index));
                        }
                        if (headcell_index >= 10 && headcell_index <= 12){
                            cell.setCellValue("期初");
                        }
                        if (headcell_index >= 13 && headcell_index <= 15){
                            cell.setCellValue("入库");
                        }
                        if (headcell_index >= 16 && headcell_index <= 20){
                            cell.setCellValue("出库");
                        }
                        if (headcell_index >= 21 && headcell_index <= 23){
                            cell.setCellValue("结存");
                        }
                    }
                    if (j == 1) {
                        if (headcell_index < 10) {
                            sheet.addMergedRegion(new CellRangeAddress(0, 1, headcell_index, headcell_index));
                        }
                    }
                    headcell_index ++;
                }
            }
            //插入需导出的数据
            for (int i = 0; i < dataList.size(); i++) {
                Row row = sheet.createRow(i + 2);
                row.setHeight((short) (20 * 20)); //设置行高  基数为20
                Object obj = dataList.get(i);
                Class classType = obj.getClass();
                int cell_index = 0;
                if (flag) {//有序号
                    Cell cell = row.createCell(cell_index);
                    cell.setCellValue(i + 1);
                    cell_index = 1;
                }
                for (int j = 0; j < fieldNames.length; j++) {
                    Cell cell = row.createCell(cell_index);
                    String fieldName = fieldNames[j];
                    String firstLetter = fieldName.substring(0, 1).toUpperCase();
                    String getMethodName = "get" + firstLetter + fieldName.substring(1);
                    Method getMethod = classType.getMethod(getMethodName, new Class[]{});
                    Object value = getMethod.invoke(obj, new Object[]{});
                    if (value != null) {
                        if (value instanceof Date) {//日期类型
                            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); //设置日期格式
                            cell.setCellValue(df.format(value));
                        } else {
                            cell.setCellValue(value.toString());
                        }
                    }
                    cell_index++;
                }
            }
            String agent = request.getHeader("USER-AGENT").toLowerCase();
            //根据浏览器类型处理文件名称
            if (agent.indexOf("msie") > -1) {
                extfilename = URLEncoder.encode(extfilename, "UTF-8");
            } else {  //firefox/safari不转码
                extfilename = new String(extfilename.getBytes("UTF-8"), "ISO8859-1");
            }
            response.setContentType("application/msexcel");
            response.setHeader("Content-disposition", "attachment;filename=\"" + extfilename + "\"");
            work.write(out);
            out.flush();
            out.close();
        } catch (Exception e) {
            logger.info("错误"+e.getMessage());
        } finally {
            work.close();
            out.close();
        }
    }

    /**
     * 导出格式为文本
     * @throws IOException
     */
    public static void createExcelWithDatePatternV2(HttpServletResponse response, HttpServletRequest request, String extfilename, String sheetName, String[] headers, String[] fieldNames, List dataList, boolean flag, String datePattern) throws IOException {
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            XSSFWorkbook work = new XSSFWorkbook();
            //创建工作表
            Sheet sheet = work.createSheet(sheetName);
            //显示标题
            Row title_row = sheet.createRow(0);
            title_row.setHeight((short) (40 * 20));
            Row header_row = sheet.createRow(0);
            //创建单元格的 显示样式
            CellStyle style = work.createCellStyle();
            XSSFDataFormat format  = work.createDataFormat();
            style.setDataFormat(format.getFormat("@"));
            style.setAlignment(HorizontalAlignment.CENTER); //水平方向上的对其方式
            style.setVerticalAlignment(VerticalAlignment.CENTER);  //垂直方向上的对其方式
            int headcell_index=0;
            if(flag){//有序号
                sheet.setColumnWidth(headcell_index, 1600);
                Cell headcell = header_row.createCell(headcell_index);
                //应用样式到  单元格上
                headcell.setCellStyle(style);
                headcell.setCellValue("序号");
                headcell_index=1;
            }
            for (int i = 0; i < headers.length; i++) {
                sheet.setColumnWidth(headcell_index, 5000);
                sheet.setDefaultColumnStyle(i,style);
                Cell cell = header_row.createCell(headcell_index);
                cell.setCellStyle(style);
                cell.setCellValue(headers[i]);
                headcell_index++;
            }
            //插入需导出的数据
            for (int i = 0; i < dataList.size(); i++) {
                Row row = sheet.createRow(i + 1);
                row.setHeight((short) (20 * 20)); //设置行高  基数为20
                Object obj = dataList.get(i);
                Class classType = obj.getClass();
                int cell_index=0;
                if(flag){//有序号
                    Cell cell = row.createCell(cell_index);
                    cell.setCellStyle(style);
                    cell.setCellValue(i+1);
                    cell_index=1;
                }
                for (int j = 0; j < fieldNames.length; j++) {
                    Cell cell = row.createCell(cell_index);
                    String fieldName = fieldNames[j];
                    String firstLetter = fieldName.substring(0, 1).toUpperCase();
                    String getMethodName = "get" + firstLetter + fieldName.substring(1);
                    Method getMethod = classType.getMethod(getMethodName, new Class[]{});
                    Object value = getMethod.invoke(obj, new Object[]{});
                    if(value!=null){
                        if (value instanceof Date) {
                            SimpleDateFormat df = new SimpleDateFormat(datePattern);//设置日期格式
                            cell.setCellValue(df.format(value));
                            cell.setCellStyle(style);
                        } else {
                            cell.setCellValue(value.toString());
                            cell.setCellStyle(style);
                        }
                    }
                    cell_index++;
                }
            }
            extfilename = URLEncoder.encode(extfilename, "UTF-8");
            response.setHeader("conent-type", "application/octet-stream");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + extfilename);
            work.write(out);
            out.flush();
            out.close();
        } catch (Exception e) {
            logger.error("导出excel出错: ",e);
        } finally {
            out.close();
        }
    }
}