package com.xyy.saas.emule.utils;

import com.github.tobato.fastdfs.domain.fdfs.StorePath;
import com.github.tobato.fastdfs.service.FastFileStorageClient;
import org.apache.commons.io.FilenameUtils;
import org.csource.fastdfs.ProtoCommon;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@Component
public class DfsUploadUtil {
    private static final Logger logger = LoggerFactory.getLogger(DfsUploadUtil.class);
    @Resource
    @Qualifier("defaultFastFileStorageClient")
    private FastFileStorageClient fastFileStorageClient;
    @Value("${upload.file.url}")
    private String uploadFileUrl;

    @Value("${fdfs.http_secret_key}")
    private String secretKey;
    @Value("${fdfs.http_anti_steal_token}")
    private Boolean antiStealToken;
    @Value("${upload.file.uploadUrlRegex}")
    private String uploadUrlRegex;

    public String saveFile(MultipartFile file) {
        if (StringUtils.isEmpty(uploadFileUrl)) {
            throw new RuntimeException("uploadUrl can not be null");
        }

        String url = "";
        String fileName = file.getOriginalFilename();
        StorePath storePath=null;
        try {
            storePath =  fastFileStorageClient.uploadFile(file.getInputStream(),file.getSize(), FilenameUtils.getExtension(fileName),null);
            logger.info("上传地址"+storePath.getFullPath());
            url = uploadFileUrl +storePath.getFullPath();
        }catch (Exception e){
            logger.info("文件名为" + fileName + "上传报错", e);
        }
        return url;
    }

    /**
     * 带有防盗链得下载
     * antiStealToken   true  开启防盗链
     * @param url
     * @return
     * @throws Exception
     */
    public String autoDownloadWithToken(String url) {
        if(StringUtils.isEmpty(url)){
            logger.info("autoDownloadWithToken url is null:{}",url);
            return url;
        }
        if(antiStealToken == false){
            return  url;
        }
        try{
            String filePathName = url.replaceAll(uploadUrlRegex,"");
            //文件名称
            String remoteFileName = filePathName.substring(filePathName.indexOf("/") + 1);
            int ts = (int) (System.currentTimeMillis() / 1000);
            String token = ProtoCommon.getToken(remoteFileName, ts, secretKey);
            url = uploadFileUrl +filePathName + "?token=" + token + "&ts=" + ts;
        }catch (Exception e){
            logger.error("autoDownloadWithToken error",e);
        }
        return url;
    }

}
