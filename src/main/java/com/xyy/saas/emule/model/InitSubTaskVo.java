package com.xyy.saas.emule.model;

import io.swagger.annotations.ApiModel;

import java.io.Serializable;

/**
 * 期初自定义模板vo
 * <AUTHOR>
 */
@ApiModel(description = "期初自定义模板vo")
public class InitSubTaskVo implements Serializable {

    private static final long serialVersionUID = -5970049398518107496L;

    /**
     * 子任务主键
     */
    private Long id;

    /**
     * 任务主键
     */
    private Long taskId;

    /**
     * 机构号
     */
    private String organSign;

    /**
     * 任务内容
     */
    private String taskInfo;

    /**
     * 错误信息
     */
    private String errorInfo;

    /**
     * 同步状态 0：失败，1:成功，2：未同步
     */
    private Integer status;

    /**
     * 操作人Id
     */
    private String operId;


    /**
     * ES查询名称
     */
    private String queryColumn;

    /**
     *页码
     */
    private Integer pageNum;

    /**
     *数量
     */
    private Integer pageSize;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public String getTaskInfo() {
        return taskInfo;
    }

    public void setTaskInfo(String taskInfo) {
        this.taskInfo = taskInfo;
    }

    public String getErrorInfo() {
        return errorInfo;
    }

    public void setErrorInfo(String errorInfo) {
        this.errorInfo = errorInfo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getOperId() {
        return operId;
    }

    public void setOperId(String operId) {
        this.operId = operId;
    }

    public String getQueryColumn() {
        return queryColumn;
    }

    public void setQueryColumn(String queryColumn) {
        this.queryColumn = queryColumn;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
