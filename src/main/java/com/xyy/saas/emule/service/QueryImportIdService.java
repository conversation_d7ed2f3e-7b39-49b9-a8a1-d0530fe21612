package com.xyy.saas.emule.service;

import java.util.Map;

/**
 * 查询导入ID
 * 自定义扩展吧!
 * 从其他地方copy的
 * employeeId必须有
 */
public interface QueryImportIdService {

    /**
     * 获取key
     * @param employeeId
     * @param model
     * @param extend
     * @return
     */
    Long getId(String employeeId, String model, Map<String, Object> extend);


    /**
     * 删除key
     * @param employeeId
     * @param model
     * @param extend
     */
    void deleteImportTaskIdKey(String employeeId, String model, Map<String, Object> extend);
}
