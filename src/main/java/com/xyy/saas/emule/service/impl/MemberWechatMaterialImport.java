package com.xyy.saas.emule.service.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.emule.service.QueryImportIdService;
import com.xyy.saas.member.api.WxMemberGetImportIdApi;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class MemberWechatMaterialImport implements QueryImportIdService {

    @Reference(version = "0.0.1")
    WxMemberGetImportIdApi wxMemberGetImportIdApi;

    @Override
    public Long getId(String employeeId, String model, Map<String, Object> extend) {
        return wxMemberGetImportIdApi.getMaterialImportTaskId(employeeId);
    }

    @Override
    public void deleteImportTaskIdKey(String employeeId, String model, Map<String, Object> extend) {
        wxMemberGetImportIdApi.deleteImportTaskIdKey(employeeId);
    }
}
