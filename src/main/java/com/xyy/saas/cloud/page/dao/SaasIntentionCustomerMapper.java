package com.xyy.saas.cloud.page.dao;


import com.xyy.saas.cloud.page.po.SaasIntentionCustomerPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SaasIntentionCustomerMapper {
    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    SaasIntentionCustomerPo queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @param saasIntentionCustomer 查询条件
     * @return 对象列表
     */
    List<SaasIntentionCustomerPo> queryByCondition(SaasIntentionCustomerPo saasIntentionCustomer);

    /**
     * 统计总行数
     *
     * @param saasIntentionCustomer 查询条件
     * @return 总行数
     */
    long count(SaasIntentionCustomerPo saasIntentionCustomer);

    /**
     * 新增数据
     *
     * @param saasIntentionCustomer 实例对象
     * @return 影响行数
     */
    int insertSelective(SaasIntentionCustomerPo saasIntentionCustomer);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<SaasIntentionCustomer> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<SaasIntentionCustomerPo> list);


    /**
     * 修改数据
     *
     * @param saasIntentionCustomer 实例对象
     * @return 影响行数
     */
    int updateSelective(SaasIntentionCustomerPo saasIntentionCustomer);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    SaasIntentionCustomerPo queryByNameAndPhone(SaasIntentionCustomerPo saasIntentionCustomerPo);
}
