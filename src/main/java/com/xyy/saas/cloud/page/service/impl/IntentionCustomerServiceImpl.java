package com.xyy.saas.cloud.page.service.impl;

import com.xyy.saas.cloud.page.constant.HasSubmittedEnum;
import com.xyy.saas.cloud.page.dao.SaasIntentionCustomerBehaviorMapper;
import com.xyy.saas.cloud.page.dao.SaasIntentionCustomerClueMapper;
import com.xyy.saas.cloud.page.dao.SaasIntentionCustomerMapper;
import com.xyy.saas.cloud.page.po.SaasIntentionCustomerBehaviorPo;
import com.xyy.saas.cloud.page.po.SaasIntentionCustomerPo;
import com.xyy.saas.cloud.page.service.IntentionCustomerService;
import com.xyy.saas.cloud.page.vo.IntentionCustomerVo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.util.UUIDUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
public class IntentionCustomerServiceImpl implements IntentionCustomerService {

    @Autowired
    private SaasIntentionCustomerBehaviorMapper saasIntentionCustomerBehaviorMapper;
    @Autowired
    private SaasIntentionCustomerClueMapper saasIntentionCustomerClueMapper;
    @Autowired
    private SaasIntentionCustomerMapper saasIntentionCustomerMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<String> saveBehavior(IntentionCustomerVo intentionCustomerVo) {
        //保存客户信息
        SaasIntentionCustomerPo customer = saveCustomer(intentionCustomerVo);
        //保存客户行为
        saasIntentionCustomerBehaviorMapper.insertSelective(SaasIntentionCustomerBehaviorPo.builder()
                        .hasSubmitted(HasSubmittedEnum.NOT_SUBMITTED.code)
                        .customerPref(customer.getCustomerPref())
                        .viewTime(new Date()).createTime(new Date()).updateTime(new Date()).build());
        return ResultVO.createSuccess("保存成功！");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultVO<String> saveClue(IntentionCustomerVo intentionCustomerVo) {
        saveCustomer(intentionCustomerVo);
        return null;
    }

    private SaasIntentionCustomerPo saveCustomer(IntentionCustomerVo intentionCustomerVo) {
        SaasIntentionCustomerPo customer =   saasIntentionCustomerMapper.queryByNameAndPhone(SaasIntentionCustomerPo.builder().customerPhone(intentionCustomerVo.getCustomerPhone())
                .customerDrugstoreName(intentionCustomerVo.getCustomerDrugstoreName()).build());
        if(customer == null || customer.getId() == null || StringUtils.isBlank(customer.getCustomerPref())) {
            customer = SaasIntentionCustomerPo.builder().customerPref(UUIDUtil.newUUID()).customerDrugstoreName(intentionCustomerVo.getCustomerDrugstoreName()).customerPhone(intentionCustomerVo.getCustomerPhone())
                    .province(intentionCustomerVo.getProvince()).city(intentionCustomerVo.getCity()).area(intentionCustomerVo.getArea())
                    .createTime(new Date()).updateTime(new Date()).build();
            saasIntentionCustomerMapper.insertSelective(customer);
        }
    }
}
