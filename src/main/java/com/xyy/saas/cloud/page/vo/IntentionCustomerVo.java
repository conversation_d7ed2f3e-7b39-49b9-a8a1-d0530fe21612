package com.xyy.saas.cloud.page.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IntentionCustomerVo implements Serializable {

    /**
     * 药店名称
     */
    private String customerDrugstoreName;
    /**
     * 联系方式
     */
    private String customerPhone;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String area;

    /**
     * 期望联系日期
     */
    private Date expectedDate;
    /**
     * 时段 1上午 2下午
     */
    private Integer expectedPeriod;
    /**
     * 备注
     */
    private String remark;
    /**
     * 数据来源 1智慧脸 2药帮忙
     */
    private Integer source;
    /**
     * 销售姓名
     */
    private String salesName;
    /**
     * 销售电话
     */
    private String salesPhone;
}
