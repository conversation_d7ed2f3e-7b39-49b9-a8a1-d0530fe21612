package com.xyy.saas.cloud.page.controller;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.xyy.saas.cloud.page.constant.ExpectedPeriodEnum;
import com.xyy.saas.cloud.page.constant.IntentionCustomerSourceEnum;
import com.xyy.saas.cloud.page.dao.SaasIntentionCustomerBehaviorMapper;
import com.xyy.saas.cloud.page.dao.SaasIntentionCustomerClueMapper;
import com.xyy.saas.cloud.page.po.SaasIntentionCustomerBehaviorPo;
import com.xyy.saas.cloud.page.po.SaasIntentionCustomerCluePo;
import com.xyy.saas.cloud.page.service.IntentionCustomerService;
import com.xyy.saas.cloud.page.vo.IntentionCustomerVo;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Objects;

@RestController
@RequestMapping("/intention/customer")
public class IntentionCustomerController {

    private static final Logger logger = LoggerFactory.getLogger(IntentionCustomerController.class);


    @Autowired
    private IntentionCustomerService intentionCustomerService;

    /**
     * 保存客户行为
     *
     * @return ResultVO
     */
    @PostMapping("/saveBehavior")
    public ResultVO<String> saveBehavior(@RequestBody IntentionCustomerVo intentionCustomerVo) {
        try {
            logger.info("保存客户行为开始，参数：{}", JSONUtil.toJsonStr(intentionCustomerVo));
            if (StringUtils.isAnyBlank(intentionCustomerVo.getCustomerDrugstoreName(), intentionCustomerVo.getCustomerPhone()
                    , intentionCustomerVo.getProvince(), intentionCustomerVo.getCity(), intentionCustomerVo.getArea())) {
                return new ResultVO<>(ResultCodeEnum.ERROR, "缺少必填参数！");
            }
            return intentionCustomerService.saveBehavior(intentionCustomerVo);
        } catch (Exception e) {
            logger.error("保存客户行为异常", e);
            return new ResultVO<>(ResultCodeEnum.ERROR, "系统异常：" + e.getMessage());

        }
    }

    /**
     * 保存客户线索
     *
     * @return ResultVO
     */
        @PostMapping("/saveClue")
        public ResultVO<String> saveClue(@RequestBody IntentionCustomerVo intentionCustomerVo) {
            try {
                logger.info("保存客户线索开始，参数：{}", JSONUtil.toJsonStr(intentionCustomerVo));
                if (StringUtils.isAnyBlank(intentionCustomerVo.getCustomerDrugstoreName(), intentionCustomerVo.getCustomerPhone()
                        , intentionCustomerVo.getProvince(), intentionCustomerVo.getCity(), intentionCustomerVo.getArea())
                        || intentionCustomerVo.getExpectedDate() == null
                        || Arrays.stream(ExpectedPeriodEnum.values()).noneMatch(e-> Objects.equals(e.code,intentionCustomerVo.getExpectedPeriod()))
                        || Arrays.stream(IntentionCustomerSourceEnum.values()).noneMatch(e-> Objects.equals(e.code,intentionCustomerVo.getSource()))
                ) {
                    return new ResultVO<>(ResultCodeEnum.ERROR, "缺少必填参数！");
                }
                return intentionCustomerService.saveClue(intentionCustomerVo);
            } catch (Exception e) {
                logger.error("保存客户线索异常", e);
                return new ResultVO<>(ResultCodeEnum.ERROR, "系统异常：" + e.getMessage());
            }
        }
    }

}
