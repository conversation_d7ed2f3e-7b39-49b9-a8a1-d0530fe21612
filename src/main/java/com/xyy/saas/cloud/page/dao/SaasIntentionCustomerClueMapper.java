package com.xyy.saas.cloud.page.dao;

import com.xyy.saas.cloud.page.po.SaasIntentionCustomerCluePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SaasIntentionCustomerClueMapper {
    /**
            * 通过ID查询单条数据
     *
             * @param id 主键
     * @return 实例对象
     */
    SaasIntentionCustomerCluePo queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @param saasIntentionCustomerClue 查询条件
     * @return 对象列表
     */
    List<SaasIntentionCustomerCluePo> queryByCondition(SaasIntentionCustomerCluePo saasIntentionCustomerClue);

    /**
     * 统计总行数
     *
     * @param saasIntentionCustomerClue 查询条件
     * @return 总行数
     */
    long count(SaasIntentionCustomerCluePo saasIntentionCustomerClue);

    /**
     * 新增数据
     *
     * @param saasIntentionCustomerClue 实例对象
     * @return 影响行数
     */
    int insertSelective(SaasIntentionCustomerCluePo saasIntentionCustomerClue);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<SaasIntentionCustomerClue> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<SaasIntentionCustomerCluePo> list);


    /**
     * 修改数据
     *
     * @param saasIntentionCustomerClue 实例对象
     * @return 影响行数
     */
    int updateSelective(SaasIntentionCustomerCluePo saasIntentionCustomerClue);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);
}
