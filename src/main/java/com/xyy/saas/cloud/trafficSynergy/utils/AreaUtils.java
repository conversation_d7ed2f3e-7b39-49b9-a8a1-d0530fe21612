package com.xyy.saas.cloud.trafficSynergy.utils;

import com.alibaba.dubbo.config.annotation.Reference;
import com.xyy.saas.common.api.SaasAreaApi;
import com.xyy.saas.common.dto.SaasAreaDto;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName AreaUtils
 * @Description: TODO
 * <AUTHOR>
 * @Date 2019/11/29 13:54
 **/
@Component
public class AreaUtils {

    @Reference(version = "0.0.1")
    private SaasAreaApi saasAreaApi;

    public Map<Integer,String> getAreaListByParentId(Integer parentId){
        if(StringUtils.isEmpty(parentId)){
            return null;
        }
        SaasAreaDto saasAreaDto = new SaasAreaDto();
        Map<Integer,String> areaMap = new HashMap();
        //获取所有省份信息
        saasAreaDto.setParentId(parentId);
        List<SaasAreaDto> nameListByParentId = saasAreaApi.getNameListByParentId(saasAreaDto);
        nameListByParentId.stream().forEach(data->{
            areaMap.put(data.getId(),data.getAreaName());
        });
        return areaMap;
    }

}
