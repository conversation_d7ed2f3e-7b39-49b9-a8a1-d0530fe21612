package com.xyy.saas.cloud.trafficSynergy.constant;

import java.util.HashMap;

public enum ECRregisterUserEnabledEnum {
    ENABLED_YES(1,"已激活"),ENABLED_NO(2,"未激活");
    private int key;

    private String name;

    private ECRregisterUserEnabledEnum(int key,String name) {
        this.key = key;
        this.name=name;
    }

    public int getKey()
    {
        return this.key;
    }

    public static ECRregisterUserEnabledEnum getECRregisterUserEnabledEnum(int key) {
        for (ECRregisterUserEnabledEnum j : ECRregisterUserEnabledEnum.values()) {
            if (j.getKey() == key) {
                return j;
            }
        }
        return null;
    }

    public static String getECRregisterUserEnabledEnumName(int key) {
        for (ECRregisterUserEnabledEnum j : ECRregisterUserEnabledEnum.values()) {
            if (j.getKey() == key) {
                return j.name;
            }
        }
        return "-";
    }
    //将所有状态一起封装到HashMap中
    public static HashMap<String, String> valueMap = new HashMap<String, String>();
    //分装
    static{
        for (ECRregisterUserEnabledEnum item : ECRregisterUserEnabledEnum.values()) {
            valueMap.put(String.valueOf(item.getKey()), item.name);
        }
    }
}
