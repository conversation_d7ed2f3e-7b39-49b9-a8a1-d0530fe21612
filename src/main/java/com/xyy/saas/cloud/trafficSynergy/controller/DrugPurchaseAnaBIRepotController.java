package com.xyy.saas.cloud.trafficSynergy.controller;

import com.xyy.saas.base.AbstractCommonController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * @ClassName DrugstorePurchaseAnaRepot
 * @Description: 药品采销分析报表
 * <AUTHOR>
 * @Date 2019/11/5 14:29
 **/
@Controller
@RequestMapping("/trafficSynergy")
public class DrugPurchaseAnaBIRepotController extends AbstractCommonController {

    //药品采销分析报表页面
    @RequestMapping(value = "/drugPurchaseAnaBIRepot",method = RequestMethod.GET)
    public String competitionAnalysis(){
        return "trafficSynergy/drugPurchaseAnaBIRepot";
    }
}
