package com.xyy.saas.cloud.trafficSynergy.constant;

import java.util.HashMap;

/*
 * @Description: 资质状态
 * @Author: jiajingjing
 * @Date: 2019/9/12
**/
public enum ECRregisterUserQualificationStatusEnum {
    QUALIFICATIONSTATUS_NO_SUBMIT(1,"资质未提交"),QUALIFICATIONSTATUS_SUBMIT(2,"资质已提交"),
    /*QUALIFICATIONSTATUS_UNRECOVERED(2,"首营纸质资质未回收"),*/QUALIFICATIONSTATUS_PASS(4,"资质已通过");
    private int key;

    private String name;

    private ECRregisterUserQualificationStatusEnum(int key,String name) {
        this.key = key;
        this.name=name;
    }

    public int getKey()
    {
        return this.key;
    }
    /*
     * @Description: 返回对应类
     * @Param: [key]
     * @Return: com.xyy.saas.cloud.page.constant.CouponsRuleTypeEnum
     * @Author: jiajingjing
     * @Date: 2019/9/12
    **/
    public static ECRregisterUserQualificationStatusEnum getECRregisterUserQualificationStatusEnum(int key) {
        for (ECRregisterUserQualificationStatusEnum j : ECRregisterUserQualificationStatusEnum.values()) {
            if (j.getKey() == key) {
                return j;
            }
        }
        return null;
    }
    /*
     * @Description: 返回对应类value值
     * @Param: [key]
     * @Return: java.lang.String
     * @Author: jiajingjing
     * @Date: 2019/9/12
    **/
    public static String getECRregisterUserQualificationStatusEnumName(int key) {
        for (ECRregisterUserQualificationStatusEnum j : ECRregisterUserQualificationStatusEnum.values()) {
            if (j.getKey() == key) {
                return j.name;
            }
        }
        return "-";
    }

    //将所有状态一起封装到HashMap中
    public static HashMap<String, String> valueMap = new HashMap<String, String>();
    //分装
    static{
        for (ECRregisterUserQualificationStatusEnum item : ECRregisterUserQualificationStatusEnum.values()) {
            valueMap.put(String.valueOf(item.getKey()), item.name);
        }
    }
}
