package com.xyy.saas.cloud.trafficSynergy.controller;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xyy.saas.base.AbstractCommonController;
import com.xyy.saas.cloud.trafficSynergy.constant.BiLoginInfo;
import com.xyy.saas.util.HttpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * @ClassName DrugstorePurchaseAnaRepot
 * @Description: 药店采销分析报表
 * <AUTHOR>
 * @Date 2019/11/5 14:29
 **/
@Controller
@RequestMapping("/trafficSynergy")
public class DrugstorePurchaseAnaBIRepotController  extends AbstractCommonController {

    private Logger logger = LoggerFactory.getLogger(DrugstorePurchaseAnaBIRepotController.class);

    private final static ObjectMapper objectMapper = new ObjectMapper();

    @Value("${cloud.server.biLoginUrl}")
    private String biLoginUrl;
    @Value("${biInfo.loginName}")
    private String loginName;
    @Value("${biInfo.password}")
    private String password;


    //跳转到返券规则列表页面
    @RequestMapping(value = "/drugstorePurchaseAnaBIRepot",method = RequestMethod.GET)
    public String competitionAnalysis(){
        return "trafficSynergy/drugstorePurchaseAnaBIRepot";
    }

    @RequestMapping(value = "/getBiCode")
    @ResponseBody
    public String competitionAnalysis(HttpServletRequest request){
        String loginUserName = getLoginUserName();
        Map<String, Object> data = biLogin(loginName, password,loginUserName);
        JSONObject json = new JSONObject(data);
        return json.toString();
    }

    //,
    private Map<String, Object> biLogin(String account, String password,String loginUserName) {
        try {
            String resInfo = HttpUtil
                    .getAsString(biLoginUrl + "/api/login/dataLogin?account="
                            + account
                            + "&password="
                            + password
                            + "&username="
                            + loginUserName);
            logger.info(String.format("解析前远程调用药帮忙返回数据：%s",resInfo));
            Map<String, Object> messageInfo = null;
            messageInfo = objectMapper.readValue(resInfo, Map.class);
            logger.info("解析远程调用返回数据结果，结果集为:" + messageInfo.toString());
            if (messageInfo.get("result")!=null) {
                return messageInfo;
            } else {
                return null;
            }
        } catch (Exception e) {
            logger.info("远程调用接口失败或结果解析失败,异常信息:" , e);
            return null;
        }
    }
}
