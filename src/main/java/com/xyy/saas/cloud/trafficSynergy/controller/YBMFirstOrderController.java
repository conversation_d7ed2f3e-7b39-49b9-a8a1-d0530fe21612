package com.xyy.saas.cloud.trafficSynergy.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.cloud.trafficSynergy.utils.AreaUtils;
import com.xyy.saas.cloud.trafficSynergy.utils.ProvinceCodeConvert;
import com.xyy.saas.common.api.SaasAreaApi;
import com.xyy.saas.common.dto.SaasAreaDto;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.util.ExportExcelUtil;
import com.xyy.user.module.api.ECRegisterUserApi;
import com.xyy.user.module.dto.ECFirstAndDealOrderDto;
import com.xyy.user.module.dto.ECOrderDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName YBMFirstOrder
 * @Description: Saas流量协同中心-拉首单
 * <AUTHOR>
 * @Date 2019/9/11 16:38
 **/
@Controller
@RequestMapping(value = "/trafficSynergy")
public class YBMFirstOrderController {

    @Reference(version = "0.0.1")
    private ECRegisterUserApi ecRegisterUserApi;

    @Autowired
    private AreaUtils areaUtils;

    //跳转到Saas流量协同中心-拉首单页面
    @RequestMapping(value = "/ybmFirstOrder",method = RequestMethod.GET)
    public String competitionAnalysis(){
        return "trafficSynergy/ybmFirstOrder";
    }

    /**
     * 拉首单列表
     * @return
     */
    @RequestMapping(value = "/ybmFirstOrderList",method = RequestMethod.GET)
    @ResponseBody
    public ResultVO ybmFirstOrderList(Integer rows, Integer page, ECFirstAndDealOrderDto ecFirstAndDealOrderDto) {
        if (page == null) {
            page = 1;
        }
        if (rows == null) {
            rows = 50;
        }
        if(StringUtils.isEmpty(ecFirstAndDealOrderDto.getStartCreateTime())){
            return new com.xyy.saas.common.util.ResultVO(ResultCodeEnum.ERROR, "请填写默认起始时间，默认一个月...");
        }
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(rows);
        ResultVO<ECOrderDto> pageInfoResultVO = ecRegisterUserApi.ybmFirstOrderList(pageInfo, ecFirstAndDealOrderDto);
        return pageInfoResultVO;
    }


    @RequestMapping(value = "/exportOrderExcel",method = RequestMethod.GET)
    public void exportRecordExcel(HttpServletResponse response, HttpServletRequest request,Integer rows, Integer page, ECFirstAndDealOrderDto ecFirstAndDealOrderDto){
        String name="";
        //获取所有省份信息
        Map<Integer,String> areaMap =areaUtils.getAreaListByParentId(0);
        String province = "全国";
        if(ecFirstAndDealOrderDto.getBranchCode() != null){
            province = areaMap.get(ProvinceCodeConvert.convert(ecFirstAndDealOrderDto.getBranchCode().toString()));
        }

        if(ecFirstAndDealOrderDto.getFirstOrderFlag() != null){
            name=ecFirstAndDealOrderDto.getFirstOrderFlag()==1?"首单":"交易";
        }
        //最终结果集
        List<ECFirstAndDealOrderDto> resultList = new ArrayList();
        if (page == null) {
            page = 1;//分页当前页
        }
        if (rows == null) {
            rows = 100; //分页每页数量
        }
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(rows);
        ResultVO<ECOrderDto> pageInfoResultVO = ecRegisterUserApi.ybmFirstOrderList(pageInfo, ecFirstAndDealOrderDto);
        if(pageInfoResultVO.getResult().getECFirstAndDealOrderDtoList() != null){
            resultList.addAll(pageInfoResultVO.getResult().getECFirstAndDealOrderDtoList());
        }
        //总页数
        int pages = pageInfoResultVO.getResult().getPages();
        //分页获取信息
        while (page<pages){
            page+=1;
            pageInfo.setPageNum(page);
            ResultVO<ECOrderDto> nextResultVO = ecRegisterUserApi.ybmFirstOrderList(pageInfo, ecFirstAndDealOrderDto);
            if(nextResultVO.getResult().getECFirstAndDealOrderDtoList() != null){
                resultList.addAll(nextResultVO.getResult().getECFirstAndDealOrderDtoList());
            }
        }
        resultList.stream().forEach(data->{
            if(data.getBranchCode() != null && areaMap != null){
                data.setEcProvince(areaMap.get(ProvinceCodeConvert.convert(data.getBranchCode().toString())));
            }
        });
        String extfilename=ecFirstAndDealOrderDto.getStartCreateTime().replaceAll("-","")+"-"+ecFirstAndDealOrderDto.getEndCreateTime().replaceAll("-","")+" "+province+"助力药帮忙"+name+".xls";
        String sheetName = ecFirstAndDealOrderDto.getStartCreateTime().replaceAll("-","")+"-"+ecFirstAndDealOrderDto.getEndCreateTime().replaceAll("-","")+" "+province+"助力药帮忙"+name;
        String headers[] = new String[]{"门店编码","药店名称","省份","城市","区县","来源路径","门店编码","省份","订单编号","创建时间","含税金额（优惠前）",
                "优惠金额","实付金额","退款金额"};
        String fieldNames[] = new String[]{"organSign","drugstoreName", "province","city","area","sourcePath","ybmCode","ecProvince","orderNo", "creatTime"
                ,"totalAmount","discount" ,"money","refundAmount"};
        try {
            ExportExcelUtil.createExcel(response,request,extfilename,sheetName, headers, fieldNames, resultList,true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
