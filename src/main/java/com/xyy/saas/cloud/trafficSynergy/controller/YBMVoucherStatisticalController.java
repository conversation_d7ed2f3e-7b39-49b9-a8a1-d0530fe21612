package com.xyy.saas.cloud.trafficSynergy.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.cloud.website.vo.YbmVoucherStatisticalVO;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.util.ExportExcelUtil;
import com.xyy.user.module.api.ECVoucherApi;
import com.xyy.user.module.dto.ECVoucherDto;
import com.xyy.user.module.dto.ECVoucherSummaryDto;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Controller
@RequestMapping(value = "/voucher/statistical")
public class YBMVoucherStatisticalController {

    private static final Logger logger = LogManager.getLogger(YBMVoucherStatisticalController.class);

    @Reference(version = "0.0.1")
    private ECVoucherApi ecVoucherApi;


    //跳转优惠券使用统计页面
    @RequestMapping(value = "/index",method = RequestMethod.GET)
    public String ybmVoucherStatisticalIndex() {
        return "trafficSynergy/ybmVoucherStatistical";
    }

    /**
     * 优惠卷统计查询
     * @param rows
     * @param page
     * @param ybmVoucherStatisticalVO
     * @return
     */
    @RequestMapping(value = "/query",method = RequestMethod.GET)
    @ResponseBody
    public ResultVO<PageInfo<ECVoucherDto>> ybmDealList(Integer rows, Integer page, YbmVoucherStatisticalVO ybmVoucherStatisticalVO) {
        if (page == null) {
            page = 1;
        }
        if (rows == null) {
            rows = 100;
        }
        if (ybmVoucherStatisticalVO.getStartCreateDate() == null && ybmVoucherStatisticalVO.getEndCreateDate() == null) {
            return new ResultVO(ResultCodeEnum.ERROR, "默认查询一个月数据，开始领取-结束领取时间不能为空。");
        }
        ECVoucherDto ecVoucherDto = new ECVoucherDto();
        BeanUtils.copyProperties(ybmVoucherStatisticalVO, ecVoucherDto);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(rows);
        logger.info("优惠卷查询YbmVoucherStatisticalVO为:" + ybmVoucherStatisticalVO);
        PageInfo<ECVoucherDto> info = ecVoucherApi.queryPageECVoucher(ecVoucherDto, pageInfo);
        return new ResultVO<>(info);
    }

    /**
     * 优惠卷汇总统计
     *
     * @param ybmVoucherStatisticalVO
     * @return
     */
    @RequestMapping(value = "/summary",method = RequestMethod.POST)
    @ResponseBody
    public ResultVO<ECVoucherSummaryDto> summaryYbmVoucherStatistical(@RequestBody YbmVoucherStatisticalVO ybmVoucherStatisticalVO) {
        if (ybmVoucherStatisticalVO.getStartCreateDate() == null && ybmVoucherStatisticalVO.getEndCreateDate() == null) {
            return new ResultVO(ResultCodeEnum.ERROR, "默认查询一个月数据，开始领取-结束领取时间不能为空。");
        }
        ECVoucherDto ecVoucherDto = new ECVoucherDto();
        BeanUtils.copyProperties(ybmVoucherStatisticalVO, ecVoucherDto);
        logger.info("优惠卷汇总统计YbmVoucherStatisticalVO为:" + ybmVoucherStatisticalVO);
        ECVoucherSummaryDto summaryDto = ecVoucherApi.summaryECVoucher(ecVoucherDto);
        return new ResultVO(summaryDto);
    }

    /**
     * 优惠卷导出
     *
     * @param ybmVoucherStatisticalVO
     * @return
     */
    @RequestMapping(value = "/excel",method = RequestMethod.GET)
    public void excelYbmVoucherStatistical(HttpServletRequest request, HttpServletResponse response,  YbmVoucherStatisticalVO ybmVoucherStatisticalVO) {
        ECVoucherDto ecVoucherDto = new ECVoucherDto();
        BeanUtils.copyProperties(ybmVoucherStatisticalVO, ecVoucherDto);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(50000);
        logger.info("优惠卷导出YbmVoucherStatisticalVO为:" + ybmVoucherStatisticalVO);
        PageInfo<ECVoucherDto> info = ecVoucherApi.queryPageECVoucher(ecVoucherDto, pageInfo);
        List<ECVoucherDto> list = info.getList();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String extfilename = "优惠券使用统计" + df.format(new Date()) + ".xls";
        String sheetName = "优惠券使用统计";
        String[] headers = new String[]{"智慧脸药店名称", "智慧脸门店编号", "达标版本号", "药帮忙门店编号", "药帮忙ID", "省份",
                "城市", "区县", "用户卷ID", "领取时间", "有效期", "券状态", "使用时间", "订单编号", "订单总额",
                "商品总额", "券后优惠金额", "券模板ID", "优惠券名称", "券文案", "券类型", "券面额", "使用条件",
                "领取总量", "使用总量"};
        String[] fieldNames = new String[]{"drugstoreName", "organSign", "standardsRuleVersionName", "syncNo", "merchantId", "province",
                "city", "district", "voucherId", "startCreateDate", "validityTime", "stateName", "startUpdateTime", "orderNo", "orderTotalAmount",
                "voucherSkuPriceSum", "orderDiscountAmount", "templateId", "templateName", "voucherTitle", "voucherTypeName", "moneyInVoucher", "moneyToEnable",
                "totalLimitReceive", "totalLimitUse"};
        try {
            ExportExcelUtil.createExcel(response, request, extfilename, sheetName, headers, fieldNames, list, true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
