package com.xyy.saas.cloud.trafficSynergy.controller;

import com.alibaba.dubbo.config.annotation.Reference;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.cloud.trafficSynergy.constant.ECRregisterUserEnabledEnum;
import com.xyy.saas.cloud.trafficSynergy.constant.ECRregisterUserQualificationStatusEnum;
import com.xyy.saas.cloud.trafficSynergy.utils.AreaUtils;
import com.xyy.saas.cloud.trafficSynergy.utils.ProvinceCodeConvert;
import com.xyy.saas.common.dto.SaasAreaDto;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.util.ExportExcelUtil;
import com.xyy.user.module.api.ECRegisterUserApi;
import com.xyy.user.module.dto.ECFirstAndDealOrderDto;
import com.xyy.user.module.dto.ECOrderDto;
import com.xyy.user.module.dto.ECRegisterUserDto;
import com.xyy.user.module.dto.ECRegisterUserRespDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName YBMNewUser
 * @Description: Saas流量协同中心-拉新
 * <AUTHOR>
 * @Date 2019/9/11 16:37
 **/
@Controller
@RequestMapping(value = "/trafficSynergy")
    public class YBMNewUserController {


    @Reference(version = "0.0.1")
    private ECRegisterUserApi ecRegisterUserApi;

    @Autowired
    private AreaUtils areaUtils;

    //跳转到Saas流量协同中心-拉新页面
    @RequestMapping(value = "/ybmNewUser",method = RequestMethod.GET)
    public String competitionAnalysis(Model model){
        //资质状态
        model.addAttribute("status",ECRregisterUserQualificationStatusEnum.valueMap);
        //激活状态
        model.addAttribute("enabled",ECRregisterUserEnabledEnum.valueMap);
        return "trafficSynergy/ybmNewUser";
    }

    /**
     * 拉新列表
     * @return
     */
    @RequestMapping(value = "/ybmNewUserList",method = RequestMethod.GET)
    @ResponseBody
    public ResultVO<ECRegisterUserRespDto> ybmNewUserList(Integer rows, Integer page,ECRegisterUserDto ecRegisterUserDto) {
        if (page == null) {
            page = 1;
        }
        if (rows == null) {
            rows = 50;
        }

        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(rows);
        ResultVO<ECRegisterUserRespDto> pageInfoResultVO = ecRegisterUserApi.ybmNewUserList(pageInfo, ecRegisterUserDto);
        return pageInfoResultVO;
    }

    @RequestMapping(value = "/exportUserExcel",method = RequestMethod.GET)
    public void exportRecordExcel(HttpServletResponse response, HttpServletRequest request,Integer rows, Integer page, ECRegisterUserDto ecRegisterUserDto){
        //最终结果集
        List<ECRegisterUserDto> resultList = new ArrayList();
        //获取所有省份信息
        Map<Integer,String> areaMap =areaUtils.getAreaListByParentId(0);
        String province = "全国";
        if(ecRegisterUserDto.getBranchCode()!=null){
            province = areaMap.get(ProvinceCodeConvert.convert(ecRegisterUserDto.getBranchCode().toString()));
        }
        if (page == null) {
            page = 1;//分页当前页
        }
        if (rows == null) {
            rows = 100; //分页每页数量
        }
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(rows);
        ResultVO<ECRegisterUserRespDto> pageInfoResultVO = ecRegisterUserApi.ybmNewUserList(pageInfo, ecRegisterUserDto);
        if(pageInfoResultVO.getResult().getList() != null){
            resultList.addAll(pageInfoResultVO.getResult().getList());
        }
        //总页数
        int pages = pageInfoResultVO.getResult().getPages();
        //分页获取信息
        while (page<pages){
            page+=1;
            pageInfo.setPageNum(page);
            ResultVO<ECRegisterUserRespDto> nextResultVO = ecRegisterUserApi.ybmNewUserList(pageInfo, ecRegisterUserDto);
            if(nextResultVO.getResult().getList() != null){
                resultList.addAll(nextResultVO.getResult().getList());
            }
        }
        //资质状态
        HashMap<String, String> qualificationStatusMap = ECRregisterUserQualificationStatusEnum.valueMap;
        //激活状态
        HashMap<String, String> enabledMap = ECRregisterUserEnabledEnum.valueMap;
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
        resultList.stream().forEach(data->{
            if(data.getRegisterCode() != null && areaMap != null){
                String name = data.getRegisterCode().substring(2);
                data.setEcProvince(areaMap.get(ProvinceCodeConvert.convert(name)));
            }
            if(!StringUtils.isEmpty(data.getQualificationStatus())){
                data.setStrQualificationStatus(qualificationStatusMap.get(String.valueOf(data.getQualificationStatus())));
            }
            if(!StringUtils.isEmpty(data.getEnabled())){
                data.setStrEnabled(enabledMap.get(String.valueOf(data.getEnabled())));
            }
            if(!StringUtils.isEmpty(data.getYbmRegisterTime())){
                data.setYbmDateRegisterTime(sd.format(new Date(data.getYbmRegisterTime())));
            }
            if(!StringUtils.isEmpty(data.getDrugstoreCreateTime())){
                data.setDrugstoreTime(sd.format(data.getDrugstoreCreateTime()));
            }
//            data.setSourcePath("智慧脸管理端-绑定窗口-注册链接");
        });
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");//设置日期格式
        Date start = new Date(ecRegisterUserDto.getStartTime());
        String startTime = format.format(start);
        Date end = new Date(ecRegisterUserDto.getEndTime());
        String endTime = format.format(end);

        String extfilename=startTime+"-"+endTime+" "+province+"助力药帮忙拉新"+".xls";
        String sheetName = startTime+"-"+endTime+" "+province+"助力药帮忙拉新";
        String headers[] = new String[]{"门店编码","药店名称","省份","城市","区县","注册时间","来源路径","手机号码","省份","注册时间","激活状态","资质状态"};
        String fieldNames[] = new String[]{"organSign","drugstoreName", "province","city","area","drugstoreTime","sourcePath","ybmUserName","ecProvince","ybmDateRegisterTime", "strEnabled","strQualificationStatus"};
        try {
            ExportExcelUtil.createExcel(response,request,extfilename,sheetName, headers, fieldNames, resultList,true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
