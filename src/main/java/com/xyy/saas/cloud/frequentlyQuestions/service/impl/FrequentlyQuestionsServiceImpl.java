package com.xyy.saas.cloud.frequentlyQuestions.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.cloud.frequentlyQuestions.dao.FrequentlyQuestionsMapper;
import com.xyy.saas.cloud.frequentlyQuestions.po.FrequentlyQuestionsPo;
import com.xyy.saas.cloud.frequentlyQuestions.vo.FrequentlyQuestionsVo;
import com.xyy.saas.cloud.frequentlyQuestions.service.FrequentlyQuestionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @program: saas-cloud
 * @description: ${description}
 * @author: cbx
 * @create: 2018-08-02 16:57
 **/
@Service
public class FrequentlyQuestionsServiceImpl implements FrequentlyQuestionsService {

    @Autowired
    private FrequentlyQuestionsMapper frequentlyQuestionsMapper;
    @Override
    public PageInfo<FrequentlyQuestionsPo> findPage(FrequentlyQuestionsVo record, PageInfo pageInfo) {
        PageHelper.startPage(pageInfo.getPageNum(), pageInfo.getPageSize());

        return new PageInfo(frequentlyQuestionsMapper.findList(record));
    }

    @Override
    public PageInfo<FrequentlyQuestionsPo> queryFrequentlyQuestionsPos(FrequentlyQuestionsVo record, PageInfo pageInfo) {
        PageHelper.startPage(pageInfo.getPageNum(), pageInfo.getPageSize());
        List<FrequentlyQuestionsPo> list = frequentlyQuestionsMapper.findList1(record);
        if (list!=null&&list.size()>0){
            for(FrequentlyQuestionsPo frequentlyQuestionsPo:list){
                String count = frequentlyQuestionsPo.getContent();
                count = count.length()>50?count.substring(0,50):count;
                frequentlyQuestionsPo.setSummary(count);
            }
        }
        return new PageInfo(list);
    }

    @Override
    public int insert(FrequentlyQuestionsPo record) {
        return frequentlyQuestionsMapper.insertSelective(record);
    }

    @Override
    public FrequentlyQuestionsPo selectByPrimaryKey(Integer id) {
        return frequentlyQuestionsMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKey(FrequentlyQuestionsPo record) {
        return frequentlyQuestionsMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int deleteByPrimaryKey(Integer id) {
        return frequentlyQuestionsMapper.deleteByPrimaryKey(id);
    }
}
