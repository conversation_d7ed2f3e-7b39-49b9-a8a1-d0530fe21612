package com.xyy.saas.cloud.instructionsBook.dao;
import com.xyy.saas.cloud.instructionsBook.po.InstructionsBookPo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
@Mapper
public interface InstructionsBookMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(InstructionsBookPo record);

    int insertSelective(InstructionsBookPo record);

    InstructionsBookPo selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(InstructionsBookPo record);

    int updateByPrimaryKey(InstructionsBookPo record);

    List findList(InstructionsBookPo record);
}