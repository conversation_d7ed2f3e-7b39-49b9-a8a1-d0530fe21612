package com.xyy.saas.cloud.thirdExcel.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 药品名称、规格、生产厂家、批准文号、单位、条形码、订单号、供应商名称、订单时间、批号、生产日期、有效期至、入库数、进货价（优惠前）、进货价（优惠后）
 * @Author:chenxiaoyi
 * @Date:2023/09/12 10:59
 */
@Data
public class ThirdImportConfigVo implements Serializable {

    /**
     * 角标
     */
    private int index;

    /**
     * 三方字段
     */
    private String thirdName;

    /**
     * 我们字段
     */
    private String field;

    /**
     * 我们字段名称
     */
    private String fieldName;

    /**
     * 字段是否必填 默认false
     */
    private boolean fieldRequire;

    /**
     * 字段类型
     */
    private String type;

    /**
     * 长度校验 eg ： 字符型: 长度不能超过90,默认字符类型基础长度100
     * 数字形式,不能超过 10000000  (1000w)
     */
    private Integer valid;

}
