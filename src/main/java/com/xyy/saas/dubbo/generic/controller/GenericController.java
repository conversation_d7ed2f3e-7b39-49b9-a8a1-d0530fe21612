package com.xyy.saas.dubbo.generic.controller;

import com.xyy.saas.util.DubboGenericUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Controller
@RequestMapping(value = "/dubbo/generic")
public class GenericController {

    @Resource
    private DubboGenericUtil dubboGenericUtil;

    // dubbo 泛型调用入口
    @PostMapping(value = {
            "/{registry}/{interface}/{version}/{group}/{method}",
            "/{registry}/{interface}/{version}/{method}"
    })
    @ResponseBody
    public Object call(@PathVariable(value = "registry") String registry,
                       @PathVariable(value = "interface") String interfaceName,
                       @PathVariable(value = "version") String version,
                       @PathVariable(value = "group", required = false) String group,
                       @PathVariable(value = "method") String method,
                       @RequestBody Param param
                       ) {
        // 泛化调用
        return dubboGenericUtil.invoke(registry, interfaceName, version, group, method, param);
    }

    @Data
    @Accessors(chain = true)
    public static class Param {
        private String[] typeList;
        private Object[] valueList;

        public Param typeList(String... typeList) {
            this.typeList = typeList;
            return this;
        }

        public Param valueList(Object... valueList) {
            this.valueList = valueList;
            return this;
        }
    }
}
