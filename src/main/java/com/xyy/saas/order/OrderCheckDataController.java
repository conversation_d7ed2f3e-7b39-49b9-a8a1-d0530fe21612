package com.xyy.saas.order;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.base.AbstractCommonController;
import com.xyy.saas.base.ResultVO;
import com.xyy.saas.cloud.page.dao.PositionPoMapper;
import com.xyy.saas.cloud.page.vo.LoginUser;
import com.xyy.saas.config.RedisService;
import com.xyy.saas.order.core.DataCheckScheduleDto;
import com.xyy.saas.order.core.api.CheckDataApi;
import com.xyy.saas.order.core.api.SalesOrderApi;
import com.xyy.saas.order.core.dto.SaasPreOrderDetailDto;
import com.xyy.saas.order.core.dto.SaasPreOrderDto;
import com.xyy.saas.order.sharding.core.api.report.SalesRecordFormsReportApi;
import com.xyy.saas.order.sharding.core.dto.CreateReturnOrderDto;
import com.xyy.saas.order.vo.CreateReturnOrderDetailVo;
import com.xyy.saas.order.vo.CreateReturnOrderVo;
import com.xyy.saas.purchase.core.dto.thirdparty.HeyeOrderSourceDto;
import com.xyy.saas.purchase.core.dto.thirdparty.OrderSourceDto;
import com.xyy.saas.purchase.core.api.thirdparty.ThirdOrderApi;
import com.xyy.saas.purchase.core.dto.thirdparty.ThirdOrderSourceDto;
import com.xyy.saas.util.BeanUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.websocket.server.PathParam;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/order")
public class OrderCheckDataController extends AbstractCommonController {

    private static final Logger logger = LoggerFactory.getLogger(OrderCheckDataController.class);

    @Reference(version = "0.0.1")
    private CheckDataApi checkDataApi;

    @Reference(version = "0.0.1")
    private ThirdOrderApi thirdOrderApi;


    @Reference(version = "0.0.1")
    private SalesRecordFormsReportApi salesRecordFormsReportApi;

    @Reference(version = "0.0.1")
    private SalesOrderApi salesOrderApi;

    @Autowired
    private RedisService redisService;


    @Value("#{ T(org.apache.commons.lang3.StringUtils).split('${query.OrderInfo.One.OrganSign.Config:}',',')}")
    private List<String> queryOrderInfoOneOrganSignConfig;




    @RequestMapping(value = "/queryCheckDataResult",method = RequestMethod.POST)
    public ResultVO<Object> getMemberSmsPackageList(@RequestBody DataCheckScheduleDto dataCheckScheduleDto){
        logger.info("检查数据参数的值:{}", JSON.toJSON(dataCheckScheduleDto));
        com.xyy.saas.common.util.ResultVO<Object> data = checkDataApi.checkSaasOrderInfoData(dataCheckScheduleDto);
        logger.info("检查数据返回的值:{}", JSON.toJSON(data));
        return ResultVO.createSuccess(data.getResult());
       //return ResultVO.createSuccess(dataCheckScheduleDto);
    }




    @RequestMapping(value = "/queryOrderInfoOne",method = RequestMethod.POST)
    public ResultVO<Object> queryOrderInfoOne(@RequestBody CreateReturnOrderVo createReturnOrderVo){

        logger.info("queryOrderInfoOne.in->param"+ JSON.toJSON(createReturnOrderVo));


        if(StringUtils.isBlank(createReturnOrderVo.getOrganSign())){
            return ResultVO.createError("机构编码为空，请检查！");
        }

        if(StringUtils.isBlank(createReturnOrderVo.getTicketNo()) && StringUtils.isBlank(createReturnOrderVo.getCommerceOrderNumber())){
            return ResultVO.createError("小票号或三方订单号不能都为空!");
        }


        if(!queryOrderInfoOneOrganSignConfig.contains(createReturnOrderVo.getOrganSign())){
            return ResultVO.createError("当前机构不可查询");
        }

        if(createReturnOrderVo.getPageNum() == null){
            createReturnOrderVo.setPageNum(1);
        }
        if(createReturnOrderVo.getPageSize() == null){
            createReturnOrderVo.setPageSize(50);
        }

        PageInfo<?> pageInfo = new PageInfo<>();
        pageInfo.setPageNum(createReturnOrderVo.getPageNum());
        pageInfo.setPageSize(createReturnOrderVo.getPageSize());

        CreateReturnOrderDto createReturnOrderDto = new CreateReturnOrderDto();
        BeanUtils.copyProperties(createReturnOrderVo,createReturnOrderDto);
        Map<String,Object> res =  salesRecordFormsReportApi.queryOrderInfoOneOnCloud(pageInfo,createReturnOrderDto);
        logger.info("queryOrderInfoOne.out->param"+ JSON.toJSON(res));
        if(CollectionUtils.isEmpty(res)){
            return ResultVO.createError("此订单号不存在，请检查");
        }


        return ResultVO.createSuccess(res);
    }


    @RequestMapping(value = "/queryOrderInfoOneDetail",method = RequestMethod.POST)
    public ResultVO<Object> queryOrderInfoOneDetail(@RequestBody CreateReturnOrderVo createReturnOrderVo){

        logger.info("queryOrderInfoOneDetail.in->param"+ JSON.toJSON(createReturnOrderVo));


        if(StringUtils.isAnyBlank(createReturnOrderVo.getOrganSign(),createReturnOrderVo.getTicketNo())){
            return ResultVO.createError("机构编码或小票号为空，请检查！");
        }

        if(!queryOrderInfoOneOrganSignConfig.contains(createReturnOrderVo.getOrganSign())){
            return ResultVO.createError("当前机构不可查询");
        }

        CreateReturnOrderDto createReturnOrderDto = new CreateReturnOrderDto();
        BeanUtils.copyProperties(createReturnOrderVo,createReturnOrderDto);
        Map<String,Object> res =  salesRecordFormsReportApi.queryOrderInfoOneDetailOnCloud(createReturnOrderDto);
        logger.info("queryOrderInfoOneDetail.out->param"+ JSON.toJSON(res));
        if(CollectionUtils.isEmpty(res) || res.get("list") == null){
            return ResultVO.createError("查询不到订单明细信息！");
        }

        return ResultVO.createSuccess(res);
    }

    @RequestMapping(value = "/returnOrderUpload",method = RequestMethod.POST)
    public ResultVO<Object> returnOrderUpload(@RequestBody CreateReturnOrderVo createReturnOrderVo){
        logger.info("returnOrderUpload.in->param" + JSON.toJSON(createReturnOrderVo));
         if(!checkInParam(createReturnOrderVo)){
             return ResultVO.createError("必填参数为空，请检查！");

         }
        String lockKey = "saas:cloud:returnOrderUpload:" + createReturnOrderVo.getOrganSign() + ":" + createReturnOrderVo.getTicketNo();
        String requestId = RandomStringUtils.randomAlphanumeric(8);
         try {
             if(!redisService.tryGetDistributedLock(lockKey,requestId,50)){
                 return ResultVO.createError("请勿重复提交！");
             }
             SaasPreOrderDto saasPreOrderDto =  buildSaasPreOrderDto(createReturnOrderVo);
             com.xyy.saas.common.util.ResultVO resultVO = salesOrderApi.uploadOrderDataOtherCloud(saasPreOrderDto);
             logger.info("returnOrderUpload.out->param"+ JSON.toJSON(resultVO));
             if(resultVO.getCode() != 0){
                 return ResultVO.createError(resultVO.getMsg());
             }
         }catch (Exception e){
             logger.error("returnOrderUpload->error,e:{}",e.getMessage(),e);
             return ResultVO.createError("下账失败！");
         }finally {
             redisService.releaseDistributedLock(lockKey,requestId);
         }
        return ResultVO.createSuccess("下账成功!");
    }

    private SaasPreOrderDto buildSaasPreOrderDto(CreateReturnOrderVo createReturnOrderVo) {
        SaasPreOrderDto saasPreOrderDto = new SaasPreOrderDto();
        saasPreOrderDto.setOrganSign(createReturnOrderVo.getOrganSign());
        saasPreOrderDto.setTicketNo(createReturnOrderVo.getTicketNo());
        saasPreOrderDto.setBussinessType(StringUtils.EMPTY);
        LoginUser user = getLoginUserVo();
        saasPreOrderDto.setOrderRemark("此退款单从智慧脸云平台操作退款"+ (user != null ? "，操作人：" +user.getRealName() : ""));
        List<SaasPreOrderDetailDto> detailDtoList = new ArrayList<>();
        for (CreateReturnOrderDetailVo detailVo : createReturnOrderVo.getDetailDtoList()) {
            SaasPreOrderDetailDto saasPreOrderDetailDto = new SaasPreOrderDetailDto();
            saasPreOrderDetailDto.setProductCode(detailVo.getProductCode());
            saasPreOrderDetailDto.setPositionId(detailVo.getPositionId());
            saasPreOrderDetailDto.setLotNumber(detailVo.getBatchNo());
            saasPreOrderDetailDto.setExitNumber(detailVo.getExitNumber());
            detailDtoList.add(saasPreOrderDetailDto);
        }
        saasPreOrderDto.setDetailDtoList(detailDtoList);
        return saasPreOrderDto;

    }

    private boolean checkInParam(CreateReturnOrderVo createReturnOrderVo) {
        if(createReturnOrderVo == null  || StringUtils.isBlank(createReturnOrderVo.getOrganSign()) || StringUtils.isBlank(createReturnOrderVo.getTicketNo())){
            return false;
        }


        if(CollectionUtils.isEmpty(createReturnOrderVo.getDetailDtoList())){
            return false;
        }

        //过滤掉未退款数据
        List<CreateReturnOrderDetailVo> detailDtoList = createReturnOrderVo.getDetailDtoList();
        detailDtoList =   detailDtoList.stream().filter(data -> data.getExitNumber() != null ).collect(Collectors.toList());
        for (CreateReturnOrderDetailVo detailDto : detailDtoList) {
            if(StringUtils.isAnyBlank(detailDto.getBatchNo(),detailDto.getProductCode()) || detailDto.getPositionId() == null || detailDto.getExitNumber() == null){
                return false;
            }
        }

        createReturnOrderVo.setDetailDtoList(detailDtoList);
        return true;
    }


    @RequestMapping(value = "/addOrderSource",method = RequestMethod.POST)
    public ResultVO addOrderSource(@RequestBody HeyeOrderSourceDto reqDto){
        logger.info("添加荷叶渠道的数据:{}", JSON.toJSON(reqDto));
        if(StringUtils.isBlank(reqDto.getSecretKey()) || CollectionUtils.isEmpty(reqDto.getSourceList())){
            return ResultVO.createError("必填参数为空，请检查！");
        }
        List<OrderSourceDto> list = reqDto.getSourceList();
        for(OrderSourceDto dto : list){
            if(ObjectUtils.isEmpty(dto.getSourceCode()) || StringUtils.isBlank(dto.getSourceName())){
                return ResultVO.createError("渠道配置为空，渠道编码："+dto.getSourceCode()+"，渠道名称："+dto.getSourceName());
            }
        }
        com.xyy.saas.common.util.ResultVO result = thirdOrderApi.addOrderSource(reqDto);
        return BeanUtils.copyProperties(result,ResultVO.class);
    }

    @GetMapping(value = "/orderSource/addOne")
    public ResultVO addOne(@PathParam("sourceCode") Integer sourceCode, @PathParam("sourceName") String sourceName, @PathParam("secretKey") String secretKey){
        logger.info("添加单个荷叶渠道的数据，编码：{}，名称：{}，秘钥：{}", sourceCode,sourceName,secretKey);
        if(StringUtils.isBlank(sourceName) || StringUtils.isBlank(secretKey) || ObjectUtils.isEmpty(sourceCode)){
            return ResultVO.createError("必填参数为空，请检查！");
        }
        HeyeOrderSourceDto dto = new HeyeOrderSourceDto();
        List<OrderSourceDto> sourceList = new ArrayList<>();
        sourceList.add(new OrderSourceDto(sourceCode,sourceName));
        dto.setSourceList(sourceList);
        dto.setSecretKey(secretKey);
        com.xyy.saas.common.util.ResultVO result = thirdOrderApi.addOrderSource(dto);
        return BeanUtils.copyProperties(result,ResultVO.class);
    }


    @RequestMapping(value = "/updateOrderSource",method = RequestMethod.POST)
    public ResultVO updateOrderSource(@RequestBody HeyeOrderSourceDto reqDto){
        logger.info("修改荷叶渠道的数据:{}", JSON.toJSON(reqDto));
        if(StringUtils.isBlank(reqDto.getSecretKey()) || CollectionUtils.isEmpty(reqDto.getSourceList())){
            return ResultVO.createError("必填参数为空，请检查！");
        }
        com.xyy.saas.common.util.ResultVO result = thirdOrderApi.updateOrderSource(reqDto);
        return BeanUtils.copyProperties(result,ResultVO.class);
    }

    @GetMapping(value = "/orderSource/updateOne")
    public ResultVO updateOne(@PathParam("sourceCode") Integer sourceCode, @PathParam("sourceName") String sourceName, @PathParam("secretKey") String secretKey){
        logger.info("修改单个荷叶渠道的数据，编码：{}，名称：{}，秘钥：{}", sourceCode,sourceName,secretKey);
        if(StringUtils.isBlank(sourceName) || StringUtils.isBlank(secretKey) || ObjectUtils.isEmpty(sourceCode)){
            return ResultVO.createError("必填参数为空，请检查！");
        }
        HeyeOrderSourceDto dto = new HeyeOrderSourceDto();
        List<OrderSourceDto> sourceList = new ArrayList<>();
        sourceList.add(new OrderSourceDto(sourceCode,sourceName));
        dto.setSourceList(sourceList);
        dto.setSecretKey(secretKey);
        com.xyy.saas.common.util.ResultVO result = thirdOrderApi.updateOrderSource(dto);
        return BeanUtils.copyProperties(result,ResultVO.class);
    }


    @RequestMapping(value = "/getOrderSource",method = RequestMethod.POST)
    public ResultVO<List<ThirdOrderSourceDto>> getOrderSource(){
        return ResultVO.createSuccess(thirdOrderApi.getOrderSources());
    }

    @GetMapping(value = "/orderSource/getList")
    public ResultVO<List<ThirdOrderSourceDto>> getList(){
        return ResultVO.createSuccess(thirdOrderApi.getOrderSources());
    }

    @RequestMapping(value = "/initOrderSource",method = RequestMethod.POST)
    public ResultVO initOrderSource(){
        com.xyy.saas.common.util.ResultVO result = thirdOrderApi.initOrderSource();
        return BeanUtils.copyProperties(result,ResultVO.class);
    }



}
