package com.xyy.saas.search.util;

import java.util.concurrent.*;

/**
 * @ClassName ExecServiceTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/9/24 17:44
 * @Version 1.0
 **/
public class ExecServiceTask {
    private static final int nThreads = Runtime.getRuntime().availableProcessors()+1;
//    private static ExecutorService executorService = new ThreadPoolExecutor(10, 100,
//            60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(500));
    private static ThreadPoolExecutor executorService = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(), 20,
            60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000));
    public static void execute(Runnable runnable){
        executorService.execute(runnable);
    }
}
