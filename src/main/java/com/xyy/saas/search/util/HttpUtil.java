package com.xyy.saas.search.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Method;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;


public class HttpUtil {
	private static Logger logger = LoggerFactory.getLogger(HttpUtil.class);

	private static SSLContext sslcontext = null;

	private static X509TrustManager tm = null;

	private static SSLConnectionSocketFactory sslsf = null;

	static {
		tm = new X509TrustManager() {

			@Override
			public void checkClientTrusted(X509Certificate[] xcs, String string)
					throws CertificateException {
			}

			@Override
			public void checkServerTrusted(X509Certificate[] xcs, String string)
					throws CertificateException {
			}

			@Override
			public X509Certificate[] getAcceptedIssuers() {
				return null;
			}
		};

		try {
			sslcontext = SSLContext.getInstance("SSL");
			sslcontext.init(null, new TrustManager[]{tm}, null);
		} catch (NoSuchAlgorithmException e) {
			logger.error("error", e);
		} catch (KeyManagementException e) {
			logger.error("error", e);
		}

		sslsf = new SSLConnectionSocketFactory(sslcontext,
				new String[]{"TLSv1"}, null,
				SSLConnectionSocketFactory.BROWSER_COMPATIBLE_HOSTNAME_VERIFIER);
	}


	public static String doHttp(String urlStr, Object paraBean)
			throws Exception {
		String charSet = "UTF-8";
		String timeOut = "200000";
		return doHttp(urlStr, charSet, paraBean, timeOut);
	}

	public static String doHttp(String urlStr, Map<String, Object> params)
			throws Exception {
		String charSet = "UTF-8";
		String timeOut = "200000";
		return doHttp(urlStr, charSet, params, timeOut);
	}

	/**
	 * 发送HTTP请示
	 *
	 * @param urlStr
	 *            ：请求URL
	 * @param charSet
	 *            :编码方式
	 * @param parameters
	 *            :javaBean参数对象，会利用其中的getter方式获取值
	 * @param timeOut
	 *            :超时
	 * @return
	 * @throws Exception
	 */
	private static String doHttp(String urlStr, String charSet, Object paraBean,
			String timeOut) throws Exception {
		String responseString = null;
		int statusCode = 0;
		HttpClient httpclient = new HttpClient();
		httpclient.setConnectionTimeout(new Integer(timeOut).intValue());
		PostMethod postMethod = new PostMethod(urlStr);
		httpclient.getParams().setParameter(
				HttpMethodParams.HTTP_CONTENT_CHARSET, charSet);
		InputStream is=null;
		InputStreamReader isReader=null;
		BufferedReader reader=null;
		
		try {
			// 组合请求参数
			List<NameValuePair> list = new ArrayList<NameValuePair>();
			Method[] ms = paraBean.getClass().getMethods();
			for (int i = 0; i < ms.length; i++) {
				Method m = ms[i];
				String name = m.getName();
				if (name.startsWith("get")) {
					String param = name.substring(3, name.length());
					param = param.substring(0, 1).toLowerCase()
							+ param.substring(1, param.length());
					if (param.equals("class")) {
						continue;
					}
					Object value = "";
					try {
						value = m.invoke(paraBean, null);
						NameValuePair nvp = new NameValuePair(param,
								value.toString());
						list.add(nvp);
					} catch (Exception e) {
						e.printStackTrace();
						throw e;
					}
				}
			}
			NameValuePair[] nvps = new NameValuePair[list.size()];
			postMethod.setRequestBody(list.toArray(nvps));
			statusCode = httpclient.executeMethod(postMethod);
			
			
			//responseString = postMethod.getResponseBodyAsString();
			is=postMethod.getResponseBodyAsStream();
			isReader=new InputStreamReader(is);
			reader = new BufferedReader(isReader);
			StringBuffer stringBuffer = new StringBuffer();
			String str = "";
			
			while((str = reader.readLine())!=null){
				stringBuffer.append(str);
			}
			
			responseString = stringBuffer.toString();
			
			if (statusCode < HttpURLConnection.HTTP_OK
					|| statusCode >= HttpURLConnection.HTTP_MULT_CHOICE) {
				System.err.println("失败返回码[" + statusCode + "]");
				throw new Exception("请求接口失败，失败码[ " + statusCode + " ]");
			}
		} catch (IOException e) {
			e.printStackTrace();
			System.err.println(e.toString());
			throw e;
		}finally{
			try {
				if(is!=null){
					is.close();
				}
				if(isReader!=null){
					isReader.close();
				}
				if(reader!=null){
					reader.close();
				}
			} catch (Exception e2) {
				
			}
		}
		return responseString;
	}

	/**
	 * 发送HTTP请示
	 * 
	 * @param urlStr
	 *            ：请求URL
	 * @param charSet
	 *            :编码方式
	 * @param parameters
	 *            :javaBean参数对象，会利用其中的getter方式获取值
	 * @param timeOut
	 *            :超时
	 * @return
	 * @throws Exception
	 */
	private static String doHttp(String urlStr, String charSet,
			Map<String, Object> params, String timeOut) throws Exception {
		String responseString = null;
		int statusCode = 0;
		HttpClient httpclient = new HttpClient();
		httpclient.setConnectionTimeout(new Integer(timeOut).intValue());
		PostMethod postMethod = new PostMethod(urlStr);
		httpclient.getParams().setParameter(
				HttpMethodParams.HTTP_CONTENT_CHARSET, charSet);
		BufferedReader reader=null;
		InputStreamReader isReader=null;
		InputStream is=null;
		
		try {
			// 组合请求参数
			List<NameValuePair> list = convertToNameValuePairs(params);
			NameValuePair[] nvps = new NameValuePair[list.size()];
			postMethod.setRequestBody(list.toArray(nvps));
			statusCode = httpclient.executeMethod(postMethod);
			//responseString = postMethod.getResponseBodyAsString();
			is=postMethod.getResponseBodyAsStream();
			isReader=new InputStreamReader(is);
			reader = new BufferedReader(isReader);
			StringBuffer stringBuffer = new StringBuffer();
			String str = "";
			while((str = reader.readLine())!=null){
				stringBuffer.append(str);
			}
			responseString = stringBuffer.toString();
			
			if (statusCode < HttpURLConnection.HTTP_OK
					|| statusCode >= HttpURLConnection.HTTP_MULT_CHOICE) {
				System.err.println("失败返回码[" + statusCode + "]");
				throw new Exception("请求接口失败，失败码[ " + statusCode + " ]");
			}
		} catch (IOException e) {
			e.printStackTrace();
			System.err.println(e.toString());
			throw e;
		}
		finally{//释放流资源666666
			try {
				if(is!=null){
					is.close();
				}
				if(isReader!=null){
					isReader.close();
				}
				if(reader!=null){
					reader.close();
				}
			} catch (Exception e2) {
				
			}
		}
		return responseString;
	}


	private static List<NameValuePair> convertToNameValuePairs(
			Map<String, Object> maps) {
		List<NameValuePair> result = new ArrayList<NameValuePair>();
		for (String name : maps.keySet()) {
			result.add(new NameValuePair(name, maps.get(name).toString()));
		}

		return result;
	}

	private static CloseableHttpClient createHttpClient(String url)
			throws KeyManagementException, NoSuchAlgorithmException,
			MalformedURLException {
		URL u = new URL(url);
		CloseableHttpClient httpclient = null;
		if ("https".equals(u.getProtocol())) {
			logger.trace("https");
			httpclient = HttpClients.custom().setSSLSocketFactory(sslsf)
					.build();
		} else {
			logger.trace("http");
			httpclient = HttpClients.createDefault();
		}

		return httpclient;
	}

	public static String postAsList(String url,List<String> list,String name)
			throws IOException, KeyManagementException,
			NoSuchAlgorithmException {
		CloseableHttpClient httpclient = HttpUtil.createHttpClient(url);
		HttpPost httpPost = new HttpPost(url);
		List<org.apache.http.NameValuePair> params = new ArrayList<org.apache.http.NameValuePair>();
		org.apache.http.NameValuePair pair = new BasicNameValuePair(name, String.join(",",list));
		params.add(pair);
		httpPost.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
		CloseableHttpResponse response = httpclient.execute(httpPost);
		return EntityUtils.toString(response.getEntity());
	}

	public static String postAsString(String url)
			throws IOException, KeyManagementException,
			NoSuchAlgorithmException {
		CloseableHttpClient httpclient = HttpUtil.createHttpClient(url);
		HttpPost httpPost = new HttpPost(url);
		RequestConfig defaultRequestConfig = RequestConfig.custom()
				.setSocketTimeout(300000)
				.setConnectTimeout(30000)
				.setConnectionRequestTimeout(30000)
				.build();
		httpPost.setConfig(defaultRequestConfig);
		CloseableHttpResponse response = httpclient.execute(httpPost);
		return EntityUtils.toString(response.getEntity());
	}
}
