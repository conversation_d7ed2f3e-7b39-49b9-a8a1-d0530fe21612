package com.xyy.saas.search.util;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * ip 帮助类
 *
 */
public class IpUtil {

	/**
	 * 获取本地ip地址
	 * 
	 * @return
	 */
	public static String getLocalIP() {
		InetAddress addr = null;
		try {
			addr = InetAddress.getLocalHost();
		} catch (UnknownHostException e) {
			e.printStackTrace();
		}

		byte[] ipAddr = null;
		if(addr != null) {
			ipAddr = addr.getAddress();
		}
		String ipAddrStr = "";
		if(ipAddr != null && ipAddr.length>0) {
			for (int i = 0; i < ipAddr.length; i++) {
				if (i > 0) {
					ipAddrStr += ".";
				}
				ipAddrStr += ipAddr[i] & 0xFF;
			}
		}
		
		return ipAddrStr;
	}

	public static void main(String[] args) {
		System.out.println(getLocalIP());
	}
}
