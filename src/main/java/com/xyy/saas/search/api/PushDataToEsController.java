package com.xyy.saas.search.api;

import com.xyy.saas.search.config.SearchConfig;
import com.xyy.saas.search.es.EsProductUtil;
import com.xyy.saas.search.es.EsStandLibraryUtil;
import com.xyy.saas.search.service.SaasProductSLService;
import com.xyy.saas.search.service.SaasProductService;
import com.xyy.saas.search.util.IpUtil;
import com.xyy.saas.search.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @date 2019-02-24 14:33 星期日
 */
@RequestMapping("/push")
@Controller
public class PushDataToEsController {
    private static final Logger LOGGER = LoggerFactory.getLogger(PushDataToEsController.class);
    @Autowired
    private SearchConfig searchConfig;

    @Autowired
    private SaasProductSLService saasProductSLService;

    @Autowired
    private SaasProductService saasProductService;

    @RequestMapping("/bulkPushAllInfoToES")
    @ResponseBody
    public Object bulkPushAllInfoToES() {
        // 获取配置里配置的ip地址
        String createIndexIp = searchConfig.getInitcreateindexip();
        // 得到本地ip地址
        String localIp = IpUtil.getLocalIP();
        // 判断系统配置的ip是否跟本地ip一致，如果一致，则执行全量同步
//        if (StringUtil.isNotEmpty(createIndexIp) && StringUtil.isNotEmpty(localIp) && localIp.equals(createIndexIp)) {
		if (true){
            try {
                // 执行全量更新
                boolean b = saasProductService.syncBulkAllInfoToEs();
                if (b){
                    // 切换索引
                    EsProductUtil.tabIndex();
                }
                return "bulkPushAllInfoToES is successfully!!!";
            } catch (Exception e) {
                LOGGER.error("手动全量bulkPushAllInfoToES更新失败：", e);
                return "bulkPushAllInfoToES is failed!!!!!!!!!!!!!";
            }
        }else {
            return "手动全量更新索引失败，原因是因为系统配置的ip地址跟本地ip地址不一致!";
        }
    }

    @RequestMapping("/bulkPushAllInfoToES21")
    @ResponseBody
    public Object bulkPushAllInfoToES21() {
        // 获取配置里配置的ip地址
        String createIndexIp = searchConfig.getInitcreateindexip();
        // 得到本地ip地址
        String localIp = IpUtil.getLocalIP();
        // 判断系统配置的ip是否跟本地ip一致，如果一致，则执行全量同步
//        if (StringUtil.isNotEmpty(createIndexIp) && StringUtil.isNotEmpty(localIp) && localIp.equals(createIndexIp)) {
        if (true){
            try {
                // 执行全量更新
                boolean b = saasProductService.syncBulkAllInfoToEs21();
                if (b){
                    // 切换索引
                    EsProductUtil.tabIndex();
                }
                return "bulkPushAllInfoToES is successfully!!!";
            } catch (Exception e) {
                LOGGER.error("手动全量bulkPushAllInfoToES更新失败：", e);
                return "bulkPushAllInfoToES is failed!!!!!!!!!!!!!";
            }
        }else {
            return "手动全量更新索引失败，原因是因为系统配置的ip地址跟本地ip地址不一致!";
        }
    }


    @RequestMapping("/bulkPushProductInfoToES")
    @ResponseBody
    public Object bulkPushProductInfoToES() {
        // 获取配置里配置的ip地址
        String createIndexIp = searchConfig.getInitcreateindexip();
        // 得到本地ip地址
        String localIp = IpUtil.getLocalIP();
        // 判断系统配置的ip是否跟本地ip一致，如果一致，则执行全量同步
        if (StringUtil.isNotEmpty(createIndexIp) && StringUtil.isNotEmpty(localIp) && localIp.equals(createIndexIp)) {
            try {
                // 执行全量更新
                boolean b = saasProductSLService.syncBulkAllInfoToEs();
                return "bulkPushProductInfoToES is successfully!!!";
            } catch (Exception e) {
                LOGGER.error("手动全量bulkPushProductInfoToES更新失败：", e);
                return "bulkPushProductInfoToES is failed!!!!!!!!!!!!!";
            }
        }else {
            return "手动全量更新索引失败，原因是因为系统配置的ip地址跟本地ip地址不一致!";
        }
    }

    @RequestMapping("/bulkPushProductInfoToES21")
    @ResponseBody
    public Object bulkPushProductInfoToES21() {
        // 获取配置里配置的ip地址
        String createIndexIp = searchConfig.getInitcreateindexip();
        // 得到本地ip地址
        String localIp = IpUtil.getLocalIP();
        // 判断系统配置的ip是否跟本地ip一致，如果一致，则执行全量同步
        if (StringUtil.isNotEmpty(createIndexIp) && StringUtil.isNotEmpty(localIp) && localIp.equals(createIndexIp)) {
            try {
                // 执行全量更新
                boolean b = saasProductSLService.syncBulkAllInfoToEs21();
                return "bulkPushProductInfoToES is successfully!!!";
            } catch (Exception e) {
                LOGGER.error("手动全量bulkPushProductInfoToES更新失败：", e);
                return "bulkPushProductInfoToES is failed!!!!!!!!!!!!!";
            }
        }else {
            return "手动全量更新索引失败，原因是因为系统配置的ip地址跟本地ip地址不一致!";
        }
    }

    @RequestMapping("/getAllBkzCount")
    @ResponseBody
    public Object getAllBkzCount() {
        return saasProductSLService.getAllCount();
    }
}
