package com.xyy.saas.search.api;

import com.xyy.saas.search.config.SearchConfig;
import com.xyy.saas.search.service.StandLibraryService;
import com.xyy.saas.search.util.IpUtil;
import com.xyy.saas.search.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;

/**
 * @ClassName StandLibraryController
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/10/28 14:16
 **/
@RequestMapping("/standLibrary")
@Controller
public class StandLibraryController {
    private static final Logger logger = LoggerFactory.getLogger(StandLibraryController.class);
    @Autowired
    private SearchConfig searchConfig;

    @Autowired
    private StandLibraryService standLibraryService;

    @RequestMapping("/init")
    @ResponseBody
    public Object initStandLibrary(HttpServletRequest request) throws IOException {
        // 获取配置里配置的ip地址
//        String createIndexIp = searchConfig.getInitcreateindexip();
//        // 得到本地ip地址
//        String localIp = IpUtil.getLocalIP();
//        // 判断系统配置的ip是否跟本地ip一致，如果一致，则执行全量同步
//        if (StringUtil.isNotEmpty(createIndexIp) && StringUtil.isNotEmpty(localIp) && localIp.equals(createIndexIp)) {
            try {
                // 执行全量更新
                boolean b = standLibraryService.initStandLibrary();
                logger.info("initStandLibrary is successfully!!!");
                return "initStandLibrary is successfully!!!";
            } catch (Exception e) {
                logger.error("手动全量initStandLibrary更新失败：", e);
                return "initStandLibrary is failed!!!!!!!!!!!!!";
            }
        }
//        else {
//            return "手动全量更新索引失败，原因是因为系统配置的ip地址跟本地ip地址不一致!";
//        }
//    }
}
