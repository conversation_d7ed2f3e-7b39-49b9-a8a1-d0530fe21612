package com.xyy.saas.search.api;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.search.model.*;
import com.xyy.saas.search.service.NewSaasProductService;
import com.xyy.saas.search.service.SaasProductService;
import com.xyy.saas.search.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: AutoMatchApiController
 * @date 2019/12/11  14:04
 * @description: 新匹配规则
 */
@RequestMapping("/auto")
@Controller
public class AutoMatchApiController {

    private static Logger LOGGER = LoggerFactory.getLogger(SearchController.class);

    @Autowired
    private NewSaasProductService newSaasProductService;

    @RequestMapping("/newMacthRule")
    @ResponseBody
    public Map<String,String> newMacthRule(HttpServletRequest request) throws Exception {
        Map<String,String> resultMap = new HashMap<>();
        List<StandardLibraryVo> successList = new ArrayList<>();
        //接收数据
        StringBuffer sb = new StringBuffer() ;
        InputStream is = request.getInputStream();
        InputStreamReader isr = new InputStreamReader(is, "UTF-8");
        BufferedReader br = new BufferedReader(isr);
        String s = "" ;
        while((s=br.readLine())!=null){
            sb.append(s) ;
        }
        String jsonStr = sb.toString();
        if (StringUtil.isNotEmpty(jsonStr)) {
            List<StandardLibraryVo> productBzkVoList = JSONArray.parseArray(jsonStr,StandardLibraryVo.class);
            LOGGER.info("-----入参的数据条数："+productBzkVoList.size()+"------");
            for(StandardLibraryVo productBzkVo : productBzkVoList ){
                try {
                    List<StandardLibraryVo> matchList = matchingData( productBzkVo);
                    if(!CollectionUtils.isEmpty(matchList)){
                        if(matchList.size() == 1){
                            Long productId = productBzkVo.getProductId();
                            productBzkVo = matchList.get(0);
                            if(null != productId){
                                productBzkVo.setProductId(productId);
                            }
                            successList.add(productBzkVo);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    LOGGER.error("查询标准库信息异常："+e);
                }
            }
        }
        resultMap.put("success",JSONObject.toJSONString(successList));
        return resultMap;
    }

    /**
     * 匹配规则
     * @param ref
     * @return
     */
    private List<StandardLibraryVo> matchingData(StandardLibraryVo ref){
        if (ref!=null) {
            try {
                //条码 精准匹配
                if (StringUtil.isNotEmpty(ref.getBarCode())){
                    return newSaasProductService.matchByBarCode(ref);
                }
            } catch (Exception e) {
                e.printStackTrace();
                LOGGER.error("查询标准库信息异常："+e);
            }
        }
        return new ArrayList<>();
    }
}
