package com.xyy.saas.search.dao;

import com.xyy.saas.search.model.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-02-23 20:39 星期六
 */
@Mapper
public interface SaasProductMapper {

    /**
     * 获取标准库信息
     * @return
     */
    List<StandardLibrary> getAllInfo();

    /**
     * 获取标准库数据条数
     * @return
     */
    int getAllCount();

    /**
     * 获取标准库信息(根据当前时间前31分钟)
     * @return
     */
    List<StandardLibrary> getInfoByTime();

    /**
     * 同步标准库数据
     * @return
     */
    List<Map<String,Object>> findAllList();

    /**
     * 定时更新
     * @return
     */
    List<Map<String,Object>> findListByUpdateTime();

    List<Map<String,Object>> getIdByTypeAndName(DictionaryInfo info);

    List<Map<String,Object>> getIdByparentIdAndValue(DictionaryManagement management);

    Long insertDictionary(DictionaryInfo info);

    Long insertDictionaryManager(DictionaryManagement management);

    boolean insertBiaozhunkuxinxi(StandardLibraryInfo info);

    boolean updateBiaozhunkuxinxi(StandardLibraryInfo info);
}
