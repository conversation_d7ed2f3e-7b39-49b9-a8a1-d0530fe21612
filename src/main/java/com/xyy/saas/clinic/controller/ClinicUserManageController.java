package com.xyy.saas.clinic.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.base.ResultVO;
import com.xyy.saas.clinic.enm.ClinicApiUrlKeyEnum;
import com.xyy.saas.clinic.sync.ClinicDataSyncService;
import com.xyy.user.module.dto.SaasUserDto;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * Created with IntelliJ IDEA.
 * User: Lv.<PERSON>e
 * Date: 2018/9/29
 * Time: 10:31
 * Description: 用户管理
 */
@Controller
@RequestMapping(value = "/clinic/backstage/user")
public class ClinicUserManageController {
    @Resource
    private ClinicDataSyncService clinicDataSyncService;

    @RequestMapping(value = "/toList")
    public String toHtml() {
        return "/clinic/backstage/userManger";
    }

    @RequestMapping("/findUserByIdAndRoleId")
    public String findUserByIdAndRoleId(Model model, Integer id,Integer roleId,String rn){
//        SaasUserDto saasUserDto = userManageApi.findUserByIdAndRoleId( id,roleId);
        JSONObject param = new JSONObject();
        param.put("id", id);
        param.put("roleId", roleId);
        Object object = clinicDataSyncService.execRequest(ClinicApiUrlKeyEnum.USER_URL_UserManage.getCode(),"findUserByIdAndRoleId", param);
        SaasUserDto saasUserDto= JSONObject.parseObject(JSON.toJSONString(object), SaasUserDto.class);
        String drugStoreStatus="正常";
        if(saasUserDto.getDrugStoreStatus()!=null && saasUserDto.getDrugStoreStatus()==2){
            drugStoreStatus="已冻结";
        }
        model.addAttribute("saasUser",saasUserDto);
        model.addAttribute("rn",rn);
        model.addAttribute("drugStoreStatus",drugStoreStatus);
        return "/clinic/backstage/userInfo";
    }

    /**
     * 分页查询
     * @param phone
     * @param page
     * @param rows
     * @return
     */
    @RequestMapping("/findPageList")
    @ResponseBody
    public ResultVO findPageList(String phone, Integer page, Integer rows) {
//        return ResultVO.createSuccess(userManageApi.findUserPageList(phone,page,rows));
        JSONObject param = new JSONObject();
        param.put("phone", phone);
        param.put("page", page);
        param.put("rows", rows);
        return ResultVO.createSuccess(clinicDataSyncService.execRequest(ClinicApiUrlKeyEnum.USER_URL_UserManage.getCode(),"findUserPageList", param));
    }

    /**
     * 变更状态
     * @param status
     * @param id
     * @return
     */
    @RequestMapping("/changeStatus")
    @ResponseBody
    public ResultVO changeStatus(Byte status,Integer id) {
//        return ResultVO.createSuccess(userManageApi.changeStatus(status, id));
        JSONObject param = new JSONObject();
        param.put("status", status);
        param.put("id", id);
        return ResultVO.createSuccess(clinicDataSyncService.execRequest(ClinicApiUrlKeyEnum.USER_URL_UserManage.getCode(),"changeStatus", param));
    }


}
