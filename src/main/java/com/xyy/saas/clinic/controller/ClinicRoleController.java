package com.xyy.saas.clinic.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.clinic.enm.ClinicApiUrlKeyEnum;
import com.xyy.saas.clinic.sync.ClinicDataSyncService;
import com.xyy.user.module.dto.AddRoleDto;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Lv.<PERSON>
 * Date: 2018/10/11
 * Time: 11:08
 * Description:
 */
@Controller
@RequestMapping(value = "/clinic/backstage/role")
public class ClinicRoleController {
    @Resource
    private ClinicDataSyncService clinicDataSyncService;

    @RequestMapping(value = "/toList")
    public String toHtml() {
        return "/clinic/backstage/role";
    }

    /**
     * 分页查询
     * @param page
     * @param rows
     * @return
     */
    @RequestMapping("/findPageList")
    @ResponseBody
    public ResultVO findPageList(Integer page, Integer rows) {
//        return roleApi.queryPageRoleInfo(rows,page);
        JSONObject param = new JSONObject();
        param.put("rows", rows);
        param.put("page", page);
        return ResultVO.createSuccess(clinicDataSyncService.execRequest(ClinicApiUrlKeyEnum.USER_URL_Role.getCode(),"queryPageRoleInfo", param));
    }

    /**
     * 查询详情
     * @return
     */
    @RequestMapping(value = "/queryPermissionRoleInfo", method = RequestMethod.GET)
    public String  queryPermissionRoleInfo(Model model, Integer roleId,String roleName) {
//        List<Map<String, Object>>  permissionList =  permissionApi.findPermissionByRoleId(roleId);
        JSONObject param = new JSONObject();
        param.put("roleId", roleId);
        Object object = clinicDataSyncService.execRequest(ClinicApiUrlKeyEnum.USER_URL_Permission.getCode(), "findPermissionByRoleId", param);
        List<Map<String, Object>>  permissionList = getPermissionList(object);
        model.addAttribute("permissionList", permissionList);
        model.addAttribute("roleName",roleName);
        model.addAttribute("roleId",roleId);
        return "/clinic/backstage/roleInfo";
    }

    /**
     * 添加
     * @return
     */
    @RequestMapping(value = "/toRoleAdd")
    public String  toRoleAdd() {
        return "/clinic/backstage/roleAdd";
    }

    /**
     * 删除系统角色
     * @param id 角色id
     * @return
     */
    @RequestMapping(value = "/deleteSystemRoleInfo", method = RequestMethod.POST)
    @ResponseBody
    public ResultVO deleteSystemRoleInfo(Integer id) {
//        return roleApi.deleteSystemRoleInfo(id);
        JSONObject param = new JSONObject();
        param.put("id", id);
        return ResultVO.createSuccess(clinicDataSyncService.execRequest(ClinicApiUrlKeyEnum.USER_URL_Role.getCode(),"deleteSystemRoleInfo", param));
    }

    /**
     * 保存系统角色
     * @param id 角色id
     * @param permssionIds 资源id
     * @return
     */
    @RequestMapping(value = "/saveRolePermissionInfo")
    @ResponseBody
    public ResultVO saveRolePermissionInfo(Integer id,String permssionIds) {
        ResultVO resultVO = new ResultVO();
        resultVO.setCode(0);
//        resultVO.setResult(permissionApi.saveRolePermission(id,permssionIds));
        JSONObject param = new JSONObject();
        param.put("id",id);
        param.put("permssionIds",permssionIds);
        resultVO.setResult(clinicDataSyncService.execRequest(ClinicApiUrlKeyEnum.USER_URL_Permission.getCode(),"saveRolePermission", param));
        return resultVO;
    }

    // 查询菜单树
    @RequestMapping(value = "/findPermissionTreeByRoleId",method= RequestMethod.POST)
    @ResponseBody
    public ResultVO findPermissionTreeByRoleId(Integer roleId) {
//        List<Map<String,Object>> menuList = permissionApi.findPermissionByRoleId(roleId);
        JSONObject param = new JSONObject();
        param.put("roleId", roleId);
        Object object = clinicDataSyncService.execRequest(ClinicApiUrlKeyEnum.USER_URL_Permission.getCode(), "findPermissionByRoleId", param);
        List<Map<String, Object>>  permissionList = getPermissionList(object);
        return ResultVO.createSuccess(permissionList);
    }

    private List<Map<String,Object>> getPermissionList(Object object) {
        JSONArray objects = JSONArray.parseArray(object.toString());
        List<Map<String, Object>>  permissionList = new ArrayList<>();
        for(int i = 0; i< objects.size(); i++){
            Map<String, Object> objectMap = JSON.parseObject(JSON.toJSONString(objects.get(i)), new TypeReference<Map<String, Object>>() {});
            List<Map<String, Object>>  childList = new ArrayList<>();
            JSONArray objectChild = JSONArray.parseArray(JSON.toJSONString(objectMap.get("children")));
            if(objectChild != null && objectChild.size() > 0) {
                for (int j = 0; j < objectChild.size(); j++) {
                    Map<String, Object> childObjectMap = JSON.parseObject(JSON.toJSONString(objectChild.get(j)), new TypeReference<Map<String, Object>>() {
                    });
                    List<Map<String, Object>> childList2 = new ArrayList<>();
                    JSONArray objectChild2 = JSONArray.parseArray(JSON.toJSONString(childObjectMap.get("children")));
                    if (objectChild2 != null && !objectChild2.isEmpty()) {
                        for (int z = 0; z < objectChild2.size(); z++) {
                            Map<String, Object> childObjectMap2 = JSON.parseObject(JSON.toJSONString(objectChild2.get(z)), new TypeReference<Map<String, Object>>() {
                            });
                            childList2.add(childObjectMap2);
                        }
                        childObjectMap.put("children", childList2);
                    }
                    childList.add(childObjectMap);
                }
            }
            objectMap.put("children", childList);
            permissionList.add(objectMap);
        }
        return permissionList;
    }

    // 添加角色
    @RequestMapping(value = "/saveRole",method= RequestMethod.POST)
    @ResponseBody
    public ResultVO saveRole(AddRoleDto addRoleDto) {
//        return roleApi.addSystemRoleInfo(addRoleDto);
        return ResultVO.createSuccess(clinicDataSyncService.execRequest(ClinicApiUrlKeyEnum.USER_URL_Role.getCode(),"addSystemRoleInfo", addRoleDto));
    }





}
