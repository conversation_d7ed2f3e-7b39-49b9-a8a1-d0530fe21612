package com.xyy.saas.clinic.controller;

import com.github.tobato.fastdfs.domain.fdfs.StorePath;
import com.github.tobato.fastdfs.service.FastFileStorageClient;
import com.xyy.saas.base.ResultCodeEnum;
import com.xyy.saas.base.ResultVO;
import com.xyy.saas.video.service.FileUploadService;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @annotation:文件上传类
 * @create 2018-08-06 10:02
 **/
@Controller
@RequestMapping("/clinicFileUpload")
public class ClinicFileUploadController {
    private static final Logger logger = LoggerFactory.getLogger(ClinicFileUploadController.class);
    @Autowired
    private FileUploadService fileUploadService;

    @RequestMapping("/toFile")
    public String toFile(){
        return "/clinicVideo/fileUpload";
    }
    @Autowired
    @Qualifier("defaultFastFileStorageClient")
    private FastFileStorageClient fastFileStorageClient;

    @Value("${upload.file.url}")
    private String uploadFileUrl;

    @RequestMapping("/fileSave")
    @ResponseBody
    public ResultVO fileSave(MultipartFile file){
        if(file.isEmpty()){
            return new ResultVO(ResultCodeEnum.ERROR,null);
        }
        Map<String,MultipartFile> files = new HashMap<>();
        String fileName = file.getOriginalFilename();
        String filexName = fileName.substring(0,fileName.lastIndexOf("."));
        files.put(filexName,file);
        String fileUrl="";
        Map<String,String> urlList =  saveFile(files);
        if(urlList!=null){
            for(String key : urlList.keySet()){
                fileUrl=urlList.get(key);
            }
        }
        return new ResultVO(ResultCodeEnum.SUCCESS,fileUrl);
        /*int size = (int) file.getSize();
        System.out.println(fileName + "-->" + size);

        String path = "C:/test" ;
        File dest = new File(path + "/" + fileName);
        if(!dest.getParentFile().exists()){ //判断文件父目录是否存在
            dest.getParentFile().mkdir();
        }
        try {
            file.transferTo(dest); //保存文件
            //保存并返回绝对路径
            return new ResultVO(ResultCodeEnum.SUCCESS,dest.getAbsolutePath());
        } catch (IllegalStateException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return new ResultVO(ResultCodeEnum.ERROR,null);
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return new ResultVO(ResultCodeEnum.ERROR,null);
        }*/

    }

    private Map<String,String> saveFile(Map<String, MultipartFile> files) {

        if (StringUtils.isEmpty(uploadFileUrl)) {
            throw new RuntimeException("uploadUrl can not be null");
        }

        Map<String,String> url = new HashMap<>();
        for(String key : files.keySet()){
            MultipartFile file = files.get(key);
            String fileName = file.getOriginalFilename();
            StorePath storePath=null;
            try {
                storePath =  fastFileStorageClient.uploadFile(file.getInputStream(),file.getSize(), FilenameUtils.getExtension(fileName),null);
                logger.info("上传地址"+storePath.getFullPath());
                url.put(key, uploadFileUrl+storePath.getFullPath());
            }catch (Exception e){
                logger.error("图片名为"+fileName+"上传报错",e);
            }
        }
        return url;
    }

    private Boolean deleteFileByUrl(String url) {
        boolean back = true;
        try {
            fastFileStorageClient.deleteFile(url);
        }catch (Exception e){
            logger.error("deleteFileByUrl error",e);
            back= false;
        }
        return back;
    }

}

