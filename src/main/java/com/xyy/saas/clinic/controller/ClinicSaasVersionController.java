package com.xyy.saas.clinic.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.base.AbstractCommonController;
import com.xyy.saas.base.ResultVO;
import com.xyy.saas.clinic.enm.ClinicApiUrlKeyEnum;
import com.xyy.saas.clinic.sync.ClinicDataSyncService;
import com.xyy.saas.common.dto.SystemVersion;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * @description 版本信息列表(Controller)
 * <AUTHOR>
 * @date 2018-05-29 13:49:23
 */
@Controller
@RequestMapping("/clinic/saas/version")
public class ClinicSaasVersionController extends AbstractCommonController {

	@Resource
	private ClinicDataSyncService clinicDataSyncService;
	/**
	 * 跳转到列表页面
	 */
	@RequestMapping("/toList")
	public String toList(){
		return "clinic/backstage/saas/versionList";
	}

	/**
	 * 跳转到新增页面
	 * @return
	 */
    @RequestMapping("/toAdd")
    public String toAdd(){
        return "clinic/backstage/saas/addVersion";
    }

	/**
	 * 跳转到修改页面
	 */
	@RequestMapping("/toUpdate")
	public String getProductById(Integer id, Model model){
//        SystemVersion systemVersion = sysVersionApi.queryVersionById(id);
		JSONObject param = new JSONObject();
		param.put("id", id);
		Object object = clinicDataSyncService.execRequestCommon(ClinicApiUrlKeyEnum.COMMON_URL_SysVersion.getCode(), "queryVersionById", param);
		model.addAttribute("result", object);
		return "clinic/backstage/saas/editVersion";
	}

	@RequestMapping("/query")
	@ResponseBody
	public ResultVO query(Integer rows, Integer page){
		if(page==null){
			page=1;
		}
		if(rows==null){
			rows=20;
		}
		PageInfo pageInfo=new PageInfo();
		pageInfo.setPageSize(rows);
		pageInfo.setPageNum(page);
//		return ResultVO.createSuccess(sysVersionApi.queryVersions(pageInfo));
		return ResultVO.createSuccess(clinicDataSyncService.execRequestCommon(ClinicApiUrlKeyEnum.COMMON_URL_SysVersion.getCode(),"queryVersions", pageInfo));
	}

	/**
	 * 新增或
	 */
	@PostMapping(value="/addOrUpdate")
	public String add(Integer id, String versionNumber, String versionContent){
		SystemVersion systemVersion = new SystemVersion();
		systemVersion.setVersionNumber(versionNumber);
		systemVersion.setVersionContent(versionContent);

		if (StringUtils.isEmpty(id)) {
//            sysVersionApi.addVersionInfo(systemVersion);
			clinicDataSyncService.execRequestCommon(ClinicApiUrlKeyEnum.COMMON_URL_SysVersion.getCode(),"addVersionInfo", systemVersion);
        } else {
            systemVersion.setId(id);
//            sysVersionApi.updateVersionInfo(systemVersion);
			clinicDataSyncService.execRequestCommon(ClinicApiUrlKeyEnum.COMMON_URL_SysVersion.getCode(),"updateVersionInfo", systemVersion);
		}
		return "redirect:/clinic/saas/version/toList";
	}

	/**
	 * 删除
	 */
	@GetMapping("/delete")
	public String delete(Integer id){
//        sysVersionApi.deleteVersionInfo(id);
		JSONObject param = new JSONObject();
		param.put("id", id);
		clinicDataSyncService.execRequestCommon(ClinicApiUrlKeyEnum.COMMON_URL_SysVersion.getCode(),"deleteVersionInfo", param);
		return "redirect:/clinic/saas/version/toList";
	}
}
