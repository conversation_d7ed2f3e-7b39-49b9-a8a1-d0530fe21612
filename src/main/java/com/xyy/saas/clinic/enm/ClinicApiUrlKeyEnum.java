package com.xyy.saas.clinic.enm;

/**
 * @ClassName: ClinicApiUrlKeyEnum
 * @Description: api对应url
 * @author: zhang<PERSON><PERSON><PERSON>
 * @date: 2019/6/14 10:28
 */
public enum ClinicApiUrlKeyEnum {
    USER_URL_Permission("PermissionApi","/permission"),//user服务--权限
    USER_URL_Drugstore("DrugstoreApi","/drugstore"),//user服务--药店
    USER_URL_Role("RoleApi","/role"),//user服务--角色
    USER_URL_User("UserApi","/user"),//user服务--用户
    USER_URL_UserManage("UserManageApi","/userManager"),//user服务--用户
    USER_URL_Upgrade("UpgradeApi","/upgrade"),//user服务--版本升级
    USER_URL_PermissionGrayChange("PermissionGrayChangeApi","/permissionGrayChange"),//user服务--版本升级
    COMMON_URL_SysVersion("SysVersionApi","/sysversion"),//common服务--版本信息
    COMMON_URL_SysAvertPictrue("SaasAdvertPictureApi","/advertPicture"),//common广告服务
    COMMON_URL_SysVideoContent("SaaasVideoContentApi","/videoContent"),//common视频服务
    COMMON_URL_SysVideoClassify("SaasClassifyApi","/videoClassify"),//common视频分类服务
    COMMON_URL_SyncMsg("SyncMsgApi","/syncMsg");//common服务--同步监控

    private String name;
    private String code;

    private ClinicApiUrlKeyEnum(String code, String name){
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    /**
     * 根据code获取name
     */
    public static String getByCode(String code) {
        for (ClinicApiUrlKeyEnum temp : ClinicApiUrlKeyEnum.values()) {
            if (temp.getCode().equals(code)) {
                return temp.getName();
            }
        }
        return "";
    }
}
