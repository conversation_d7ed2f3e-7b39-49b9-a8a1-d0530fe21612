package com.xyy.saas.clinic.sycnMsg.service;

import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.cloud.sycnMsg.vo.SyncMsgVo;
import com.xyy.saas.common.dto.SyncMsgDto;

public interface SyncMsgClinicService {

	
	/**
     * 查询同步信息
     * @param syncMsgDto
     * @return
     */
    PageInfo<SyncMsgDto> selectSyncMsgList(PageInfo pageInfo, SyncMsgVo syncMsgVo);
    
    /**
     * 更改同步信息
     * @param syncMsgDto
     * @return
     */
    Map<String, Object> updateSyncMsgList(SyncMsgVo syncMsgVo);
    
    /**
     * 推送MQ
     * @param syncMsgDto
     * @return
     */
    Map<String, Object> sendSyncMsg2MQ(SyncMsgVo syncMsgVo);

}
