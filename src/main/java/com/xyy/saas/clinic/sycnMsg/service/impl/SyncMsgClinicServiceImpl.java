package com.xyy.saas.clinic.sycnMsg.service.impl;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xyy.saas.clinic.enm.ClinicApiUrlKeyEnum;
import com.xyy.saas.clinic.sycnMsg.service.SyncMsgClinicService;
import com.xyy.saas.clinic.sync.ClinicDataSyncService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.cloud.sycnMsg.vo.SyncMsgVo;
import com.xyy.saas.common.dto.SyncMsgDto;
import com.xyy.saas.common.util.DateUtil;
import com.xyy.util.StringUtil;

import javax.annotation.Resource;

@Service
public class SyncMsgClinicServiceImpl implements SyncMsgClinicService {

//	@Reference(version = "0.0.3")
//    private SyncMsgApi syncMsgApi;
	@Resource
	private ClinicDataSyncService clinicDataSyncService;

	/**
     * 查询同步信息
     * @return
     */
	@Override
	public PageInfo<SyncMsgDto> selectSyncMsgList(PageInfo pageInfo, SyncMsgVo syncMsgVo) {
		SyncMsgDto syncMsgDto = new SyncMsgDto();
    	BeanUtils.copyProperties(syncMsgVo, syncMsgDto);
    	String cs = syncMsgVo.getCreateTimeS();
        String ce = syncMsgVo.getCreateTimeE();
        String us = syncMsgVo.getUpdateTimeS();
        String ue = syncMsgVo.getUpdateTimeE();
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS);
        try {
	        if(!StringUtil.isEmpty(cs)) {
	        	syncMsgDto.setCreateTimeS(sdf.parse(cs));
	        }
	        if(!StringUtil.isEmpty(ce)) {
	        	syncMsgDto.setCreateTimeE(sdf.parse(ce));
	        }
	        if(!StringUtil.isEmpty(us)) {
	        	syncMsgDto.setUpdateTimeS(sdf.parse(us));
	        }
	        if(!StringUtil.isEmpty(ue)) {
	        	syncMsgDto.setUpdateTimeE(sdf.parse(ue));
	        }
        }catch(Exception e) {
        	e.printStackTrace();
        }
//		return syncMsgApi.selectSyncMsgList(pageInfo, syncMsgDto);
		JSONObject param = JSONObject.parseObject(JSON.toJSONString(syncMsgDto));
		param.put("pageNum", pageInfo.getPageNum());
		param.put("pageSize", pageInfo.getPageSize());
		param.put("size", pageInfo.getSize());
		Object resObject = clinicDataSyncService.execRequestCommon(ClinicApiUrlKeyEnum.COMMON_URL_SyncMsg.getCode(), "selectSyncMsgList", param);
		PageInfo<SyncMsgDto> resPageInfo = JSON.parseObject(JSON.toJSONString(resObject), new TypeReference<PageInfo<SyncMsgDto>>() {});
		return resPageInfo;
	}
	
	/**
     * 查询同步信息
     * @return
     */
	@Override
    public Map<String, Object> updateSyncMsgList(SyncMsgVo syncMsgVo){
    	SyncMsgDto syncMsgDto = new SyncMsgDto();
    	BeanUtils.copyProperties(syncMsgVo, syncMsgDto);
//    	return syncMsgApi.updateSyncMsgList(syncMsgDto);
		Object sendSyncMsg2MQ = clinicDataSyncService.execRequestCommon(ClinicApiUrlKeyEnum.COMMON_URL_SyncMsg.getCode(), "updateSyncMsgList", syncMsgDto);
		return JSONObject.parseObject(JSON.toJSONString(sendSyncMsg2MQ), HashMap.class);
    }
    
    /**
     * 推送MQ
     * @return
     */
	@Override
    public Map<String, Object> sendSyncMsg2MQ(SyncMsgVo syncMsgVo){
//    	return syncMsgApi.sendSyncMsg2MQ(syncMsgDto);
		Object sendSyncMsg2MQ = clinicDataSyncService.execRequestCommon(ClinicApiUrlKeyEnum.COMMON_URL_SyncMsg.getCode(), "sendSyncMsg2MQ", syncMsgVo);
		return JSONObject.parseObject(JSON.toJSONString(sendSyncMsg2MQ), HashMap.class);
	}

}
