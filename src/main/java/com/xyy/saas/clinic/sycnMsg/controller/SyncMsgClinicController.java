package com.xyy.saas.clinic.sycnMsg.controller;

import java.util.Date;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.xyy.saas.clinic.sycnMsg.service.SyncMsgClinicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.github.pagehelper.PageInfo;
import com.xyy.saas.cloud.sycnMsg.vo.SyncMsgVo;
import com.xyy.saas.common.dto.SyncMsgDto;
import com.xyy.saas.purchase.core.common.ResultVO;
import com.xyy.saas.util.DateUtil;

/**
 * @date
 * <AUTHOR>
 * @description 同步监控
 */
@Controller
@RequestMapping("/clinic/syncMsg")
public class SyncMsgClinicController {

    @Autowired
    private SyncMsgClinicService syncMsgClinicService;

    @RequestMapping(value = "/syncMsgList")
    public ModelAndView toHtml(HttpServletRequest request,ModelAndView model){
    	 Date date = new Date();
    	 String createTimeS = DateUtil.parseDateToStr(DateUtil.addDate(date, 0, 0, -6, 0, 0, 0, 0),DateUtil.DATE_FORMAT_YYYY_MM_DD);
    	 String createTimeE = DateUtil.parseDateToStr(date, DateUtil.DATE_FORMAT_YYYY_MM_DD);
    	 model.addObject("createTimeS", createTimeS);
         model.addObject("createTimeE", createTimeE);
    	model.setViewName("/clinic/syncMsg/syncMsgList");
        return model;
    }


    /**
     * 查询列表
     */
    @RequestMapping("/selectSyncMsgList")
    @ResponseBody
//    public ResultVO<PageInfo<SyncMsgDto>> selectSyncMsgList(Integer rows, Integer page, @RequestBody SyncMsgVo syncMsgVo) {
    	public ResultVO<PageInfo<SyncMsgDto>> selectSyncMsgList(Integer rows, Integer page,  SyncMsgVo syncMsgVo) {
    	if (page == null) {
            page = 1;
        }
        if (rows == null) {
            rows = 10;
        }
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(rows);
        pageInfo.setPageNum(page);
        PageInfo<SyncMsgDto> SyncMsgDtoPageInfo = syncMsgClinicService.selectSyncMsgList(pageInfo, syncMsgVo);

        return ResultVO.createSuccess(SyncMsgDtoPageInfo);
    }
    
    /**
     * 更改同步信息状态
     */
    @RequestMapping("/changeStatus")
    @ResponseBody
    public ResultVO<Map<String, Object>> changeStatus(@RequestBody SyncMsgVo syncMsgVo) {
    	Map<String, Object> map = syncMsgClinicService.updateSyncMsgList(syncMsgVo);
    	return ResultVO.createSuccess(map);
    }
    
    /**
     * 推送MQ
     */
    @RequestMapping("/sendSyncMsg2MQ")
    @ResponseBody
    public ResultVO<Map<String, Object>> sendSyncMsg2MQ(@RequestBody SyncMsgVo syncMsgVo) {
    	Map<String, Object> map = syncMsgClinicService.sendSyncMsg2MQ(syncMsgVo);
    	return ResultVO.createSuccess(map);
    }

}
