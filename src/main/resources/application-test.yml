spring:
  datasource:
#    url: ************************************************************************************************************************************************************************************************
#    username: wang_zhenyu
#    password: ADEMRzNJD25uKOq9CbmkYWRxLpaxb5
#    type: com.alibaba.druid.pool.DruidDataSource
#    driverClassName: com.mysql.jdbc.Driver
    maxActive: 50
    initialSize: 5
    maxWait: 60000
    minIdle: 5
    timeBetweenEvictionRunsMillis: 2000
    minEvictableIdleTimeMillis: 300000
    validationQuery: select 'x'
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
#    druid:
#      filters: config
#      connection-properties: config.decrypt=true;config.decrypt.key=${spring.datasource.key}
xyy:
  spring:
    zebra:
      ds:
        cloud:
          url[0]: ************************************************************************************************************************************************************************************************
          active[0]: true
          username[0]: wang_zhenyu
          password[0]: ADEMRzNJD25uKOq9CbmkYWRxLpaxb5
          url[1]: ************************************************************************************************************************************************************************************************
          driverClass[1]: com.mysql.jdbc.Driver
          active[1]: true
          username[1]: wang_zhenyu
          password[1]: ADEMRzNJD25uKOq9CbmkYWRxLpaxb5

es:
  cluster: saas-es-test
  esurl: es1-saas-test.elasticsearch.ybm100.top:19300;es2-saas-test.elasticsearch.ybm100.top:19300;es3-saas-test.elasticsearch.ybm100.top:19300
  initcreateindexip: ************

rocketmq:
  name-server: mq1-me-test.rocketmq.ybm100.top:9876;mq2-me-test.rocketmq.ybm100.top:9876;mq3-me-test.rocketmq.ybm100.top:9876;mq4-me-test.rocketmq.ybm100.top:9876
  consumer:
    esProductGroup: CID_ME_PRODUCT_ZHL_CONSUMER_GROUP
    esProductTopic: XYY_ME_PRODUCT_TOPIC

cloud:
  server:
    saasInfoUrl: https://interface.test.ybm100.com/saas/getSaasInfoByGoodsCode
    saasInfoByPage: https://interface.test.ybm100.com/saas/getSaasInfoByPage

  ## https://es.int.ybm100.com/  es head 地址

#apollo配置
apollo:
  meta: http://node01-test.appolo.ybm100.top:8080,http://node02-test.appolo.ybm100.top:8080,http://node03-test.appolo.ybm100.top:8080
  bootstrap:
    enabled: true
    eagerLoad:
      enabled: true
  test: apollo


