<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="utf-8" />
    <title>Dashboard - Ace Admin</title>

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
<#include "/common/top-common.ftl"/>
</head>
<body id="innerBody">
<!-- 工具栏 -->
<#--<div id="toolbox" class="toolbox newToolbox">-->
<#--    <a href="/fights/toAdd" class="btn btn-addbag btn-default btn-round"> 新增</a>-->
<#--    <a href="javascript:;" id="editproScatteredBtn" class="btn btn-redactbag btn-default btn-round">  编辑</a>-->
<#--    <button id="exportLotNumber" type="button" class="btn btn-redactbag"> 导出Excel</button>-->
<#--</div>-->
<div class="panel panel-default publicPanel newPanel">
    <form class="commonValidate">
        <div class="row">
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">清斗日期从：</label>
                    <input type="text" id="start" name="start"  value="${endDate!""}" class="form-control  Wdate" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'end\');}'})" style="height: inherit;">
                    <input type="hidden" id="start1" name="start1" value="${startDate!""}">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">至</label>
                    <input type="text" id="end" name="end"  value="${endDate!""}" class="form-control  Wdate" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'start\');}'})" style="height: inherit;">
                    <input type="hidden" id="end1" name="end1" value="${endDate!""}">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">清斗单号</label>
                    <input type="text" name="clearFightPref" id="clearFightPref" class="form-control"  />
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">商品信息</label>
                    <input type="text" name="productName" id="productName" class="form-control" placeholder="商品编码/通用名称/助记码/批准文号/条形码" />
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-xs-12 marginTop5">
                <div class="input-group">
                    <button id="exportLotNumber" data-auth="导出Excel" type="button" class="btn btn-redactbag" style="float: left"> 导出Excel</button>
                    <button id="queryBtn" type="button" class="btn btn-redactbag forbidRepeatSearch">  查询</button>
                    <#--  <button id="cancleBtn" type="button" class="btn btn-return">   重置</button>  -->
                    <button type="button" class="btn btn-redactbag" id="filtrkolumn">筛选列</button>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="panel panel-default publicPanel" id="b_table">

    <table id="grid-table"></table>
    <div id="grid-pager" style="margin-top: 20px !important;"></div>

    <div hidden>
        <input id="agentList" value='${agentList!""}' />
    </div>
</div>


</body>
<script src="/static/js/fights/clearFightShow.js?v=${jsVersion}"></script>

</html>

