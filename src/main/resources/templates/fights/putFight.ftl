<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="utf-8" />
    <title>Dashboard - Ace Admin</title>

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
</head>
<body id="innerBody">
<div hidden>
    <input id="agentList" value='${agentList!""}' />
</div>
<!-- 工具栏 -->
<div id="toolbox" class="toolbox newToolbox">
    <a href="/putFight/toAdd" data-auth="新增" class="btn btn-redactbag"> 新增</a>
    <#--  <a href="javascript:;" id="editproScatteredBtn" class="btn btn-redactbag btn-default btn-round">  编辑</a>  -->
    <#--  <button id="exportLotNumber" type="button" class="btn btn-redactbag"> 导出Excel</button>  -->
</div>
<div class="panel panel-default publicPanel newPanel">
    <form class="commonValidate">
        <div class="col-xs-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">装斗单号</label>
                <input class="form-control" type="text" name="putFightPref" id="putFightPref" />
            </div>
        </div>
        <#--  <div class="col-xs-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">商品信息</label>
                <input class="form-control" type="text" name="productPref" id="product" />
                <input type="hidden" name="id" id="id" />
            </div>
        </div>  -->
        <#--  <div class="col-xs-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">商品信息</label>
                <input type="text" name="productPref" id="productPref" class="form-control"  />
            </div>
        </div>  -->
        <div class="col-xs-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">起始时间</label>
                <input type="text" name="begin" id="begin" value="${endDate}" class="Wdate form-control"    onfocus="WdatePicker({maxDate:'#F{$dp.$D(\'end\')}'})" style="height: inherit;"/>
                <input type="hidden" name="defaultStartTime" id="defaultStartTime" value="${endDate}" class="Wdate form-control"    onfocus="WdatePicker({maxDate:'#F{$dp.$D(\'end\')}'})" style="height: inherit;"/>
            </div>
        </div>
        <div class="col-xs-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">结束时间</label>
                <input type="text" name="end" id="end" value="${endDate}" class="Wdate form-control"  onfocus="WdatePicker({minDate:'#F{$dp.$D(\'begin\')}'})" style="height: inherit;"/>
                <input type="hidden" name="defaultEndTime" id="defaultEndTime" value="${endDate}" class="Wdate form-control"  onfocus="WdatePicker({minDate:'#F{$dp.$D(\'begin\')}'})" style="height: inherit;"/>
            </div>
        </div>
        <div class="col-xs-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">装斗人</label>
                <div class="dropdown">
                    <span class="caret"></span>
                    <input type="hidden" value="" id="putFightUserId" name="putFightUserId">
                    <input type="button" class="dropdown-toggle select-btn" data-toggle="dropdown" value="全部" aria-haspopup="true" aria-expanded="false">
                    <ul class="dropdown-menu select-single">
                        <li><a href="">全部</a></li>
                        <#if (employeeDtos)??>
                            <#list employeeDtos as dtos>
                            <li><a href="#{dtos.id}">${dtos.name}</a></li>
                            </#list>
                        </#if>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-xs-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">审核人</label>
                <div class="dropdown">
                    <span class="caret"></span>
                    <input type="hidden" value="" id="reviewerUserId" name="reviewerUserId">
                    <input type="button" class="dropdown-toggle select-btn" data-toggle="dropdown" value="全部" aria-haspopup="true" aria-expanded="false">
                    <ul class="dropdown-menu select-single">
                        <li><a href="">全部</a></li>
                        <#if (employeeDtos)??>
                            <#list employeeDtos as dtos>
                            <li><a href="#{dtos.id}">${dtos.name}</a></li>
                            </#list>
                        </#if>
                    </ul>
                </div>
            </div>
        </div>
        <#--  <div class="col-xs-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">审核人</label>
                <select  lit="seach"  class="form-control" id ="reviewerUserName" name="checkUserName">
                    <option value=""  selected="selected">全部</option>
                    <#if (employeeDtos)??>
                        <#list employeeDtos as em>
                            <option value="#{em.id}">${em.name}</option>
                        </#list>
                    </#if>
                </select>
            </div>
        </div>  -->
        <div class="col-xs-9 marginTop5">
            <div class="input-group">
                <button id="queryBtn" type="button" class="btn btn-redactbag forbidRepeatSearch">查询</button>
                <button id="cancelBtn" type="button" class="btn btn-return forbidRepeatSearch"> 重置</button>
            </div>
        </div>
    </form>
</div>
<div class="panel panel-default publicPanel" id="b_table">
    <table id="grid-table"></table>
    <div id="grid-pager"></div>
</div>
</div>
</body>

<script src="/static/js/fights/putFight.js?v=${jsVersion}"></script>


</html>