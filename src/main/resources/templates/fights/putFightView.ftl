<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="utf-8" />
    <title>Dashboard - Ace Admin</title>

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />

<#include "/common/top-common.ftl"/>
    <style>
        .input-group .input-group-addon{
            min-width: 90px;
        }
        .marginTop5{
            padding: 0 4px;
            margin-top: 6px;
        }
    </style>
</head>
<body id="innerBody">
    <input type="hidden" name="type" id='putFightStatus' <#if (putFightStatus??)>value='${putFightStatus}'</#if> />
<!-- 工具栏 -->
    <div id="dictionary_tools" class="toolbox newToolbox">
        <button type="button" class="btn btn-return" id="cancel">取消</button>
        <button type="button" class="btn btn-redactbag" id="exportBtn" ps="true">导出Excel</button>
    </div>
    <div class="panel panel-default publicPanel newPanel">

        <form class="commonValidate" id="saveProduct">
            <div class="row">
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>装斗单号</label>
                        <input name="putFightPref" id="putFightPref" readonly type="text" <#if (data??)>value="${data.putFightPref!''}" </#if> class="form-control">
                    </div>
                </div>
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>装斗日期</label>
                        <input type="text" name="putFightTime" disabled required="required" id="putFightTime" <#if (data??)>value="${data.putFightTime?string("yyyy-MM-dd")}" </#if> class="Wdate form-control"  onfocus="WdatePicker()" style="height: inherit;">
                    </div>
                </div>
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>装斗人</label>
                        <input type="text" name="putFightUserRealName" disabled required="required" id="putFightUserRealName" class="form-control" <#if (data??)>value="${data.putFightUserRealName!''}" </#if> <#if (putFightUserRealName??)>value="${putFightUserRealName!""}"</#if> fs="true" required="required" data-msg-required='该字段为必填项' >
                    </div>
                </div>
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>审核人</label>
                        <input type="text" name="reviewerUserRealName" disabled required="required" id="reviewerUserRealName" class="form-control" <#if (data??)>value="${data.reviewerUserRealName!''}" </#if> <#if (reviewerUserRealName??)>value="${reviewerUserRealName!""}"</#if> fs="true" required="required" data-msg-required='该字段为必填项' >
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label  class="input-group-addon">注意事项</label>
                        <input name="mattersAttention" id="mattersAttention" <#if (data?? && data.mattersAttention??)>value="${data.mattersAttention!''}" </#if> type="text" disabled class="form-control">
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label  class="input-group-addon">备注</label>
						<input name="remark" id="remark" <#if (data?? && data.remark??)>value="${data.remark!''}" </#if> type="text" disabled class="form-control">
					</div>
				</div>
            </div>
        </form>
    </div>
    <div class="panel panel-default publicPanel" id="b_table">
        <table id="grid-table"></table>
    </div>
</body>

<script src="/static/js/fights/putFightView.js?v=${jsVersion}"></script>

</html>