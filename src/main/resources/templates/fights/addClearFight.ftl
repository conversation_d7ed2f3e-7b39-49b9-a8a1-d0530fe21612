<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="utf-8" />
    <title>Dashboard - Ace Admin</title>

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />

<#include "/common/top-common.ftl"/>
    <style>
        .input-group .input-group-addon{
            min-width: 90px;
        }
        .marginTop5{
            padding: 0 4px;
            margin-top: 6px;
        }
    </style>
</head>
<body id="innerBody">
    <input type="hidden" name="type" id='documentType' <#if (type??)>value='${type}'</#if> />
    <!-- 工具栏 -->
    <div id="dictionary_tools" class="toolbox newToolbox">
        <button type="button" class="btn btn-return" id="cancel">取消</button>
        <button type="button" class="btn btn-addbag" id="formSave" ps="true">保存</button>
        <button type="button" class="btn btn-redactbag" id="formSubmit" ps="true">提交</button>
    </div>
    <div class="panel panel-default publicPanel newPanel">
            <form class="commonValidate" action="save" method="post" id="saveProduct">
                <div class="row">
                    <#--  <div class="col-xs-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon">生产日期</label>
                            <input type="text" name="fightCreateTime" id="fightCreateTime" class="Wdate form-control" <#if (saasFightsInfo??)>value="${saasFightsInfo.fightCreateTime?string("yyyy-MM-dd")}" </#if>  onfocus="WdatePicker()" style="height: inherit;" >
                        </div>
                    </div>  -->
                    <#if (data.clearFightPref)??>
                        <div class="col-sm-3 marginTop5">
                            <div class="input-group">
                                <label class="input-group-addon"><span class="danger">*</span>清斗单号</label>
                                <input name="clearFightPref" id="clearFightPref" disabled type="text" <#if (data??)>value="${data.clearFightPref!''}" </#if> class="form-control">
                            </div>
                        </div>
                    </#if> 
                    <div class="col-xs-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon"><span class="danger">*</span>清斗日期</label>
                            <#if systemConfigYn.defaultPersonDateModifiable?? && systemConfigYn.defaultPersonDateModifiable==1>
                                <input type="text" name="clearFightTime" required="required" id="clearFightTime" value="${endDate}" class="Wdate form-control"  onfocus="WdatePicker()" style="height: inherit;">
                            <#else>
                                <input type="text" name="clearFightTime" required="required" id="clearFightTime" value="${endDate}" class="form-control" readonly style="height: inherit;background-color:#eee !important;">
                            </#if>
                        </div>
                    </div>
                    <#--  <#if '${p.id?c}'=='447'>value="${p.name!''}"</#if>  -->
                   <div class="col-sm-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon"><span class="danger">*</span>清斗人</label>
                            <#if systemConfigYn.defaultPersonDateModifiable?? && systemConfigYn.defaultPersonDateModifiable==1>
                                <select  lit="seach"  class="form-control" id ="clearFightUserId" name="clearFightUserId" fs="true" required="required" data-msg-required='该字段为必填项' >
                                    <#if (employeeDtos)??>
                                        <#list employeeDtos as em>
                                                <option value="#{em.id}" <#if reviewerUserId==em.id>selected </#if>>${em.name}</option>
                                        </#list>
                                    </#if>
                                </select>
                            <#else>
                                <input class="form-control" id="clearFightUserId" name="clearFightUserId" value='${reviewerUserId}' type="hidden" readonly/>
                                <#if (employeeDtos)??>
                                    <#list employeeDtos as em>
                                        <#if reviewerUserId==em.id> 
                                            <input class="form-control" readonly value='${em.name}' style="background-color:#eee !important;"/>
                                        </#if>
                                    </#list>
                                <#else>
                                    <input class="form-control" readonly value='' style="background-color:#eee !important;"/>
                                </#if>
                                <span class="copy-select-arrow-bottom arrow-bottom-position"></span>
                            </#if>
                        </div>
                    </div>
                    <div class="col-sm-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon"><span class="danger">*</span>审核人</label>
                            <select  lit="seach"  class="form-control" id ="reviewerUserId" name="reviewerUserId"  fs="true" required="required" data-msg-required='该字段为必填项' >
                                <#if (employeeDtos)??>
                                <#list employeeDtos as em>
                                            <option value="#{em.id}" <#if reviewerUserId==em.id>selected </#if> >${em.name}</option>
                                        </#list>
                                </#if>
                            </select>
                        </div>
                    </div>
                    <#--  <div class="col-xs-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon">清斗人</label>
                            <div class="dropdown">
                                <span class="caret"></span>
                                <input type="hidden" value="" id="clearFightUserId" name="clearFightUserId"<#if (data??)>value="${data.clearFightUserId!''}" </#if> <#if (clearFightUserName??)>value="${clearFightUserId!""}"</#if> fs="true" required="required" data-msg-required='该字段为必填项' >
                                <input type="button" class="dropdown-toggle select-btn" data-toggle="dropdown" value="全部" aria-haspopup="true" aria-expanded="false">
                                <ul class="dropdown-menu select-single">
                                    <li><a href="">全部</a></li>
                                    <#if (employeeDtos)??>
                                        <#list employeeDtos as dtos>
                                        <li><a href="#{dtos.id}">${dtos.name}</a></li>
                                        </#list>
                                    </#if>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon">审核人</label>
                            <div class="dropdown">
                                <span class="caret"></span>
                                <input type="hidden" value="" id="reviewerUserId" name="reviewerUserId" <#if (data??)>value="${data.reviewerUserId!''}" </#if> <#if (reviewerUserId??)>value="${reviewerUserId!""}"</#if> fs="true" required="required" data-msg-required='该字段为必填项' >
                                <input type="button" class="dropdown-toggle select-btn" data-toggle="dropdown" value="全部" aria-haspopup="true" aria-expanded="false">
                                <ul class="dropdown-menu select-single">
                                    <li><a href="">全部</a></li>
                                    <#if (employeeDtos)??>
                                        <#list employeeDtos as dtos>
                                        <li><a href="#{dtos.id}">${dtos.name}</a></li>
                                        </#list>
                                    </#if>
                                </ul>
                            </div>
                        </div>
                    </div>  -->
                    <div class="col-xs-3 marginTop5">
                        <div class="input-group">
                            <label  class="input-group-addon">注意事项</label>
                            <input name="mattersAttention" id="mattersAttention" maxlength="100" type="text" class="form-control">
                        </div>
                    </div>
                    <div class="col-xs-3 marginTop5">
                        <div class="input-group">
                            <label  class="input-group-addon">备注</label>
                            <input name="remark" id="remark" maxlength="50" type="text" class="form-control">
                        </div>
                    </div>
                </div>
                <#--<button class="hideSubmit" type="submit">提交</button>-->
            </form>
    </div>
    <div class="panel panel-default publicPanel" id="b_table">
        <div class="row">
            <div class="input-group box_zll">
                <button class="btn btn-addbag" id="addRow">新增行</button>
                <#--  <button class="btn btn-removebag" id="deleteRow">删除行</button>  -->
            </div>
        </div>
        <table id="grid-table"></table>
    </div>
</body>
    <script src="/static/assets/js/GridSub.js"></script>
    <script src="/static/js/fights/addOrUpdateClearFight.js?v=${jsVersion}"></script>

</html>