<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>Dashboard - Ace Admin</title>
		<meta name="google" content="notranslate" />
		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
	</head>
	<body id="innerBody" class="notranslate">
		<div class="panel panel-default publicPanel newPanel">
			<form class="row dialog-search">
				<div class="col-xs-4 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">商品</label>
						<input type="text" lit="seach" id="product" value="${name!}" class="form-control">
					</div>
				</div>
                <div class="col-xs-4 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">生产厂家</label>
                        <input type="text" lit="seach" id="manufacturer"  class="form-control">
                    </div>
                </div>
				<div class="col-xs-4 marginTop5">
					<div class="input-group">
						<button id="queryBtn" type="button" class="btn btn-redactbag">查询</button>
						<button id="resetBtn" type="button" class="btn btn-return">重置</button>
					    <#if (type) ??>
                        <button id="queryBtnProduct" type="button" class="btn btn-redactbag">新增商品信息</button>
                        </#if>
					</div>
				</div>
			</form>
		</div>
		<div class="panel panel-default publicPanel" id="b_table">
			<table id="grid-table"></table>
			<div id="grid-pager"></div>
		</div>
	<div class="text-right btn_sub_can_div" style="bottom: 0">
        <button type="button" class="btn btn-return" id="btn_modalClose">取消</button>
        <button  type="button" class="btn btn-redactbag" id="btn_submit">确定</button>
	</div>

	</body>
    <script src="/static/assets/js/jquery.contextmenu.r2.js?v=${jsVersion}"></script>
    <script src="/static/js/purchase/purchasePopProductListAll.js?v=${jsVersion}"></script>
    <script src="/static/js/index/index.js?v=${jsVersion}"></script>
</html>

