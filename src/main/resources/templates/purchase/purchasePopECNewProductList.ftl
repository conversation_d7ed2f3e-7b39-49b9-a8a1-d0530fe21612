<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>Dashboard - Ace Admin</title>
		<meta name="google" content="notranslate" />
		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<link rel="stylesheet" href="/static/assets/css/element-ui.min.css" />
        <#include "/common/top-common.ftl"/>
        <style>
            .el-table .el-table__cell {
                vertical-align: top;
            }
            .el-table-custom-column .item {
				display: flex;
				color: #606266;
			}
			.el-table-custom-column .item .left {
				width: 40%;
                margin-left: 4px;
			}
			.el-table-custom-column .item .middle {
				width: 40%;
                margin-left: 4px;
			}
            .el-table-custom-column .item .middle-max {
				width: 60%;
                margin-left: 4px;;
			}
			.el-table-custom-column .item .right {
				width: 20%
			}
			.el-table thead {
				color: rgba(0, 0, 0, 0.85);
			}
            .el-table-custom-column .title {
                display: flex;
                justify-content: space-between;
            }
            .el-table-custom-column .title .label-text {
                font-size: 16px;
                font-weight: 500;
                margin-bottom: 6px;
            }
            .operation-btn {
                color: #409EFF;
                cursor: pointer;
            }
            .el-button.operation-btn {
                padding-top: 0;
                padding-bottom: 0;
            }
            .operation-btn+.operation-btn {
                margin-left: 10px;
            }
            .operation-btns {
                display: flex;
                justify-content: flex-end;
                padding: 10px;
            }
            .action-bar-right {
                padding: 5px;
                text-align: right;
            }
            .action-bar-right .tips {
                font-size: 12px;
            }
            #match-product-dialog {
                padding: 5px;
            }
            .el-icon-plus {
                margin-right: 0;
            }
            .el-icon-edit {
                margin-right: 0;
            }
        </style>
	</head>
	<body id="innerBody">
        <div id="match-product-dialog" v-loading="loading">
        <div class="action-bar-right">
            <span class="tips">注：操作增加新品后，请手动点击刷新进行匹配</span>
            <el-button size="small" type="primary" @click="handleRefresh">刷新</el-button>
        </div>
            <el-table :data="tableData" style="width: 100%" height="calc(100vh - 100px)" :border="true" :show-header="false">
                <el-table-column
                    label=""
                >
                    <template slot-scope="{row}">
                        <div class="el-table-custom-column">
                            <div class="title">
                                <span class="label-text">商城商品信息</span>
                            </div>
                            <div class="item">
                                <div class="left">
                                    <span class="cell-label">通用名:</span>
                                    <span class="info">{{row.thirdProduct.commonName}}</span>
                                </div>
                                <div class="middle">
                                    <span class="cell-label">规格:</span>
                                    <span class="info">{{row.thirdProduct.attributeSpecification}}</span>
                                </div>
                                <div class="right">
                                    <span class="cell-label">单位:</span>
                                    <span class="info">{{row.thirdProduct.unitName}}</span>
                                </div>
                            </div>
                            <div class="item">
                                <div class="left">
                                    <span>批准文号:</span>
                                    <span>{{row.thirdProduct.approvalNumber}}</span>
                                </div>
                                <div class="middle-max">
                                    <span>生产厂家:</span>
                                    <span>{{row.thirdProduct.manufacturer}}</span>
                                </div>
                            </div>
                            <div class="item">
                                <div class="left">
                                    <span>进货价:</span>
                                    <span>{{row.thirdProduct.buyingPrice}}</span>
                                </div>
                                <div class="middle">
                                    <span>商城标准库id:</span>
                                    <span>{{row.thirdProduct.standardLibraryId}}</span>
                                </div>
                                <div class="right">
                                    <span>待入库数量:</span>
                                    <span>{{row.thirdProduct.stockAmount}}</span>
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="storeProductInfo"
                    label=""
                >
                    <template slot-scope="{row}">
                        <div class="el-table-custom-column">
                            <div class="title">
                                <span class="label-text">本店商品信息</span>
                                <div>
                                    <el-button type="text" icon="el-icon-plus" class="operation-btn" @click="handleAdd(row.thirdProduct.productNo)">增加新品</el-button>
                                    <el-button type="text" icon="el-icon-edit" class="operation-btn" @click="matching_New(row)">{{ row.ownProduct.productNo ? '修改匹配' : '手动匹配'  }}</el-button>   
                                </div>  
                            </div>
                            <div v-if="row.ownProduct.productNo">
                                <div class="item">
                                    <div class="left">
                                        <span class="cell-label">通用名:</span>
                                        <span class="info">{{row.ownProduct.commonName}}</span>
                                    </div>
                                    <div class="middle">
                                        <span class="cell-label">规格:</span>
                                        <span class="info">{{row.ownProduct.attributeSpecification}}</span>
                                    </div>
                                    <div class="right">
                                        <span class="cell-label">单位:</span>
                                        <span class="info">{{row.ownProduct.unitName}}</span>
                                    </div>
                                </div>
                                <div class="item">
                                    <div class="left">
                                        <span>批准文号:</span>
                                        <span>{{row.ownProduct.approvalNumber}}</span>
                                    </div>
                                    <div class="middle-max">
                                        <span>生产厂家:</span>
                                        <span>{{row.ownProduct.manufacturer}}</span>
                                    </div>
                                </div>
                                <div class="item">
                                    <div class="left">
                                        <span>本店标准库id:</span>
                                        <span>{{row.ownProduct.standardLibraryId}}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="operation-btns">
                <el-button type="text" @click="handleCancel">取消</el-button>
                <el-button v-if="tableData.length" type="primary" @click="handleConfirm">确认</el-button>
            </div>
        </div>
	</body>
    <script src="/static/assets/js/vue.min.js?v=${jsVersion}"></script>
	<script src="/static/assets/js/element-ui.min.js?v=${jsVersion}"></script>
    <script src="/static/js/purchase/purchasePopECNewProductList.js?v=${jsVersion}"></script>

</html>

