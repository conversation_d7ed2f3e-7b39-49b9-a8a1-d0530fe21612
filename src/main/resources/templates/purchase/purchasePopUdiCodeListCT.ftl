<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
	<meta charset="utf-8" />
	<title>Dashboard - Ace Admin</title>
	<meta name="google" content="notranslate" />
	<meta name="description" content="overview &amp; stats" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
	<#include "/common/top-common.ftl"/>
</head>
<body id="innerBody" class="notranslate">
	<input type="hidden" value="${billType!}" id="billType" />
	<input type="hidden" value="${productAmount!}" id="editProductAmount" />
	<input type="hidden" value="${productName!}" id="billProductName" />
	<input type="hidden" value="${productBatchNo!}" id="productBatchNo" />
	<input type="hidden" value="${rowid!}" id="rowid" />
	<input type="hidden" value="${billNo!}" id="billNo" />

	<div class="panel panel-default publicPanel newPanel">
		<form class="row dialog-search">
			<div style="display: flex; justify-content: space-between; margin-top: 5px;">
				<div>
					<div class="input-group">
						<label class="input-group-addon" style="text-align: left;">通用名称：<span id="commonName"></span></label>
					</div>
				</div>

				<div style="display: flex; align-items: center;">
					<span style="text-wrap: nowrap;">UDI码</span>
					<input style="margin-left: 10px; margin-right: 10px; width: 200px;" type="text" lit="seach" id="udiCode" maxlength="100" />
					<button id="queryBtn" type="button" class="btn btn-redactbag">录入</button>
				</div>
			</div>
		</form>
        <div style="margin:10px 0; color:red;">
		    注意：请检查录入的UDI码器械上的是否一致，若不一致，请修改UDI码后再录入。
	    </div>
	</div>
    
	<div class="panel panel-default publicPanel" id="b_table">
		<table id="grid-table"></table>
	<#--	<div id="grid-pager"></div>-->
	</div>
	<div class="btn_sub_can_div" style="text-align: end;">
		<button  type="button" class="btn btn-redactbag" id="btn_close">取消</button>
		<button  type="button" class="btn btn-redactbag" id="btn_submit">确定</button>
	</div>
</body>
<script src="/static/js/purchase/purchasePopUdiCodeListCT.js?v=${jsVersion}"></script>
</html>
