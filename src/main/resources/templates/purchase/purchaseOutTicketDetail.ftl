<!DOCTYPE html>
<html lang="en">
<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
	<meta charset="utf-8" />
	<title>Dashboard - Ace Admin</title>

	<meta name="description" content="overview &amp; stats" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
	<#include "/common/top-common.ftl"/>
	<style>
		.textareaStyle{
			padding: 7px 6px;
			border-radius: 4px!important;
		}
		.marginLeft{
			margin-left: 10px!important;
		}
		.mandatory{
			color: red;
			font-style: normal;
		}
		.marginTop30{
			margin-top: 30px;
		}
		.error{
			position: absolute;
			color: red;
			display: none;
			top: 40px;
			margin-left: 10px;
		}
		.errorInput{
			top: 60px!important;
		}
		.positionRelative{
			position: relative;
		}
	</style>
</head>
<body id="innerBody" >
<#if source?? && source == 0>
	<div id="toolbox" class="toolbox">
		<button onclick="location.href='/purchase/reBill/toOutTicketList'" class="btn btn-return">  返回</button>
		<button id="formSave" disabled class="btn btn-addbag">  保存</button>
		<button id="updateStatus" disabled class="btn btn-redactbag">  提交</button>
		<button id="getBill" disabled class="btn btn-redactbag">  提取入库验收单</button>
		<button style="display:none" id="printPage" class="btn btn-return">  打印</button>
		<#if (saasPurchaseRetrieveBillInfoVO.settlementStatus?? && saasPurchaseRetrieveBillInfoVO.settlementStatus!=2)>
			<button id="settlement" class="btn btn-return">结算</button>
		</#if>
	</div>
<#else>
	<div id="toolbox" class="toolbox">
		<#--  审核人数据  -->
		<input type="hidden" id="enterpriseOwners" name="enterpriseOwners" value="${(systemConfigYn.enterpriseOwners)!}">
		<input type="hidden" id="enterpriseId" name="enterpriseId" value="${(systemConfigYn.enterpriseOwnersEmployeeId)!}">
		<input type="hidden" id="qualityOwners" name="qualityOwners" value="${(systemConfigYn.qualityOwners)!}">
		<input type="hidden" id="qualityId" name="qualityId" value="${(systemConfigYn.qualityOwnersEmployeeId)!}">
		<input type="hidden" id="resultId" name="resultId" value="${id!''}">
		<button onclick="location.href='/purchase/reBill/toOutTicketList'" class="btn btn-return"> 返回</button>
		<button id="through"  class="btn btn-addbag">  通过</button>
		<button id="rejected"  class="btn btn-redactbag">  驳回</button>
	</div>
</#if>

<div class="panel panel-default publicPanel newPanel shfh">
	<form id="formData" class="commonValidate">
		<input type="hidden" value="${(saasPurchaseRetrieveBillInfoVO.id)!}" id="billId">
		<input type="hidden" name="guid" value="${(saasPurchaseRetrieveBillInfoVO.guid)!}" id="guid">
		<input type="hidden" name="orderMedicineType" value="${(orderMedicineType)!}" id="orderMedicineType">
		<input type="hidden" id="settlementStatus" name="settlementStatus" value="${(saasPurchaseRetrieveBillInfoVO.settlementStatus)!}">
		<div class="col-sm-3 marginTop5">
			<div class="input-group">
				<label class="input-group-addon">单据编号</label>
				<input type="text" name="saasPurchaseRetrieveBillInfoVO.billNo" id="billNo" value="${(saasPurchaseRetrieveBillInfoVO.billNo)!}" class="form-control" readonly>
			</div>
		</div>
		<div class="col-sm-3 marginTop5">
			<div class="input-group">
				<label class="input-group-addon">开票日期</label>
				<#if (saasPurchaseRetrieveBillInfoVO.billTime)??>
					<input type="text" name="saasPurchaseRetrieveBillInfoVO.billTime" id="billTime" value="${(saasPurchaseRetrieveBillInfoVO.billTime)?string("yyyy-MM-dd")}" required="required" class="Wdate form-control"  style="height: inherit;" readonly/>
				<#else>
					<input type="text" name= "saasPurchaseRetrieveBillInfoVO.billTime" id="billTime" required="required" class="Wdate form-control"  style="height: inherit;" readonly/>
				</#if>
			</div>
		</div>

		<div class="col-sm-3 marginTop5">
			<div class="input-group">
				<label class="input-group-addon">供应商编号</label>
				<input type="text" name= "saasPurchaseRetrieveBillInfoVO.pharmacyPref" id="supplierNo" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.pharmacyPref)!}" required="required" readonly>
				<input type="hidden" name= "saasPurchaseRetrieveBillInfoVO.supplierNo" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.supplierNo)!}" required="required" readonly>
			</div>
		</div>
		<div class="col-sm-3 marginTop5">
			<div class="input-group">
				<label class="input-group-addon">供应商名称</label>
				<input type="text" name= "saasPurchaseRetrieveBillInfoVO.supplierName" id="supplierName" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.supplierName)!}"  required="required" readonly>
			</div>
		</div>
		<div class="col-xs-3 marginTop5">
			<div class="input-group">
				<label class="input-group-addon">销售员</label>
				<input type="hidden" value="${(saasPurchaseRetrieveBillInfoVO.salesperson)!}" id="salesperson" name="salesperson">
				<input type="text" name= "salespersonName" id="salespersonName" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.salespersonName)!}" readonly>
			</div>
		</div>
		<div class="col-sm-3 marginTop5">
			<div class="input-group">
				<label class="input-group-addon">开票员</label>
				<input type="text" name= "saasPurchaseRetrieveBillInfoVO.billingUser" id="billingUser" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.billingUser)!}" required="required" readonly >
			</div>
		</div>
		<div class="col-sm-3 marginTop5">
			<div class="input-group">
				<label class="input-group-addon">上级单号</label>
				<input type="text" name= "saasPurchaseRetrieveBillInfoVO.parentBillNo" id="parentBillNo" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.parentBillNo)!}" readonly>
			</div>
		</div>
		<div class="col-sm-3 marginTop5">
			<div class="input-group">
				<label class="input-group-addon">冲价总金额</label>
				<input type="text" name= "saasPurchaseRetrieveBillInfoVO.taxAmountSum" id="taxAmountSum" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.taxAmountSum)!?c}" readonly>
			</div>
		</div>
		<div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">运输方式</label>
                <input type="text" name= "transportationMode" id="transportationMode" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.transportationMode)!}">
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">运输单号</label>
                <input type="text" name= "transportationCode" id="transportationCode" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.transportationCode)!}">
            </div>
        </div>
		<div class="col-sm-3 marginTop5">
			<div class="input-group">
				<label class="input-group-addon">备注</label>
				<input type="text" name= "saasPurchaseRetrieveBillInfoVO.remarks" id="remarks" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.remarks)!}" readonly>
			</div>
		</div>
	</form>
</div>
<div class="panel panel-default publicPanel" id="b_table">
	<table id="grid-table"></table>
	<div id="grid-pager"></div>
</div>
</body>
<script src="/static/js/purchase/purchaseOutTicketDetail.js?v=${jsVersion}"></script>

</html>
