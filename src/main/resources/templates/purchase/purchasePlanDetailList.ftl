<!DOCTYPE html>
<html lang="en">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>Dashboard - Ace Admin</title>

		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
	</head>
	<body id="innerBody">
		<input type="hidden" id="orderMedicineType" name="orderMedicineType" value="${orderMedicineType!}">
		<div class="loadingBlock" id="loadPop" style="display: none">
			<div class="la-ball-spin-clockwise la-2x">
				<div></div>
				<div></div>
				<div></div>
				<div></div>
				<div></div>
				<div></div>
				<div></div>
				<div></div>
			</div>
		</div>
		<div id="toolbox" class="toolbox newToolbox">
            <button onclick="location.href='/purchase/plan/toList'" class="btn  btn-return">返回</button>
            <#if plan?? && plan.status?? && plan.status ==1><button id="editPurchase" class="btn btn-redactbag">完成采购</button></#if>
			<#if plan?? && plan.status?? && plan.status ==1><button id="addProduct" class="btn btn-addbag">添加商品</button></#if>
			<button id="print" class="btn btn-return btn-print">打印</button>
			<button id="exportExcel" class="btn btn-return btn-exportexcel">导出Excel</button>
		</div>
		<div class="panel panel-default publicPanel newPanel">
			<input type="hidden" id="planId"  value="${planId!""}">
            <input type="hidden" id="billNo"  value="${plan.billNo!""}">
            <input type="hidden" id="status"  value="${plan.status!""}">
            <input type="hidden" id="supplierNo" value="${plan.supplierNo!""}">
			<form class="row form-inline">
				<div class="col-sm-4 marginTop5" style="padding-left: 70px;">
                    供应商:<span id="supplierName">${plan.supplierName!""}</span>
				</div>
				<div class="col-sm-8 marginTop5">
                    注册地址:<span id="registeredAddress">${plan.registeredAddress!""}</span>
				</div>
                <input id="proTypeList" type="hidden" value='${proTypeList!""}' />
			</form>
		</div>
		<div class="panel panel-default publicPanel" id="b_table">
                <table id="grid-table"></table>
		</div>
	</body>
    <script src="/static/js/purchase/purchasePlanDetailList.js?v=${jsVersion}"></script>

</html>
