<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>Dashboard - Ace Admin</title>
		<meta name="google" content="notranslate" />
		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
		<#include "/common/purchase-menu.ftl"/>
		<style>
			.saas-message {
				position: fixed;
				left: 50%;
				min-width: 280px;
				transform: translateX(-50%);
				top: 20px;
				background-color: #f0f9eb;
				border: 1px solid #e1f3d8;
    			z-index: 2000;
				transition: opacity .3s,transform .4s,top .4s,-webkit-transform .4s;
				overflow: hidden;
				height: 48px;
				line-height: 48px;
				padding: 0 15px;
				border-radius: 4px;
				color: #67C23A;
				display: none;
			}

			#grid-table .showText{
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
			}
			#gbox_grid-table .btn{ margin-right: 0!important; }

			.commonValidate .col-xs-3:not(.search) {
				width: 20% !important;
			}
			.commonValidate{
				padding: 2px 0px;
			}
			.marginTop5{
				margin-top: 12px
			}
			.dialogShow span{
				display: block;
				float: left;
				width: 130px;
				padding: 20px 0;
				background-color: #EDEDED;
				margin-left: 50px;
				text-align: center;
				color: #333333;
				font-size: 16px;
			}
			.dialogShow span:hover, .dialogShow span.active{
				background-image: linear-gradient(-180deg, #35C3FF 0%, #1890FF 100%) !important;
				color: #fff;
				cursor: pointer;
			}
			.dialogShow p{
				line-height: 80px;
				display: block;
				clear: both;
				font-size: 16px;
				text-align: center;
			}
			.ybm-bill-list {
				padding: 10px 30px;
				
			}
			.ybm-bill-list table {
				width: 100%;
				border-collapse: collapse;
    			border-spacing: 0px;
			}
			.ybm-bill-list table td {
				text-align: center;
				padding: 5px;
    			border: 1px solid black;
			}
			.ybm-bill-list .title {
				font-weight: 500;
				text-align: center;
				font-size: 16px;
				margin: 10px 0;
			}
			.ybm-bill-list .msg {
				margin-bottom: 10px;
			}
			.ybm-bill-list .wait {
				color: red;
				cursor: pointer;
			}
			.disable-dialog {
				margin-top: 6px;
			}
			.disable-dialog span {
				vertical-align: top;
			}
			.ybm-dialog-footer {
				padding: 0 10px;
				text-align: center;
			}
			.ybm-dialog-footer .span-btn {
				display: inline-block;
				width: 90px;
				height: 32px;
				line-height: 32px;
				text-align: center;
				cursor: pointer;
				border-radius: 4px;
				border: 1px solid #6b6a6a;
			}
			.ybm-dialog-footer .span-btn:first-child {
				margin-right: 60px;
			}
			.ybm-btn {
				display: none;
			}
		</style>
	</head>
	<body id="innerBody" class="notranslate">
		<div class="saas-message"><span class="message">正在检查是否有药帮忙订单</span></div>
		<div class="loadingBlock" id="loadPop" style="display: none">
			<div class="la-ball-spin-clockwise la-2x">
				<div></div>
				<div></div>
				<div></div>
				<div></div>
				<div></div>
				<div></div>
				<div></div>
				<div></div>
			</div>
		</div>
		<div id="purchaseTab" class="tabBar"></div>
		<div id="toolbox" class="toolbox newToolbox topLine">
			<button id="ybmOrder" type="button" class="btn btn-redactbag ybm-btn">药帮忙订单入库</button>
			<#--  <a href="/purchase/bill/toAddOrEditPurchase?type=01" class="btn btn-addbag btn-default btn-round"> 新增</a>  -->
			<button id="addPurPlan" type="button" class="btn btn-redactbag">新增</button>
			<#--  <a href="#" id="editPurchase" class="btn btn-redactbag btn-default btn-round">  编辑</a>
			<a href="#" id="deletePurchase" class="btn btn-removebag btn-default btn-round"> 删除</a>  -->
            <a href="#" id="planOrderBtn"   class="btn btn-redactbag btn-default btn-round">生成电子计划单</a>
			<button id="exportExcel" type="button" class="btn btn-redactbag">导出Excel</button>
		</div>
		<div class="panel-default publicPanel newPanel">
			<input type="hidden" id="beginTimeHidden" name="beginTimeHidden" value="${beginTime}"/>
			<input type="hidden" id="endTimeHidden" name="endTimeHidden" value="${endTime}"/>
			<form class="commonValidate">
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">单据信息</label>
                        <input lit="seach" type="text" name="billNo" placeholder="单据编号/供应商名称/供应商编号" id="billNo" class="form-control">
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">采购员</label>
                        <div class="dropdown">
                            <span class="caret"></span>
                            <input type="hidden" value="" id="billingUser">
                            <input type="button" class="dropdown-toggle select-btn" data-toggle="dropdown" value="全部" aria-haspopup="true" aria-expanded="false">
                            <ul class="dropdown-menu select-single">
                                <li><a href="">全部</a></li>
								<#if (employeeDtos)??>
									<#list employeeDtos as dtos>
                                    <li><a href="#{dtos.id}">${dtos.name}</a></li>
									</#list>
								</#if>
                            </ul>
                        </div>
                    </div>
                </div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">处理状态</label>
						<div class="dropdown">
							<span class="caret"></span>
							<input type="hidden" value="" id="status">
							<input type="button" class="dropdown-toggle select-btn" data-toggle="dropdown" value="全部" aria-haspopup="true" aria-expanded="false">
							<ul class="dropdown-menu select-single">
								<li><a href="">全部</a></li>
								<li><a href="03">待收货</a></li>
								<li><a href="01">暂存</a></li>
								<li><a href="02">已完成</a></li>
							</ul>
						</div>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">开始时间</label>
						<input lit="seach" type="text" name="beginTime" value="${beginTime}" id="beginTime" class="Wdate form-control"   onfocus="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}'})" style="height: inherit;"/>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">结束时间</label>
						<input lit="seach" type="text" name="endTime" value="${endTime}" id="endTime" class="Wdate form-control"  onfocus="WdatePicker({minDate:'#F{$dp.$D(\'beginTime\')}'})" style="height: inherit;"/>
					</div>
				</div>
                <#--  <div class="col-xs-3 marginTop5">
					<div class="input-group">
                        <label class="input-group-addon">状态</label>
                        <div class="dropdown">
                            <span class="caret"></span>
                            <input type="hidden" value="00" id="status">
                            <input type="button" class="dropdown-toggle select-btn" data-toggle="dropdown" value="全部" aria-haspopup="true" aria-expanded="false">
                            <ul class="dropdown-menu select-single">
                                <li><a href="00">全部</a></li>
                                <li><a href="01">待收货</a></li>
                                <li><a href="02">已完成</a></li>
                            </ul>
                        </div>
					</div>
				</div>  -->

				<div class="col-xs-3 marginTop5">
					<div class="input-group" >
						<label class="input-group-addon">备注</label>
						<div style="position: relative">
							<input type="text" lit="seach" placeholder="" name="note" id="note" class="form-control input-sm" value="">
							<div class="hot-list"></div>
						</div>
					</div>
				</div>
				<div class="col-xs-8 marginTop5" style="width: 80%">
					<div class="input-group">
						<button id="queryBtn" type="button" class="btn btn-redactbag forbidRepeatSearch">  查询</button>
						<button id="disQueryCondition" type="button" class="btn btn-return">   重置</button>
                        <button class="btn btn-redactbag" id="filtrkolumn">筛选列</button>
					</div>
				</div>
			</form>
		</div>
		<div class="panel panel-default publicPanel" id="b_table">
                <table id="grid-table"></table>
                <div id="grid-pager" style="margin-top: 20px !important;"></div>
		</div>
	</body>
    <script src="/static/js/purchase/purchasePlanList.js?v=${jsVersion}"></script>

</html>

