<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>Dashboard - Ace Admin</title>
		<meta name="google" content="notranslate" />
		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<link rel="stylesheet" href="/static/assets/css/element-ui.min.css" />
		<#include "/common/top-common.ftl"/>
		<style>
			.info-icon{
				display: inline-block;
				background: #000;
				border: 1px solid #000;
				border-radius: 50%;
				/* width: 14px; */
				/* height: 14px; */
				color: #fff;
				line-height: 14px;
				color: #FFFFFF;
				font-size: 12px;
				/* font-weight: bold; */
				text-align: left;
				padding: 0px 5px 0px 4px;
				position: relative;
			}
			.info-icon .tips-msg{
				display: none;
				background: #000;
				border: 1px solid #000;
				border-radius: 2px;
				color: #fff;
				width: 200px;
				height: 20px;
				line-height: 20px;
				text-align: center;
				position: absolute;
				left: -5px;
				top: 22px;
				z-index: 10;
			}
			.info-icon .tips-msg:before{
				content: '';
				display: block;
				border-left: 5px solid rgba(0,0,0,0);
				border-right: 5px solid rgba(0,0,0,0);
				border-top: 5px solid rgba(0,0,0,0);
				border-bottom: 5px solid #000;
				width: 0px;
				height: 0px;
				position: absolute;
				top: -11px;
				left: 5px;
			}
			.newPanel{
				margin: 8px 0 0 0;
			}
			.tabWrap{
				position: relative;
				width: 100%;
				height: 40px;
				display: flex;
				flex-flow: row nowrap;
				background: #eee;
			}
			.tabWrap .new-version {
				width: 107px;
				height: 28px;
				position: absolute;
				right: 20px;
				top: 6px;
				cursor: pointer;
			}
			.tabWrap > div {
				width: 115px;
				height: 40px;
				line-height: 40px;
				text-align: center;
			}
			.tabWrap > div.active{
				color: #2798fa;
				border-bottom: 1px solid;
			}

			.tips{
				margin-top:5px;
				<#--  width: calc(100% - 230px);  -->
				color: #707070;
				text-align:center;
			}
			.inputGroups{
				flex: 1
			}
			#b_table001 .ui-jqgrid-bdiv{
				height: 88px !important;
			}
			#b_table #grid-pager {
				position: absolute;
				bottom: 9px!important;
				width: 100% !important;
			}
			#b_table {
				position: static;
				margin-top: 0 !important;
				margin-bottom: 0 !important;
			}
			.el-dialog {
				border-radius: 6px;
			}
			.el-dialog__header {
				border-radius: 6px 6px 0 0;
				background-color: rgb(246, 246, 246);
			}
			.footer-btns {
				position: relative;
				padding: 0;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
			.el-dialog__body {
				height: 150px;
			}
			.condition-dialog-content {
				display: flex;
				justify-content: center;
    			align-items: center;
			}
			.condition-dialog-content .label-name {
				width: 100px;
			}
			.condition-dialog-content .label-name:before {
				content: "*";
				color: #f56c6c;
				margin-right: 4px;
			}
			.condition-dialog-content .el-input {
				margin-right: 4px;
			}
			.dialog-content .title {
				font-weight: 500;
				text-align: center;
			}
			.el-icon-loading:before {
				font-size: 36px;
			}
			.loading {
				text-align: center;
			}
			.dialog-content .result {
				display: flex;
				justify-content: center;
				align-items: center;
				text-align: center;
			}
			.dialog-content .el-input {
				width: 50%;
			}
			.el-table-custom-column .item {
				display: flex;
				justify-content: center;
				align-items: center;
				color: rgb(51, 51, 51);
			}
			.el-table-custom-column .item .left {
				width: 45%
			}
			.el-table-custom-column .item .middle {
				width: 30%
			}
			.el-table-custom-column .item .right {
				width: 25%
			}
			.el-table thead {
				color: rgba(0, 0, 0, 0.85);
			}
			.dialog-search {
				display: flex;
				flex-wrap: wrap;
			}
			.dropdown {
				width: 140px;
			}
		</style>
	</head>
	<body id="innerBody">
		<div id="vue-ele">
			<div class="tabWrap">
				<div class="active" data-type="0">
					药帮忙订单
					<span class="info-icon">!<span class="tips-msg">展示小药药自营和第三方商家订单</span>
					</span>
				</div>
				<div data-type="1">第三方订单</div>
				
				<img class="new-version" @click="openNewVersion" src="/static/assets/images/common/new_version.png" />
				
			</div>
			<div class="panel panel-default publicPanel newPanel">
				<form class="row dialog-search">
					<div class="marginTop5 bind-code">
						<div class="input-group">
							<label class="input-group-addon">客户编码</label>
							<input type="text" lit="seach" value="${bindCode!}" id="bindCode" class="form-control" disabled>
						</div>
					</div>
					<div class="marginTop5">
						<div class="input-group">
							<label class="input-group-addon">单据编号</label>
							<input type="text" lit="seach" id="businessId" class="form-control">
						</div>
					</div>
					<div class="marginTop5">
						<div class="input-group">
							<label class="input-group-addon">开票开始日期</label>
							<el-date-picker
							v-if="type === 0"
							v-model="startTime"
							type="date"
							placeholder="选择日期"
							value-format="yyyy-MM-dd"
							:picker-options="pickerStartOptions"
							>
							</el-date-picker>
							<input v-else type="text" id="startTime" lit="seach" name="startTime"
								class="Wdate form-control"  data-toggle="tooltip"
								data-placement="bottom" title=""
								onfocus="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}'})"
									<#if beginTime??>value="${beginTime}"</#if>
								style="height: inherit;" autocomplete="off" />

						</div>
					</div>
					<div class="marginTop5">
						<div class="input-group">
							<label class="input-group-addon">开票结束日期</label>
							<el-date-picker
								v-if="type === 0"
								v-model="endTime"
								type="date"
								:picker-options="pickerEndOptions"
								value-format="yyyy-MM-dd"
								placeholder="选择日期">
							</el-date-picker>
							<input v-else type="text" lit="seach"  id="endTime" name="endTime" class="Wdate form-control"   <#if endTime??>value="${endTime}"</#if>data-toggle="tooltip" data-placement="bottom" title=""
								onfocus="WdatePicker({minDate:'#F{$dp.$D(\'startTime\')}'})" style="height: inherit;"  autocomplete="off"/>
						</div>
					</div>
					<div class="marginTop5" v-show="type === 0">
						<div class="input-group">
							<label class="input-group-addon">入库状态</label>
							<div class="dropdown">
								<span class="caret"></span>
								<input type="hidden" value="" id="warehousingStatus">
								<input type="button" class="dropdown-toggle select-btn" data-toggle="dropdown" value="全部" aria-haspopup="true" aria-expanded="false">
								<ul class="dropdown-menu select-single">
									<li><a href="">全部</a></li>
									<li><a href="0">未提取</a></li>
									<li><a href="1">已收货</a></li>
									<li><a href="2">已入库</a></li>
								</ul>
							</div>
						</div>
					</div>
					<div class="marginTop5" style="display:flex">
						<div class="inputGroups">
							<button id="queryBtn" type="button" class="btn btn-redactbag">  查询</button>
							<button id="resetBtn" type="button" class="btn btn-return">重置</button>
							<button v-if="type === 0" type="button" class="btn btn-return" @click="handleOpenSyncDialog('syncDialogVisible')">同步商城订单</button>
							<button v-if="type === 0" type="button" class="btn btn-return" @click="handleOpenSyncDialog('syncConditionDialogVisible')">按条件同步订单</button>
						</div>
					</div>
				</form>
			</div>
			<span class="tips"  id="orderTips">双击列表中的订单单据，可根据本店的商品信息关联的标准库ID自动匹配该订单中商品</span>
			<div class="panel panel-default publicPanel" id="b_table">
				<#-- 药帮忙订单 -->
				<table id="grid-table"></table>
				<#-- 第三方订单 -->
				<div id="grid-pager"></div>
			</div>
			<div class="panel panel-default publicPanel" id="b_table001" style="width: 100%;">
				<table id="grid-table-detial"></table>
			</div>
			<div class="footer-btns text-right btn_sub_can_div" style="bottom:0">
				<div class="left">
					<a id="notWarehouseOeder" style="float: left;color: #000;text-decoration: underline;cursor: pointer">查询未出仓订单</a>
					<a id="warehouseRecord" style="float: left;color: #000; margin-left: 20px;text-decoration: underline;cursor: pointer">入库记录</a>
				</div>
				<div class="right">
					<button type="button" class="btn btn-return" id="btn_modalClose">取消</button>
					<button  type="button" class="btn btn-redactbag" id="btn_submit">确定</button>
					
				</div>
			</div>
			<template>
				<el-dialog
					title="温馨提示"
					:visible.sync="syncDialogVisible"
					:show-close="false"
					@close="closeDialog(true)"
					width="60%"
				>
					<div class="dialog-content">
						<h4 class="title">{{ syncStatus === 3 ? '数据同步中......' : '数据同步完成'}}</h4>
						<div class="result" v-if="syncStatus === 1 && !successTotal">
							⚠️当前没有可同步的商城订单
						</div>
						<div class="result" v-if="syncStatus === 1 && successTotal">✅已同步{{successTotal}}条商城订单</div>
						<div class="result" v-if="syncStatus === 2">❌同步失败,请稍后再试</div>
					</div>
					<span slot="footer" class="dialog-footer" v-if="syncStatus !== 3">
						<el-button type="primary" @click="syncDialogVisible = false">确 定</el-button>
					</span>
				</el-dialog>
				<el-dialog
					title="数据同步"
					:visible.sync="syncConditionDialogVisible"
					width="60%"
					@close="closeDialog(false)"
				>
					<div class="condition-dialog-content">
						<span class="label-name">商城单号</span>
						<el-input v-model="ecOrder" placeholder="请输入商城订单号"></el-input>
						<el-button type="primary" @click="handleSyncPref">按单号同步</el-button>
					</div>
				</el-dialog>
			</template>
		</div>
	</body>
	<script src="/static/assets/js/vue.min.js?v=${jsVersion}"></script>
	<script src="/static/assets/js/element-ui.min.js?v=${jsVersion}"></script>
	<#--  <script type="text/x-template" id="async-dialog">
		<p>sss</p>
	</script>  -->
    <script src="/static/js/purchase/purchasePopECOrderList.js?v=${jsVersion}"></script>
</html>

