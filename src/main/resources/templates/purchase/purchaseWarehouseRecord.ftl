<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="utf-8" />
    <title>Dashboard - Ace Admin</title>
    <meta name="google" content="notranslate" />
    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="stylesheet" href="/static/assets/css/element-ui.min.css" />
    <#include "/common/top-common.ftl"/>
    <style>
        .el-button {
            border-radius: 4px;
        }
        .el-button--primary:hover {
            color: #FFF !important;
            background: linear-gradient(180deg,#1890ff,#096dd9)
        }
        .el-form--inline {
            padding: 10px 10px 0 10px;
        }
        .operation-btns {
            text-align: right;
        }
        .el-form-item {
            margin-bottom: 0;
        }
        .table-wrapper {
            padding: 10px;
        }
        .table-wrapper .el-table__header {
            border-bottom: 1px solid #ccc;
        }
        .table-wrapper .el-table th {
            border-color: #ccc;
            background-color: #E8E8E8;
        }
        .el-table thead {
            color: #333 !important;
        }
        .table-wrapper .el-table--border, .el-table--group {
            border: 1px solid #ccc;
        }
        .table-wrapper .el-table--border th.el-table__cell{
            border-bottom: 1px solid #EBEEF5;
        }
        .table-wrapper .el-table--border td {
            border-right: 1px solid #EBEEF5;
        }
        .table-wrapper .el-table td {
             border-bottom: 1px solid #EBEEF5;
        }
        .table-wrapper .el-table td {
            border-color: #ccc;
        }
        .table-wrapper .el-table__body tr:hover>td.el-table__cell {
            background: #b9ddff !important;
        }
        .table-wrapper .tr.current-row>td {
            background: #b9ddff !important;
        }
        .common-pagination {
            padding-bottom: 1px;
            box-shadow: 0px 8px 15px 0px rgb(149 156 182 / 15%);
        }
        .common-pagination .el-pagination {
            margin-bottom: 5px;
            padding: 0;
            text-align: right;

        }
        .el-pagination__total {
            height: 30px !important;
            line-height: 30px !important;
        }
        .el-input__inner {
          width: 93px;
          height: 28px;
          background: rgba(255, 255, 255, 1);
          border-radius: 2px;
          border: 1px solid rgba(132, 143, 169, 1);
        }
        .el-input {
            margin: 0;
        }
        .el-pager {
            box-sizing: border-box;
            height: 28px;
            line-height: 28px;
            border: 1px solid rgb(104, 107, 112);
        }
        .el-pager >li {
            height: 100%;
            line-height: 26px;
            border-right: 1px solid rgba(132, 143, 169, 1);
        }
        .el-pager >li:hover {
            color: rgb(104, 107, 112);
        }
        .el-pager >li:last-child  {
            border-right: none !important;
        }
        .el-pager >li.active {
            background: #404159;
            color: #fff;
        }
        button:not(:disabled):hover {
            color: rgb(104, 107, 112);
        }
        .btn-prev,.btn-next {
            width: 58px;
            height: 28px;
            padding: 0;
            text-align: center;
            background: rgba(255, 255, 255, 1);
            border-radius: 2px;
            border: 1px solid rgba(132, 143, 169, 1);
        }
        .btn-prev {
            margin-right: 8px;
        }
        .el-pagination__jump{
            margin-left: 0px;
            margin-right: 10px;
        
        }
        .el-input__inner{
            border: 1px solid #848fa9;
            border-radius: 2px;
        }
        .back {
            padding: 6px 14px 0 0;
            text-align: right;
        }
        .back .el-button {
            width: 80px;
        }
        .export-name {
            display: inline-block;
            width: calc(100% - 204px);
        }
        .el-dialog__header {
            background: #f6f6f6;
            border-bottom: 1px solid #e5e5e5;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px
        }
        .el-dialog__footer {
            background: #f6f6f6;
            border-top: 1px solid #e5e5e5;
            padding: 10px 20px;
            border-bottom-left-radius: 14px;
            border-bottom-right-radius: 14px;
        }
    </style>
</head>
<body id="innerBody">
<div id="vue-ele">
    <el-form :inline="true" :model="formModel" ref="elForm" label-width="90px">
        <el-form-item label="商品">
            <el-input v-model="formModel.product" size="mini" placeholder="请输入商品名称"></el-input>
        </el-form-item>
        <el-form-item label="采购入库单">
            <el-input v-model="formModel.billNo" size="mini" placeholder="请输入采购入库单号"></el-input>
        </el-form-item>
        <el-form-item label="商城单号">
            <el-input v-model="formModel.businessId" size="mini" placeholder="请输入商城单号"></el-input>
        </el-form-item>
        <el-form-item label="开票时间">
            <div class="date-picker-wrapper">
                <el-date-picker
                    v-model="formModel.beginTime"
                    type="date"
                    :picker-options="pickerStartOptions"
                    value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <span class="ll-date-picker-range-separator">-</span>
                <el-date-picker
                    v-model="formModel.endTime"
                    type="date"
                    :picker-options="pickerEndOptions"
                    value-format="yyyy-MM-dd"
                >
                </el-date-picker>
             </div>
        </el-form-item>
        <div class="operation-btns">
            <el-button type="primary" size="small" @click="handleQuery">查询</el-button>
            <el-button type="primary" size="small" @click="handleReset">重置</el-button>
            <el-button type="primary" size="small" @click="handleExport">导出Excel</el-button>
        </div>
    </el-form>
    <div class="table-wrapper">
        <el-table
        :data="tableData"
        :border="true"
        :highlight-current-row="true"
        :height="tableHeight"
        style="width: 100%">
        <el-table-column align="center" type="index" label="序号" width="60">
        <template slot-scope="{ row, $index }">
          <span>{{ (pagination.page - 1) * pagination.rows + $index + 1 }}</span>
        </template>
      </el-table-column>
        <el-table-column
            v-for="(item, index) in colModelList"
            :prop="item.prop"
            :show-overflow-tooltip="true"
            :label="item.label"
            align="center"
            width="180">
        </el-table-column>
        
         </el-table>
    </div>
    <div class="common-pagination">
        <el-pagination
        :total="pagination.total"
        :page-count="pagination.total"
        :current-page.sync="pagination.page"
        :layout="pagination.layout"
        :page-size="pagination.rows"
        :page-sizes="pagination.pageSizes"
        prev-text="上一页"
        next-text="下一页"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        >
        </el-pagination>
    </div>
    <div class="back">
        <el-button size="small" @click="handleBack" >返回</el-button>
    </div>
    <el-dialog
        v-loading="loading"
        :visible.sync="isShow"
        :element-loading-text="loadingText"
        :modal="false"
        width="500px"
        title="提示"
        top="30vh"
        append-to-body
        @close="close"
    >
        <div class="">
            <span>请输入下载的Excel名称：</span>
            <el-input class="export-name" v-model="exportName">
        </div>
        <div slot="footer">
            <el-button size="small" @click="close"> 取 消 </el-button>
            <el-button type="primary" size="small" @click="handleDlgOk">确 定</el-button>
        </div>
    </el-dialog>
</div>
</body>
<script src="/static/assets/js/vue.min.js?v=${jsVersion}"></script>
<script src="/static/assets/js/element-ui.min.js?v=${jsVersion}"></script>
<script src="/static/js/purchase/purchaseWarehouseRecord.js?v=${jsVersion}"></script>

</html>

