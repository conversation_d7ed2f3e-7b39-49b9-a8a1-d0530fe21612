<!DOCTYPE html>
<html lang="en">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>Dashboard - Ace Admin</title>

		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
        <#include "/common/purchase-menu.ftl"/>
	</head>
    <style>

        #grid-table .showText{
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
        .commonValidate{
            padding: 2px 0px;
        }
        .marginTop5{
            margin-top: 12px
        }
        #gbox_grid-table .btn{ margin-right: 0!important; }
        .commonValidate .col-xs-3:not(.search) {
            width: 20% !important;
        }
        .dialogShow span{
            display: block;
            float: left;
            width: 130px;
            padding: 20px 0;
            background-color: #EDEDED;
            margin-left: 50px;
            text-align: center;
            color: #333333;
            font-size: 16px;
        }
        .dialogShow span:hover, .dialogShow span.active{
            background-image: linear-gradient(-180deg, #35C3FF 0%, #1890FF 100%) !important;
            color: #fff;
            cursor: pointer;
        }
        .dialogShow p{
            line-height: 80px;
            display: block;
            clear: both;
            font-size: 16px;
            text-align: center;
        }
        .dialogShow > div{
            line-height: 60px;
            font-size: 14px;
        }
    </style>
	<body id="innerBody">
		<div class="loadingBlock" id="loadPop" style="display: none">
			<div class="la-ball-spin-clockwise la-2x">
				<div></div>
				<div></div>
				<div></div>
				<div></div>
				<div></div>
				<div></div>
				<div></div>
				<div></div>
			</div>
		</div>
        <div id="purchaseTab" class="tabBar"></div>
		<div id="toolbox" class="toolbox newToolbox topLine">
            <#--  <a href="/purchase/plan/toAdd" class="btn btn-addbag">新增</a>  -->
            <button id="addPlan" class="btn btn-redactbag">新增</button>
            <button id="goToMallPurchase" class="btn btn-redactbag">商城采购</button>
            <button id="exportExcel" type="button" class="btn btn-redactbag">导出Excel</button>
            <a href="#" data-id="" id="editPurchase" style="display: none;"></a>
            <#--  <a href="#" id="deletePurchase" class="btn btn-removebag">删除</a>
            <a href="#" id="editPurchase" class="btn btn-redactbag">去采购</a>  -->
        </div>
		<div class="panel-default publicPanel newPanel ">
			<input type="hidden" id="beginTimeHidden" value="${beginTime}">
			<input type="hidden" id="endTimeHidden" value="${endTime}">
            <input type="hidden" id="organSign" value="${organSign}">
            <input type="hidden" id="userId" value="${userId}">
			<form class="commonValidate" style="padding-top: 4px;">
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">单据信息</label>
                        <input lit="seach" type="text" name="billNo" placeholder="计划单号" id="billNo" class="form-control">
                    </div>
                </div>

                <#--<div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">创建人</label>
                        &lt;#&ndash;  <input type="text" lit="seach" name="createUser" id="createUser" class="form-control">  &ndash;&gt;
                        <select id="createUser" style="border: 1px solid #bababa; height: 36px; border-radius: 4px 0px 0px 4px;"></select>
                    </div>
                </div>-->

                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">创建人</label>
                        <div class="dropdown">
                            <span class="caret"></span>
                            <input type="hidden" value="" id="createUser">
                            <input type="button" class="dropdown-toggle select-btn" data-toggle="dropdown" value="全部" aria-haspopup="true" aria-expanded="false">
                            <ul class="dropdown-menu select-single">
                                <li><a href="">全部</a></li>
                                <#if (employeeDtos)??>
                                    <#list employeeDtos as dtos>
                                        <li><a href="#{dtos.id}">${dtos.name}</a></li>
                                    </#list>
                                </#if>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">处理状态</label>
                        <div class="dropdown">
                            <span class="caret"></span>
                            <input type="hidden" value="" id="status">
                            <input type="button" class="dropdown-toggle select-btn" data-toggle="dropdown" value="全部" aria-haspopup="true" aria-expanded="false">
                            <ul class="dropdown-menu select-single">
                                <li><a href="">全部</a></li>
                                <li><a href="1">待执行</a></li>
                                <li><a href="2">已执行</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">开始时间</label>
                        <input lit="seach" type="text" name="beginTime" value="${beginTime}" id="beginTime" class="Wdate form-control"   onfocus="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}'})" style="height: inherit;"/>
                    </div>
                </div>

                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">结束时间</label>
                        <input lit="seach" type="text" name="endTime" value="${endTime}" id="endTime" class="Wdate form-control"  onfocus="WdatePicker({minDate:'#F{$dp.$D(\'beginTime\')}'})" style="height: inherit;"/>
                    </div>
                </div>



                <#--  <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">供应商</label>
                        <input lit="seach" type="text" name= "supplierName" id="supplierName" class="form-control">
                    </div>
                </div>  -->

                <#--  <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">状态</label>
                        <div class="dropdown">
                            <span class="caret"></span>
                            <input type="hidden" value="" id="status" name="status">
                            <input type="button" class="dropdown-toggle select-btn"
                                   data-toggle="dropdown"
                                   value="全部"
                                   aria-haspopup="true"
                                   aria-expanded="false">
                            <ul class="dropdown-menu select-single">
                                <li><a href="">全部</a></li>
                                <li><a href="1">待处理</a></li>
                                <li><a href="2">已提交</a></li>
                            </ul>
                        </div>
                    </div>
                </div>  -->
                <div class="col-xs-12 marginTop5 search" >
                    <div class="input-group">
                        <button id="queryBtn" type="button" class="btn btn-redactbag forbidRepeatSearch">查询</button>
                        <button id="disQueryCondition" type="button" class="btn btn-return">重置</button>
                        <button class="btn btn-redactbag" id="filtrkolumn">筛选列</button>
                    </div>
                </div>
			</form>
		</div>
		<div class="panel panel-default publicPanel" id="b_table">
                <table id="grid-table"></table>
                <div id="grid-pager" style="margin-top: 20px !important;"></div>
		</div>
	</body>
    <script src="/static/js/purchase/purchasePlanListNew.js?v=${jsVersion}"></script>

</html>

