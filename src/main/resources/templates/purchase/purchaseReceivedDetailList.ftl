<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="utf-8" />
    <title>Dashboard - Ace Admin</title>
    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
</head>
<body id="innerBody">
    <div id="toolbox" class="toolbox newToolbox">
        <button id="exportBtn" type="button" class="btn btn-redactbag" >导出Excel</button>
        <button type="button" class="btn btn-redactbag" id="summary-filter">汇总统计</button>
    </div>
    <div class="panel panel-default publicPanel newPanel">
        <form class="row form-inline" id="searchForm">
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">开始时间</label>
                    <input lit="seach" type="text" name="beginTime" value="${beginTime}" id="beginTime" class="Wdate form-control"   onfocus="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}'})" style="height: inherit;"/>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">结束时间</label>
                    <input lit="seach" type="text" name="endTime" value="${endTime}" id="endTime" class="Wdate form-control"  onfocus="WdatePicker({minDate:'#F{$dp.$D(\'beginTime\')}'})" style="height: inherit;"/>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">单据编号</label>
                    <input lit="seach" type="text" name="billNo" id="billNo" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">复核员</label>
                    <input type="text" lit="seach" name="billingUser" id="billingUser" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">商品</label>
                    <input type="text" lit="seach" name="productName" id="productName" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">供应商</label>
                    <input lit="seach" type="text" name= "supplierName" id="supplierName" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">生产厂家</label>
                    <input lit="seach" type="text" name= "manufacturer" id="manufacturer" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group" style="text-align: right;line-height: 30px;">
                    <button id="queryBtn" type="button"  class="btn btn-redactbag forbidRepeatSearch" >查询</button>
                    <button id="resetBtn" type="button"  class="btn btn-return forbidRepeatSearch" >重置</button>
                    <button  class="btn btn-redactbag" type="button"  id="filtrkolumn">筛选列</button>
                </div>
            </div>
        </form>
    </div>
    <div class="panel panel-default publicPanel" id="b_table">
        <table id="grid-table"></table>
        <div id="grid-pager" style="margin-top: 20px !important;"></div>
    </div>
    <div class="row" style="position: absolute;bottom: 0;width: 100%;">
        <div class="marginTop5" style="display: inline-block; margin-right: 30px;">
            <div class="input-group">
                <label >数量合计：</label><label id="productAmountCount"></label>
            </div>
        </div>
        <div class="marginTop5" style="display: inline-block;">
            <div class="input-group">
                <label >折后金额合计：</label><label id="productDiscountTaxPriceSumCount" ></label>
            </div>
        </div>
    </div>
    <#--汇总统计弹窗-->
    <div class="panel panel-default publicPanel newPanel" style="display: none">
        <table id="count-table"></table>
    </div>
    <#include "../common/tb-filter-statistic.ftl"/>
</body>
<script src="/static/js/purchase/purchaseReceivedDetailList.js?v=${jsVersion}"></script>
</html>