<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>Dashboard - Ace Admin</title>

		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />

		<#include "/common/top-common.ftl"/>
	</head>
	<body id="innerBody">
		<!-- 工具栏 -->
		<div id="dictionary_tools" class="toolbox">
		    <#if requestStatus??>
                <button onclick="location.href='/gsp/purchase/toPurcharOrderList'" class="btn btn-return"> 返回</button>
            <#else>
                <button onclick="location.href='/purchase/bill/toPlanList'" class="btn btn-return"> 返回</button>
				</#if>
				<button id="print" class="btn btn-return btn-print" style="display: none;">打印</button>


			<#--  <button id="formSave" class="btn btn-addbag" disabled>  保存</button>
			<button id="updateStatus" class="btn btn-redactbag" disabled>  提交</button>  -->
		</div>
		<div class="panel panel-default publicPanel rkys cgdd">
				<form class="validate" action="save" method="post">
					<input type="hidden" id="billId" name="id" value="${(saasPurchaseBillInfoPo.id)!}">
					<input type="hidden" id="billNo" name="billNo" value="${(saasPurchaseBillInfoPo.billNo)!}">
					<input type="hidden" name="billType" value="01">
					 <#if (purchasePlanInfo)??>
                    <div class="col-xs-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon">计划编号</label>
                            <input type="text" value="${purchasePlanInfo.billNo!}" class="form-control" readonly>
                        </div>
                    </div>
                    <div class="col-xs-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon">创建人</label>
                            <input type="text"  value="${purchasePlanInfo.billingUser!}" class="form-control" readonly>
                        </div>
                    </div>
                    <div class="col-xs-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon">计划创建日期</label>
                            <input type="text" value="${(purchasePlanInfo.billTime)?string("yyyy-MM-dd")}" class="form-control" readonly>
                        </div>
                    </div>
                    <div class="col-xs-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon"></label>
                            <label class="input-group-addon"></label>
                        </div>
                    </div>
					 </#if>
					<div class="row" style="padding-bottom: 10px;">
					<div class="col-sm-3 marginTop5">
						<div class="input-group">
							<label class="input-group-addon">单据编号</label>
							<input type="text" name="billNo" value="${(saasPurchaseBillInfoPo.billNo)!}" class="form-control" readonly>
						</div>
					</div>
					<div class="col-sm-3 marginTop5">
						<div class="input-group">
							<label class="input-group-addon"><span class="danger">*</span>采购员</label>
							<input type="text" class="form-control" readonly name="billingUser" value="${(saasPurchaseBillInfoPo.billingUser)!}" required="required" readonly>
							<i class="glyphicon glyphicon-search"></i>
						</div>
					</div>
					<div class="col-sm-3 marginTop5">
						<div class="input-group">
							<label class="input-group-addon"><span class="danger">*</span>开票日期</label>
							<input id="d4311" type="text"  name="billTime" value="${(saasPurchaseBillInfoPo.billTime)?string("yyyy-MM-dd")}" class="form-control Wdate" required="required" readonly>
						</div>
					</div>
					 <div class="col-xs-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon"><span class="danger">*</span>预计收货</label>
                        	<input id="receivedTime" type="datetime"  name="receivedTime" value="${(saasPurchaseBillInfoPo.receivedTime?string("yyyy-MM-dd"))!}" required="required" class="form-control Wdate" readonly>
                        </div>
                    </div>
					<div class="col-sm-6 marginTop5">
						<div class="input-group">
							<label class="input-group-addon">备注</label>
							<input type="text" class="form-control" name="remarks" value="${(saasPurchaseBillInfoPo.remarks)!}" readonly>
						</div>
					</div>
					<#--  <div class="col-sm-3 marginTop5">
						<div class="input-group">
							<label class="input-group-addon"><span class="danger">*</span>供应商编号</label>
							<input type="text" class="form-control" name="supplierNo" value="${(saasPurchaseBillInfoPo.pharmacyPref)!}" required="required" readonly>
							<i class="glyphicon glyphicon-search"></i>
						</div>
					</div>
					<div class="col-sm-3 marginTop5">
						<div class="input-group">
							<label class="input-group-addon"><span class="danger">*</span>供应商名称</label>
							<input type="text" class="form-control" name="supplierName" value="${(saasPurchaseBillInfoPo.supplierName)!}" required="required" readonly>
						</div>
					</div>
					</div>
					<div class="row">
					  <div class="col-sm-3 marginTop5">
						<div class="input-group">
							<label class="input-group-addon"><span class="danger">*</span>开票员</label>
							<input type="text" class="form-control" readonly name="billingUser" value="${(saasPurchaseBillInfoPo.billingUser)!}" required="required" readonly>
							<i class="glyphicon glyphicon-search"></i>
						</div>
					</div>
					<div class="col-sm-3 marginTop5">
						<div class="input-group">
							<label class="input-group-addon">总金额</label>
							<input type="text" class="form-control" readonly name="taxAmountSum" value="${(saasPurchaseBillInfoPo.taxAmountSum)!?c}" readonly>
						</div>
					</div>
					<div class="col-sm-6 marginTop5">
						<div class="input-group">
							<label class="input-group-addon">备注</label>
							<input type="text" class="form-control" name="remarks" value="${(saasPurchaseBillInfoPo.remarks)!}" readonly>
						</div>
					</div>  -->
                    <button class="hideSubmit" type="submit">提交</button>
                    </div>
				</form>
		</div>

		<div class="panel panel-default publicPanel b_table2" id="b_table">
            <table id="grid-table"></table>
		</div>
	</body>
    <script src="/static/js/purchase/purchasePlanDetail.js?v=${jsVersion}"></script>
</html>
