<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta charset="utf-8"/>
    <link rel="icon" href="/static/assets/images/login/favicon.ico" type="image/x-icon"/>
    <title></title>
    <meta name="description" content="overview &amp; stats"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>
    <link rel="stylesheet" href="/static/assets/css/index.css?v=${jsVersion}"/>
<#include "/common/top-common.ftl"/>
    <style>
        .dropdown {
            width: 100%;
        }

        .select-btn {
            text-align: left;
        }

        .select-single {
            text-align: center;
        }

        .btn-group {
            height: 36px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            position: relative;
        }

        .shownAllToggle {
            position: absolute;
            left: 0;
        }

        .btn {
            margin: 0 0 0 10px !important;
        }

        .togglg {
            display: none;
        }
        input[type=number] {
            -moz-appearance: textfield;
        }

        input[type=number]::-webkit-inner-spin-button,
        input[type=number]::-webkit-outer-spin-button {
            -webkit-appearance: none;
        }
    </style>
</head>
<body id="innerBody">
<div id="toolbox" class="toolbox newToolbox">
    <button id="downloadExcel" type="button" class="btn btn-redactbag"> 导出Excel
    </button>
</div>

<div id="searchbox" class="panel panel-default publicPanel newPanel">
    <form class="commonValidate row" id="searchForm">
    <#--开始时间-->
        <input type="hidden" id="beginTime" name="beginTime" value="${groupVoDto.beginTime!""}">
    <#--默认为分页-->
        <input type="hidden" id="isPage" name="isPage" value="1"/>
    <#--复核员-->
        <input type="hidden" id="billingUser" name="billingUser" value="${groupVoDto.billingUser!""}"/>
    <#--结束时间-->
        <input type="hidden" id="endTime" name="endTime" value="${groupVoDto.endTime!""}">
    <#--单号-->
        <input type="hidden" id="billNo" name="billNo" value="${groupVoDto.billNo!""}">
    <#--商品名称-->
        <input type="hidden" id="productName" name="productName" value="${groupVoDto.productName!""}">
    <#--供应商名称-->
        <input type="hidden" id="supplierName" name="supplierName" value="${groupVoDto.supplierName!""}">
    <#--生产厂家-->
        <input type="hidden" id="manufacturer" name="manufacturer" value="${groupVoDto.manufacturer!""}">
    <#--分类字段-->
        <input type="hidden" id="typeFields" name="typeFields" value="${groupVoDto.typeFields!""}">
    <#--汇总字段-->
        <input type="hidden" id="collectFields" name="collectFields" value="${groupVoDto.collectFields!""}">
    <#--中文名称-->
        <input type="hidden" id="exportCnName" name="exportCnName" value="${groupVoDto.exportCnName!""}">
    <#--英文名称-->
        <input type="hidden" id="exportEnName" name="exportEnName" value="${groupVoDto.exportEnName!""}">
    <#--字段类型-->
        <input type="hidden" id="exportInputType" name="exportInputType" value="${groupVoDto.exportInputType!""}">

        <div class="col-xs-4 marginTop5  pull-right" id="btnBoxer">
            <div class="input-group btn-group">
                <a href="javascript:0" class="shownAllToggle">展开全部</a>
                <button type="reset" class="btn btn-return" id="resetBtn">重置</button>
                <button type="button" class="btn btn-redactbag" id="btnSearch">查询</button>
            </div>
        </div>
    </form>
</div>
<div class="panel panel-default publicPanel newPanel" id="b_table">
    <table id="grid-table"></table>
    <div id="grid-pager" style="margin-top: 20px !important;"></div>
</div>
</body>
<script>
    $(function () {

        /*导出Excel*/
        $("#downloadExcel").click(function () {
            $("#searchForm").attr("action", "/purchase/subtotalExport");
            serializeForm()
            $("#searchForm").submit();
        });

        var colNames = [];
        var colModel = [];

        InitSearchparam();
        isNumberValidate();
        function InitSearchparam() {
            var exportCnName = $("#exportCnName").val();
            var exportEnName = $("#exportEnName").val();
            var exportInputType=$("#exportInputType").val();

            var columnArr = [];

            if (exportEnName && exportEnName.length) {
                var exportCnNameTmp = exportCnName.split(',');
                var exportEnNameTmp = exportEnName.split(',');
                var exportInputTypeTmp = exportInputType.split(',');
                exportEnNameTmp.forEach((item, index) => {
                    columnArr.push({
                    cnName: exportCnNameTmp[index],
                    enName: item,
                    inputType:exportInputTypeTmp[index]
                })
            });
            }

            var htmlStr = [];
            if (columnArr.length) {

                $('.shownAllToggle')[columnArr.length > 8 ? 'show' : 'hide']();

                columnArr.forEach((item, index) => {

                var columnSorts = ['productAmount','productTaxPriceSum','productDiscountTaxPriceSum','productRejectionAmount','totalAcount']
                if(columnSorts.indexOf(item.enName.replace("Gp",""))>-1){
                    colNames.push(item.cnName);
                    colModel.push({
                        name: item.enName.replace("Gp",""),
                        index: item.enName.replace("Gp",""),
                        sortable:true,
                        inputType: item.inputType
                    });
                }
                else {
                    if(item.enName=="beginTime"){
                        if($.inArray("单据日期", colNames)==-1){
                            colNames.push("单据日期");
                            colModel.push({
                                name: "billTime",
                                index: "billTime",
                                sortable:false,
                                inputType:'dater'
                            });
                        }
                    }
                    else if(item.enName=="productDate"){
                        if($.inArray("生产日期", colNames)==-1){
                            colNames.push("生产日期");
                            colModel.push({
                                name: "productDate",
                                index: "productDate",
                                sortable:false,
                                inputType:'dater'
                            });
                        }
                    }
                    else if(item.enName=="productExpiry"){
                        if($.inArray("有效期", colNames)==-1){
                            colNames.push("有效期");
                            colModel.push({
                                name: "productExpiryDate",
                                index: "productExpiryDate",
                                sortable:false,
                                inputType:'dater'
                            });
                        }
                    }else if(item.enName=="reviewConclusionGp"){
                        colNames.push(item.cnName);
                        colModel.push({
                            name: "reviewConclusion",
                            index: "reviewConclusion",
                            sortable:false,
                            inputType:item.inputType,
                            formatter: function (reviewConclusion) {
                                return reviewConclusion ? "合格" : "不合格";
                            }
                        });
                    }else {
                        colNames.push(item.cnName);
                        colModel.push({
                            name: item.enName.replace("Gp",""),
                            index: item.enName.replace("Gp",""),
                            sortable:false,
                            inputType:item.inputType
                        });
                    }
                }

                htmlStr.push('<div class="col-xs-4 marginTop5 ' + (index > 7 ? 'togglg' : '') + '">');
                htmlStr.push('<div class="input-group input-group-tail">');
                htmlStr.push('<label class="input-group-addon" style="min-width:140px!important;">' + item.cnName + '</label>');
                if (item.inputType === "dater") {
                    htmlStr.push('<div>');
                    htmlStr.push('<input type="text" class="Wdate form-control" name="' + (item.enName) + 'Start" id="' + (item.enName) + 'Start" onfocus="WdatePicker({dateFmt:\'yyyy-MM-dd\',maxDate:\'#F{$dp.$D(' + (item.enName) + 'End)}\'})" resetable="true" />');
                    htmlStr.push('</div>');
                    htmlStr.push('<span class="transit">-</span>');
                    htmlStr.push('<div>');
                    htmlStr.push('<input type="text" class="Wdate form-control" name="' + (item.enName) + 'End" id="' + (item.enName) + 'End" onfocus="WdatePicker({dateFmt:\'yyyy-MM-dd\',minDate:\'#F{$dp.$D(' + (item.enName) + 'Start)}\'})" resetable="true" />');
                    htmlStr.push('</div>');
                }else {
                    htmlStr.push('<div style="flex: 2">');
                    htmlStr.push('<div class="dropdown">');
                    htmlStr.push('<span class="caret"></span>');
                    htmlStr.push('<input type="hidden" value="" name="' + (item.enName) + 'El">');
                    htmlStr.push('<input type="button" class="dropdown-toggle select-btn" data-toggle="dropdown" value="" data-val="" aria-haspopup="true" aria-expanded="false">');
                    htmlStr.push('<ul class="dropdown-menu select-single">');
                    if (item.inputType === "text") {
                        if (item.enName !== 'reviewConclusionGp') {
                            htmlStr.push('<li><a href="1">模糊查询</a></li>');
                        }
                        htmlStr.push('<li><a href="0">精准查询</a></li>');
                    } else if (item.inputType === "number") {
                        htmlStr.push('<li><a href="2">=</a></li>');
                        htmlStr.push('<li><a href="3">≠</a></li>');
                        htmlStr.push('<li><a href="4">></a></li>');
                        htmlStr.push('<li><a href="5">>=</a></li>');
                        htmlStr.push('<li><a href="6"><</a></li>');
                        htmlStr.push('<li><a href="7"><=</a></li>');
                    }
                    htmlStr.push('</ul>');
                    htmlStr.push('</div>');
                    htmlStr.push('</div>');
                    htmlStr.push('<span class="transit">-</span>');
                    htmlStr.push('<div style="flex: 4">');
                    //摘要特殊处理
                    if (item.enName === 'reviewConclusionGp') {
                        htmlStr.push('<div class="dropdown" id="' + item.enName + '">');
                        htmlStr.push('<span class="caret"></span>');
                        htmlStr.push('<input type="hidden" value="" name="' + (item.enName) + '" resetable="true">');
                        htmlStr.push('<input type="button" class="dropdown-toggle select-btn" data-toggle="dropdown" data-val="" aria-haspopup="true" aria-expanded="false" resetable="true">');
                        htmlStr.push('<ul class="dropdown-menu select-single">');
                        htmlStr.push('<li><a href="">全部</a></li>');//示例勿删
                        htmlStr.push('<li><a href="1">合格</a></li>');//示例勿删
                        htmlStr.push('<li><a href="0">不合格</a></li>');//示例勿删
                        htmlStr.push('</ul>');
                        htmlStr.push('</div>');
                    }else {
                        switch (item.inputType) {
                            case 'number':
                                htmlStr.push('<input type="number" class="form-control" name="' + (item.enName) + '" resetable="true" >');
                                break;
                            case 'text':
                                htmlStr.push('<input type="text" class="form-control" name="' + (item.enName) + '" resetable="true" >');
                                break;
                            default:
                                htmlStr.push('<input type="text" class="form-control" name="' + (item.enName) + '" resetable="true" >');
                                break;
                        }
                    }
                    htmlStr.push('</div>');
                }
                htmlStr.push('</div>');
                htmlStr.push('</div>');
                htmlStr.push('</div>');
            });
                $(htmlStr.join('')).insertBefore($("#btnBoxer"));
                $('#b_table').css({
                    "margin-top": $("#toolbox").outerHeight(true) + $("#searchbox").outerHeight(true)
                });
                //设置初始值
                resetDropdownValue();

                initTable(serializeForm());
            }
        }
        //隐藏展示事件
        var toggleType = false;//false:超出隐藏,true:显示全部
        $(".shownAllToggle").on('click', function () {
            toggleType = !toggleType;
            $('.togglg')[toggleType ? 'show' : 'hide']();
            $(this).html(toggleType ? '收起全部' : '展开全部');
            $('#b_table').css({
                "margin-top": $("#toolbox").outerHeight(true) + $("#searchbox").outerHeight(true)
            })
        });

        // 下拉单选 菜单交互
        $(document).on('click', ".dropdown-menu.select-single>li>a", function (e) {
            e.preventDefault();
            var selector = $(this).parents(".dropdown-menu").prevAll('input.dropdown-toggle.select-btn')
            if ($(this).hasClass('active')) {
                $(this).removeClass('active');
            } else {
                $(this).addClass('active');
            }
            selector.val(e.target.innerText);
            selector.prev().val(e.target.attributes.href.value);
            selector.focus()
        });
        // 单选下拉回显
        $(document).on("click", ".dropdown-toggle.select-btn", function () {
            var key = $(this).prev().val();
            var ul = $(this).next('.dropdown-menu.select-single');
            ul.find('a[href="' + key + '"]').addClass('active').parent().siblings().children().removeClass('active')
        });

        //序列化查询表单参数
        function serializeForm() {
            // debugger;
            var formData = $("#searchForm").serializeArray();
            var requestDataObj = {};
            if (formData && formData.length) {
                formData.forEach(item => {
                    requestDataObj[item.name] = item.value
                })
            }
            console.log(requestDataObj);
            return requestDataObj;
        }

        //查询
        $("#btnSearch").on('click', function () {
            $("#grid-table").jqGrid('setGridParam', {
                postData: serializeForm(), page: 1
            }).trigger('reloadGrid');
        });
        //isNumberValidate();
        //重置
        $("#resetBtn").on("click",function () {
            $('[resetable="true"]').val('');
            resetDropdownValue();
            $("#btnSearch").trigger('click');
        });
        function initTable(postData) {
            $("#grid-table").jqGrid({
                url: "/purchase/purchaseReceiveSubtotal",
                autowidth: true,
                postData: postData,
                jsonReader: {
                    root: "result.list",
                    page: "result.pageNum",
                    total: "result.pages",
                    records: "result.total",
                    repeatitems: false,
                    id: "0"
                },
                sortable: true,
                datatype: "json", //数据来源，本地数据（local，json,jsonp,xml等）
                height: "auto",//高度，表格高度。可为数值、百分比或'auto'
                //mtype:"GET",//提交方式
                colNames: colNames,
                colModel: colModel,
                viewrecords: true,
                rowNum: 50,//每页显示记录数
                rowList: [10, 20, 30, 40, 50],//用于改变显示行数的下拉列表框的元素数组。
                pager: '#grid-pager',//分页、按钮所在的浏览导航栏
                altRows: true,//设置为交替行表格,默认为false
                //toppager: true,//是否在上面显示浏览导航栏
                multiselect: true,//是否多选
                rownumbers: true,
                pagerpos: 'right',
                recordpos: 'left',
                loadComplete: function () {
                    var table = $(this);
                    setTimeout(function () {
                        updatePagerIcons(table);
                    }, 0);
                }
            });
        }

        //设置Dropdown初始值
        function resetDropdownValue() {
            var $dropdown = $(".dropdown[id!='summary']");
            var hiddenInput = void 0;
            var selectInputBtn = void 0;
            var selectSingleUl = void 0;
            var firstLi = void 0;
            var firstLiA = void 0;

            $.each($dropdown, (index, item) => {
                hiddenInput = $(item).find('input[type=hidden]');//隐藏域
            selectInputBtn = $(item).find('input.select-btn');//button
            selectSingleUl = $(item).find('.select-single');//ul
            firstLi = selectSingleUl.find('li:first');//第一项
            firstLiA = firstLi.find('a');

            hiddenInput.val(firstLiA.attr('href'));
            selectInputBtn.val(firstLiA.html()).attr({"data-val": firstLiA.attr('href')});

            firstLi.siblings().find('a').removeClass('active');
            firstLiA.addClass('active');
        });
        }
        //校验 开始
        function validform() {
            return $(".commonValidate").validate({
                errorPlacement: function (error, element) {
                    if (element.data('toggle') == "tooltip") {
                        $(element).tooltip('destroy');
                    }
                    var placement = element.data('placement') ? element.data('placement') : 'bottom';
                    element.data({
                        title: '<span style="color:#d43f3a" class="glyphicon glyphicon-xclamation-sign"></span>' + error.html(),
                        html: true,
                        toggle: 'tooltip',
                        placement: placement,
                        container: 'body'
                    });
                    element.tooltip('show');
                    setTimeout(function () {
                        element.tooltip('destroy');
                    }, 1000)
                }
            })
        }
    });

    function isNumberValidate() {
        var positiveReg=new RegExp(/^[0-9]\d*$/);
        var decimalReg=new RegExp(/^(([0-9]+)\.([0-9]{1,2})$)|^(([0-9]+)$)/);
        //统计条目数
        $('body').on('blur','input[name="totalAcount"]', function (e) {
            if(!positiveReg.test(e.target.value)){
                e.target.value='';
            }
        });
        //商品数量
        $('body').on('blur','input[name="productAmountGp"]', function (e) {
            if(!decimalReg.test(e.target.value)){
                e.target.value='';
            }
        });
        //拒收数量
        $('body').on('blur','input[name="productRejectionAmountGp"]', function (e) {
            if(!decimalReg.test(e.target.value)){
                e.target.value='';
            }
        });
    }
</script>
</html>