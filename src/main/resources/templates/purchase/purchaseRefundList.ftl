<!DOCTYPE html>
<html lang="en">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>Dashboard - Ace Admin</title>

		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
		<style>
			#grid-table .showText{
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
			}
			#grid-table tbody td input[readonly="readonly"]{
				border: none;
				background: transparent !important;
			}
			.commonValidate{
				padding: 2px 0px;
			}
			.marginTop5{
				margin-top: 12px
			}
			#gbox_grid-table .btn{ margin-right: 0!important; }
			.commonValidate .col-xs-3:not(.search) {
				width: 20% !important;
			}
		</style>
	</head>
	<body id="innerBody">
		<div id="toolbox" class="toolbox">
			<a href="/purchase/reBill/toAddOrEditPurchaseRetr?type=07" class="btn btn-addbag btn-default btn-round"> 新增</a>
		</div>
		<div class="panel panel-default publicPanel newPanel shfh">
			<input type="hidden" id="beginTimeHidden" value="${beginTime}">
			<input type="hidden" id="endTimeHidden" value="${endTime}">
			<form class="commonValidate">
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">单据信息</label>
						<input type="text" lit="seach" name="billNo" id="billNo" class="form-control" placeholder="订单编号/退补价单号">
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">供应商</label>
						<input type="text" lit="seach" name= "supplierName" id="supplierName" class="form-control" placeholder="供应商名称/供应商编号">
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">开票员</label>
						<div class="dropdown">
							<span class="caret"></span>
							<input type="hidden" value="" id="billingUser" name="billingUser">
							<input type="button" class="dropdown-toggle select-btn" data-toggle="dropdown" value="全部" aria-haspopup="true" aria-expanded="false">
							<ul class="dropdown-menu select-single">
								<li><a href="">全部</a></li>
								<#if (employeeDtos)??>
									<#list employeeDtos as dtos>
										<li><a href="#{dtos.id}">${dtos.name}</a></li>
									</#list>
								</#if>
							</ul>
						</div>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">开始时间</label>
						<input type="text" lit="seach" name="beginTime" value="${beginTime}" id="beginTime" class="Wdate form-control"   onfocus="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}'})" style="height: inherit;"/>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">结束时间</label>
						<input type="text" lit="seach" name="endTime" value="${endTime}" id="endTime" class="Wdate form-control"  onfocus="WdatePicker({minDate:'#F{$dp.$D(\'beginTime\')}'})" style="height: inherit;"/>
					</div>
				</div>


                    <#--  <div class="col-xs-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon">开票员</label>
                            <input type="text" lit="seach" name="billingUser" id="billingUser" class="form-control">
                        </div>
                    </div>  -->

                    <div class="col-xs-12 marginTop5">
                        <div class="input-group">
                            <button id="queryBtn"type="button" class="btn btn-redactbag forbidRepeatSearch">  查询</button>
                            <button id="disQueryCondition" type="button" class="btn btn-return">   重置</button>
                            <button class="btn btn-redactbag" id="filtrkolumn">筛选列</button>
                        </div>
                    </div>
                </div>
			</form>

		</div>
		<div class="panel panel-default publicPanel" id="b_table">
            <table id="grid-table"></table>
            <div id="grid-pager" style="margin-top: 20px !important;"></div>
		</div>
	</body>
    <script src="/static/js/purchase/purchaseRefundList.js?v=${jsVersion}"></script>
</html>

