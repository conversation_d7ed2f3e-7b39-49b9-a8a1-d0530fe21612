<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>Dashboard - Ace Admin</title>

		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />

		<#include "/common/top-common.ftl"/>
		<style>
			.tipstutorials{
				position: absolute;
				line-height: 10px;
				font-size: 14px;
				z-index: 99;
				display: none;
				background: #ffffe6;
				bottom: 40px;
				padding: 10px 10px 0;
				border-radius: 5px;
				border: 1px solid #eeeeee;
				width:100%;
				left:0;
			}
			.tmArr{
				margin-left: 0;
			}
			.tmArr~p{
				margin-left: 4.6em;
			}
            .table-btn{
                height: 36px;
                width:25px;
                padding: 0 5px;
                display: block;
                float: left;
                background-size:auto 15px !important;
            }
			.showBlock{
				display: block;
			}
			.hiddenBlock{
				display: none;
			}
            .shangchuan{
                background:url('../../../static/assets/images/common/<EMAIL>') no-repeat center;
            }
            .shangchuan:hover{
                background:url('../../../static/assets/images/common/<EMAIL>') no-repeat center;
            }
            .xiazai{
                background:url('../../../static/assets/images/common/<EMAIL>') no-repeat center;
            }
            .xiazai:hover{
                background:url('../../../static/assets/images/common/<EMAIL>') no-repeat center;
            }
            .shanchu{
                background:url('../../../static/assets/images/common/<EMAIL>') no-repeat center;
            }
            .shanchu:hover{
                background:url('../../../static/assets/images/common/<EMAIL>') no-repeat center;
            }
		</style>
	</head>
	<body id="innerBody">
		<!-- 工具栏 -->
		<div id="dictionary_tools" class="toolbox">
		    <#if requestStatus??>
		        <button onclick="location.href='/purchase/toCheckRecordList'" class="btn btn-return">  返回</button>
		    <#else>
                <button onclick="location.href='/purchase/bill/toReceivedCheckList'" class="btn btn-return">  返回</button>
		    </#if>
			<#if saasPurchaseBillInfoPo?? && saasPurchaseBillInfoPo.status =='02' && saasPurchaseBillInfoPo.receivingState ==1>
				<button id="printPage" class="btn btn-return btn-print">  打印</button>
			</#if>
			<#--  <button id="updateStatus" disabled class="btn btn-redactbag">  提交</button>
			<button id="getPurchasePlan" disabled class="btn btn-redactbag">  提取采购订单</button>
			<button id="getECOrder" disabled class="btn btn-removebag">  提取商城订单</button>  -->
		</div>
		<input id="tableUpfile" style="display:none" type="file" name="upfile"/>
		<div class="panel panel-default publicPanel newPanel">
			<form class="commonValidate" action="save" method="post">
				<input type="hidden" id="billId" name="id" value="${(saasPurchaseBillInfoPo.id)!}">
				<input type="hidden" id="billNo" name="billNo" value="${(saasPurchaseBillInfoPo.billNo)!}">
				<input type="hidden" name="billType" value="02">
				<input type="hidden" id="businessId" name="businessId" value="${(saasPurchaseBillInfoPo.businessId)!}">
				<input type="hidden" name="orderMedicineType" value="${(orderMedicineType)!}" id="orderMedicineType">

				<#if (parentBillInfo.billNo)??>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">订单编号</label>
						<input type="text" name="billNo" value="${(parentBillInfo.billNo)!}" class="form-control" readonly>
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">采购员</label>
						<input type="text" name="billNo" value="${parentBillInfo.billingUser}" class="form-control" readonly>
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">采购日期</label>
						<input type="text" name="billNo" value="${parentBillInfo.billTime?string("yyyy-MM-dd")}" class="form-control" readonly>
                    </div>
                </div>
                <#--  <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"> </label>
                        <label class="input-group-addon"> </label>
                    </div>
                </div>  -->
				</#if>

				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">单据编号</label>
						<input type="text" name="billNo" value="${(saasPurchaseBillInfoPo.billNo)!}" class="form-control" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><span class="danger">*</span>供应商编号</label>
						<input type="text" class="form-control" name="supplierNo" value="${(saasPurchaseBillInfoPo.pharmacyPref)!}" required="required" readonly>
						<i class="glyphicon glyphicon-search"></i>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><span class="danger">*</span>供应商名称</label>
						<input type="text" class="form-control" name="supplierName" value="${(saasPurchaseBillInfoPo.supplierName)!}" required="required" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">销售员</label>
						<input type="hidden" value="${(saasPurchaseBillInfoPo.salesperson)!}" id="salesperson" name="salesperson">
						<input type="text" class="form-control" name="salespersonName" value="${(saasPurchaseBillInfoPo.salespersonName)!}" required="required" readonly>
					</div>
        		</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><span class="danger">*</span>开票日期</label>
						<#if (saasPurchaseBillInfoPo.billTime)?? >
							<input id="d4311" type="text" name="billTime" value="${saasPurchaseBillInfoPo.billTime?string("yyyy-MM-dd")}" class="form-control Wdate" required="required" readonly>
						<#else>
							<input id="d4311" type="text" name="billTime" class="form-control Wdate" required="required" readonly>
						</#if>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><span class="danger">*</span>复核员</label>
						<input type="text" class="form-control" readonly name="billingUser" value="${(saasPurchaseBillInfoPo.billingUser)!}" required="required" readonly>
						<i class="glyphicon glyphicon-search"></i>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">总金额</label>
						<input type="text" class="form-control" name="taxAmountSum" value="${(saasPurchaseBillInfoPo.taxAmountSum)!?c}" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">整单折扣(%)</label>
						<input type="text" class="form-control" readonly name="discount" value="${(saasPurchaseBillInfoPo.discount)!?string(',###.00')}" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">折后金额</label>
						<input type="text" class="form-control" name="priceAfterDiscount" value="${(saasPurchaseBillInfoPo.priceAfterDiscount)!?c}" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">折扣金额</label>
						<input type="text" class="form-control" name="priceDiscounted" value="${(saasPurchaseBillInfoPo.priceDiscounted)!?c}" readonly>
					</div>
				</div>

				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">承运单位</label>
						<input type="text" class="form-control" name="carrierUnit" value="${(saasPurchaseBillInfoPo.carrierUnit)!}" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">承运单位信用代码</label>
								<input type="text" class="form-control" id="carrierUscc" maxlength="20"  name="carrierUscc" value="${(saasPurchaseBillInfoPo.carrierUscc)!}">
							</div>
						</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">启运地址</label>
						<input type="text" class="form-control" readonly name="shipment" value="${(saasPurchaseBillInfoPo.shipment)!}" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">启运时间</label>
						<#if (saasPurchaseBillInfoPo.departureTime)?? >
							<input type="text" class="form-control" name="departureTime" value="${saasPurchaseBillInfoPo.departureTime?string("yyyy-MM-dd")}" class="form-control Wdate" readonly>
						<#else>
							<input type="text" class="form-control" name="departureTime" class="form-control Wdate" readonly>
						</#if>

					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">承运方式</label>
						<input type="text" class="form-control" name="transportMode" value="${(saasPurchaseBillInfoPo.transportMode)!}" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">运输工具</label>
						<input type="text" class="form-control" name="vehicle" value="${(saasPurchaseBillInfoPo.vehicle)!}" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">备注</label>
						<input type="text" class="form-control" name="remarks" value="${(saasPurchaseBillInfoPo.remarks)!}" readonly>
					</div>
				</div>
				
				<#--  <div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">计划单开票日期</label>
						<#if (saasPurchaseBillInfoPo.planBillTime)?? >
							<input type="text" id="planBillTime" name="planBillTimeStr" value="${saasPurchaseBillInfoPo.planBillTime?string("yyyy-MM-dd")}"  class="form-control Wdate"  readonly>
						<#else>
							<input type="text" id="planBillTime" name="planBillTimeStr"  class="form-control Wdate"  readonly>
						</#if>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">订单开票日期</label>
						<#if (saasPurchaseBillInfoPo.orderBillTime)?? >
							<input type="text" id="orderBillTime" name="orderBillTimeStr" value="${saasPurchaseBillInfoPo.orderBillTime?string("yyyy-MM-dd")}"  class="form-control Wdate" autocomplete="off" readonly>
						<#else>
							<input type="text" id="orderBillTime" name="orderBillTimeStr" class="form-control Wdate" autocomplete="off" readonly>
						</#if>
					</div>
				</div>  -->



				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><#if (requiredFields!'')?index_of("invoiceTime")!=-1><span class="danger">*</span></#if>发票日期</label>
						<input type="text" class="form-control Wdate"  id="invoiceTime" maxlength="32" name="invoiceTime" <#if (saasPurchaseBillInfoPo.invoiceTime??)> value="${saasPurchaseBillInfoPo.invoiceTime?string("yyyy-MM-dd")}" </#if> required="required" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><#if (requiredFields!'')?index_of("invoiceCode")!=-1><span class="danger">*</span></#if>发票代码</label>
						<input type="text" class="form-control"  id="invoiceCode" maxlength="32" name="invoiceCode" value="${(saasPurchaseBillInfoPo.invoiceCode)!}" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><#if (requiredFields!'')?index_of("invoicePrice")!=-1><span class="danger">*</span></#if>发票金额</label>
						<input type="text" class="form-control"  id="invoiceMoney" maxlength="11" name="invoicePrice" <#if saasPurchaseBillInfoPo.invoicePrice??>value="${(saasPurchaseBillInfoPo.invoicePrice)!?c}"</#if>  onkeyup="value=replaceNumHander(value)">
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><#if (requiredFields!'')?index_of("invoiceNo")!=-1><span class="danger">*</span></#if>发票号</label>
						<input type="text" class="form-control"  id="invoiceNo" maxlength="32" name="invoiceNo" value="${(saasPurchaseBillInfoPo.invoiceNo)!}" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><#if (requiredFields!'')?index_of("receivingNo")!=-1><span class="danger">*</span></#if>收货运单号</label>
						<input type="text" class="form-control"  id="receivingNo" maxlength="32" name="receivingNo" value="${(saasPurchaseBillInfoPo.receivingNo)!}" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><#if (requiredFields!'')?index_of("withCargoTime")!=-1><span class="danger">*</span></#if>随货同行单日期</label>
						<input type="text" id="deliveryDate" name="withCargoTime"  <#if (saasPurchaseBillInfoPo ?? && saasPurchaseBillInfoPo.withCargoTime?? )> value="${saasPurchaseBillInfoPo.withCargoTime?string("yyyy-MM-dd")}" </#if> onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'})" class="form-control Wdate" autocomplete="off">
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">到货日期</label>
						<input type="text" id="arrivalTime" name="arrivalTime" <#if (saasPurchaseBillInfoPo ?? && saasPurchaseBillInfoPo.arrivalTime??)> value="${saasPurchaseBillInfoPo.arrivalTime?string("yyyy-MM-dd")}" </#if> class="form-control" autocomplete="off" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">
						<#if (saasPurchaseBillInfoPo.coldStorage)?? && saasPurchaseBillInfoPo.coldStorage == 1>
							<span class="danger">*</span>
						</#if>
						到货温度</label>
						<input type="text" class="form-control" name="arrivalTemperature" <#if saasPurchaseBillInfoPo.arrivalTemperature??>value="${saasPurchaseBillInfoPo.arrivalTemperature!?c}"</#if> readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
					<label class="input-group-addon">
						<#if (saasPurchaseBillInfoPo.coldStorage)?? && saasPurchaseBillInfoPo.coldStorage == 1>
							<span class="danger">*</span>
						</#if>
						运输温湿度记录</label>
						<input type="text" class="form-control" id="temperature" name="temperature" <#if saasPurchaseBillInfoPo.startT??>value="${saasPurchaseBillInfoPo.startT!?c}"</#if> readonly>
								<#--  气泡弹窗  -->
						<#if (saasPurchaseBillInfoPo.transportT) ??>
							<div class="tipstutorials">
								<p>启运温度: ${saasPurchaseBillInfoPo.startT!?c}℃</p>
								<p>到货湿度: ${saasPurchaseBillInfoPo.endHumidity!?c}%RH</p>
									<#assign json=saasPurchaseBillInfoPo.transportT?eval/>
									<#list json as item>
										<#if item_index = 0>
											<p class="tmArr">运输温度: ${item.d!} ${item.t!}℃</p>
										<#else>
											<p>${item.d!} ${item.t!}℃</p>
										</#if>
									</#list>
							</div>
						</#if>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">是否冷藏</label>
						<#if (saasPurchaseBillInfoPo.coldStorage)?? && saasPurchaseBillInfoPo.coldStorage == 1>
							<input id="isRefrigeration" name="isRefrigeration" type="checkbox" class="form-control" style="width: 15px;height: 15px !important;margin-top: 10px;" disabled checked="checked">
						<#else>
							<input id="isRefrigeration" name="isRefrigeration" type="checkbox" class="form-control" style="width: 15px;height: 15px !important;margin-top: 10px;" disabled>
						</#if>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><#if (requiredFields!'')?index_of("saleUser")!=-1><span class="danger">*</span></#if>批发企业业务员编号</label>
						<input type="text" class="form-control" id="saleUser"  maxlength="32" name="saleUser" value="${(saasPurchaseBillInfoPo.saleUser)!}">
					</div>
				</div>

				 <#if saasPurchaseBillInfoPo.invoiceFileUrl?? && saasPurchaseBillInfoPo.invoiceFileUrl!="">
					<div class="col-xs-3 marginTop5"  >
						<div class="input-group">
							<label class="input-group-addon"><#if (requiredFields!'')?index_of("invoiceFileUrl")!=-1><span class="danger">*</span></#if>发票</label>
							<a name='${saasPurchaseBillInfoPo.invoiceFileUrl}' class='table-btn xiazai downloadImg'></a>
						</div>
					</div>
				 </#if>
				 <#if saasPurchaseBillInfoPo.withCargoFileUrl?? && saasPurchaseBillInfoPo.withCargoFileUrl!="">
					<div class="col-xs-3 marginTop5"  >
						<div class="input-group">
							<label class="input-group-addon"><#if (requiredFields!'')?index_of("withCargoFileUrl")!=-1><span class="danger">*</span></#if>随货同行单</label>
							<a name='${saasPurchaseBillInfoPo.withCargoFileUrl}' class='table-btn xiazai downloadImg'></a>
						</div>
					</div>
				 </#if>
				
				<div class="col-xs-3 marginTop5"  >
					<div class="input-group billfile-group">
					    <input type="hidden" id="billId" value='${saasPurchaseBillInfoPo.id}' ></input>
					    <input type="hidden" id="billId2" value='${areaCodeRequiredFields!''}' ></input>
						<label class="input-group-addon"><#if (areaCodeRequiredFields!'')?index_of("billFile")!=-1><span class="danger">*</span></#if>质检报告单</label>
						 <a name='${saasPurchaseBillInfoPo.billFile}' class='table-btn xiazai downloadImg' style="display:none"></a>
						 <a href='javascript:;' id="removeBillFileImg" class='table-btn shanchu' style="display:none"></a>
						 <a href='javascript:;' id="upLoaderBillFileImg" class='table-btn shangchuan' style="display:none"></a>
<#--  
						<#if (saasPurchaseBillInfoPo.billFile)?? && saasPurchaseBillInfoPo.billFile!="">
						    <a name='${saasPurchaseBillInfoPo.billFile}' class='table-btn xiazai downloadImg'></a>
							<a href='javascript:;' id="removeBillFileImg" class='table-btn shanchu' onclick="delFileAjax()"></a>
						<#else>
							<a href='javascript:;' id="upLoaderBillFileImg" class='table-btn shangchuan' onclick="UpladFile('upLoaderBillFileImg')"></a>
						</#if>  -->
						<input type="hidden" id="billFile" <#if (areaCodeRequiredFields!'')?index_of("billFile")!=-1>required</#if> name="billFile" class="form-control" value="${(saasPurchaseBillInfoPo.billFile)!}" />
                            
					</div>
				</div>
				

				<div class="col-xs-3 marginTop5">
					<div class="col-xs-9 qualityReportBox">
						<div class="input-group">
							<label class="input-group-addon">质检报告</label>
							<input type="text" id="drugReportName" class="form-control" readonly />
						</div>
					</div>
					<div class="col-xs-3 qualityReportBox" style="padding-right: 0;">
						<button id="downloadBtn" class="btn btn-redactbag" type="button" style="margin-top: 3px;float: right;margin-right: 0;">下载</button>
					</div>
				</div>
				
				<button class="hideSubmit" type="submit">提交</button>
			</form>
		</div>


        <div class="panel panel-default publicPanel b_table2" id="b_table">
            <table id="grid-table"></table>
		</div>
	</body>
	<#--  <script src="../../static/assets/js/jszip/jszip.js"></script>
	<script src="../../static/assets/js/jszip/jszip-utils.js"></script>
	<script src="../../static/assets/js/jszip/FileSaver.js"></script>  -->
    <script src="/static/js/purchase/purchaseReceivedCheckDetail.js?v=${jsVersion}"></script>
</html>
