<!DOCTYPE html>
<html lang="en">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>验收记录</title>

		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
		<style>
		    #iframe{width:100%;border:0px;height:calc(100vh - 100px)}
		</style>
	</head>
	<body id="innerBody">
	    <div id="toolbox" class="toolbox newToolbox">
            <ul class="ui-tabs__nav" id="tabBtn">
                <li class="active" data-id="lossList">订单汇总</li>
                <li data-id="lossGood" class="">商品汇总</li>
            </ul>
        </div>
		<#--  <div id="toolbox" class="toolbox">
			<a href="/purchase/bill/toAddOrEditPurchase?type=03" class="btn btn-addbag btn-default btn-round"> 新增</a>
		</div>  -->
		<div class="ui-tab__content">
            <div class="panel panel-default publicPanel newPanel">
                <input type="hidden" id="beginTimeHidden" value="">
                <input type="hidden" id="endTimeHidden" value="">
                <form class="commonValidate">
                    <div class="col-xs-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon">单据信息</label>
                            <input type="text" lit="seach" placeholder="验收单号/供应商名称/供应商编号" name="billNo" id="billNo" class="form-control">
                        </div>
                    </div>
                    <div class="col-xs-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon">验收人</label>
<#--                            <div class="dropdown">-->
<#--                                <span class="caret"></span>-->
<#--                                <input type="hidden" value="" id="billingUser">-->
<#--                                <input type="button" class="dropdown-toggle select-btn" data-toggle="dropdown" value="全部" aria-haspopup="true" aria-expanded="false">-->
<#--                                <ul class="dropdown-menu select-single">-->
<#--                                    <li><a href="">全部</a></li>-->
<#--                                    <#if (employeeDtos)??>-->
<#--                                        <#list employeeDtos as dtos>-->
<#--                                        <li><a href="#{dtos.id}">${dtos.name}</a></li>-->
<#--                                        </#list>-->
<#--                                    </#if>-->
<#--                                </ul>-->
<#--                            </div>-->
                            <input type="text" lit="seach" placeholder="" name="billingUser" id="billingUser" class="form-control">
                        </div>
                    </div>

                   <#-- <div class="col-xs-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon">处理状态</label>
                            <div class="dropdown">
                                <span class="caret"></span>
                                <input type="hidden" value="" id="status">
                                <input type="button" class="dropdown-toggle select-btn" data-toggle="dropdown" value="全部" aria-haspopup="true" aria-expanded="false">
                                <ul class="dropdown-menu select-single">
                                    <li><a href="">全部</a></li>
                                    <li><a href="03">待处理</a></li>
                                    <li><a href="01">暂存</a></li>
                                    <li><a href="02">已完成</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>-->

                    <div class="col-xs-2 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon">开始时间</label>
                            <input type="text" lit="seach" name="beginTime" value="" id="beginTime" class="Wdate form-control"   onfocus="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}'})" style="height: inherit;"/>
                        </div>
                    </div>
                    <div class="col-xs-2 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon">结束时间</label>
                            <input type="text" lit="seach" name="endTime" value="" id="endTime" class="Wdate form-control"  onfocus="WdatePicker({minDate:'#F{$dp.$D(\'beginTime\')}'})" style="height: inherit;"/>
                        </div>
                    </div>
                    <div class="col-xs-2 marginTop5">
                        <div class="input-group" style="text-align: right;margin-top: 3px;">
                        <button id="queryBtn" type="button" class="btn btn-redactbag forbidRepeatSearch">  查询</button>
                        <button id="disQueryCondition" type="button" class="btn btn-return forbidRepeatSearch">   重置</button>
                        </div>
                    </div>
                    <div class="col-xs-12 marginTop5">
                        <div class="input-group" style="text-align: left">
                            <button class="btn btn-redactbag" id="filtrkolumn">筛选列</button>
                            <button id="oneStepQuery" type="button" class="btn btn-redactbag">一键查询</button>
                            <button id="exportExcel" type="button" class="btn btn-redactbag">导出Excel</button>
                            <button id="reportPrint" type="button" class="btn btn-redactbag" >打印</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="panel panel-default publicPanel" id="b_table">
                <table id="grid-table"></table>
                <div id="grid-pager" style="margin-top: 20px !important;"></div>
            </div>
        </div>
        <div class="ui-tab__content" hidden>
            <iframe frameborder="0" scrolling="yes" src="" id="iframe"></iframe>
        </div>
	</body>
	<script src="/static/assets/js/moment.min.js"></script>
    <script src="/static/js/purchase/purchaseStorageRecordList.js?v=${jsVersion}"></script>

</html>

