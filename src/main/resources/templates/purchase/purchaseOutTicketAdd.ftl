<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="utf-8" />
    <title>Dashboard - Ace Admin</title>

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <#include "/common/top-common.ftl"/>
    <style>
        select  {
            appearance: none;/*清除select下拉框默认样式*/
            -moz-appearance: none;
            -webkit-appearance: none;
            padding-left: 10px!important;
        }

        #grid-table .showText{
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
        #grid-table tbody td input[readonly="readonly"]{
            border: none;
            background: transparent !important;
        }
        #grid-table tbody td input[name='productBatchNo']{
            border: 1px solid #CECECE;
            background: #ffffff !important;
        }
        #jqgh_grid-table_amount::before,#jqgh_grid-table_productTaxPrice::before{
            content: "*";
            color: red;
        }
        .taxPrice .ui-dialog-content{
            height: 180px !important;
            overflow-y: auto;
        }
        #sellerDropdown{
            height:36px;
        }
        #traceCode {
            display: none;
        }
    </style>
</head>
<body id="innerBody">
<div id="toolbox" class="toolbox">
    <button onclick="location.href='/purchase/reBill/toOutTicketList'" class="btn btn-return">  返回</button>
    <button id="formSave" class="btn btn-addbag forbidRepeatSearch">  暂存</button>
    <button id="formSubmit" class="btn btn-redactbag forbidRepeatSearch">  提交</button>
    <button id="getBill" class="btn btn-redactbag">  提取采购入库单</button>
    <button id="getProductAmount" class="btn btn-redactbag" style="display: none;">原单退回</button>
</div>
<div class="panel panel-default publicPanel newPanel shfh">
    <form  id="form" class="commonValidate">
        <input type="hidden" name="id" value="${(saasPurchaseRetrieveBillInfoVO.id)!}" id="billId">
        <input type="hidden" name="guid" value="${(saasPurchaseRetrieveBillInfoVO.guid)!}" id="guid">
        <input type="hidden" name="billType" value="05">
        <input type="hidden" name="supplierNo" id="supplierNo" value="${(saasPurchaseRetrieveBillInfoVO.supplierNo)!}">
        <input type="hidden" id="billingUserLoginName" name="billingUserLoginName" value="${loginName!}">
        <#if (saasPurchaseRetrieveBillInfoVO.billingUser)??>
            <input type="hidden" id="billingId" name="billingId" value="${(saasPurchaseRetrieveBillInfoVO.billingUserId)!}">
        <#else>
            <input type="hidden" id="billingId" name="billingId" value="${currentUserId}">
        </#if>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">单据编号</label>
                <input type="text" name="billNo" id="billNo" value="${(saasPurchaseRetrieveBillInfoVO.billNo)!}" class="form-control" readonly>
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon"><span class="danger">*</span>开票日期</label>
                <#if (saasPurchaseRetrieveBillInfoVO.billTime)??>
                    <input type="text" name="billTime" id="billTime" value="${(saasPurchaseRetrieveBillInfoVO.billTime)?string("yyyy-MM-dd")}" required="required" class="Wdate form-control" <#if systemConfigYn.defaultPersonDateModifiable?? && systemConfigYn.defaultPersonDateModifiable == 1>onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'})"<#else>readonly style="background: #f5f5f5!important" </#if> style="height: inherit;" />
                <#elseif (currentTime)??>
                    <input type="text" name="billTime" id="billTime" value="${currentTime?string("yyyy-MM-dd")}" required="required" class="form-control Wdate" <#if systemConfigYn.defaultPersonDateModifiable?? && systemConfigYn.defaultPersonDateModifiable == 1>onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'})"<#else>readonly style="background: #f5f5f5!important" </#if> >
                <#else>
                    <input type="text" name= "billTime" id="billTime" required="required" class="Wdate form-control" <#if systemConfigYn.defaultPersonDateModifiable?? && systemConfigYn.defaultPersonDateModifiable == 1>onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'})"<#else>readonly style="background: #f5f5f5!important" </#if> style="height: inherit;" />
                </#if>
            </div>
        </div>

        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon"><span class="danger">*</span>供应商编号</label>
                <input type="text" id="supplierNoText" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.pharmacyPref)!}" required="required">
                <i class="glyphicon glyphicon-search"></i>
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon"><span class="danger">*</span>供应商名称</label>
                <input type="text" name= "supplierName" id="supplierName" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.supplierName)!}"  required="required" readonly>
            </div>
        </div>
        <#--  <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon"><span class="danger">*</span>开票员</label>
						<#if (saasPurchaseRetrieveBillInfoVO.billingUser)??>
							<input type="text" name= "billingUser" id="billingUser" class="form-control billingUser" value="${(saasPurchaseRetrieveBillInfoVO.billingUser)!}" required="required" readonly >
						<#else>
							<input type="text" class="form-control billingUser" id="billingUser" name="billingUser" value="${currentUser}" required="required" readonly>
						</#if>
                <i class="glyphicon glyphicon-search"></i>
            </div>
        </div>  -->
        <div class="col-xs-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">销售员</label>
                <div class="dropdown" id="sellerDropdown">
                    <span class="caret" id="slect-icon"></span>
                    <#if (saasPurchaseRetrieveBillInfoVO.salesperson)??>
                        <input type="hidden" value="${(saasPurchaseRetrieveBillInfoVO.salesperson)!}" id="salesperson" name="salesperson">
                    <#else>
                        <input type="hidden" value="" id="salesperson" name="salesperson">
                    </#if>
                    <#if (saasPurchaseRetrieveBillInfoVO.salespersonName)??>
                        <input type="button" class="dropdown-toggle select-btn" id="sales-text" data-toggle="dropdown" value="${(saasPurchaseRetrieveBillInfoVO.salespersonName)!}" aria-haspopup="true" aria-expanded="false">
                    <#else>
                        <input type="button" class="dropdown-toggle select-btn" id="sales-text" data-toggle="dropdown" value="" aria-haspopup="true" aria-expanded="false">
                    </#if>
                </div>
                <input type="hidden" name= "sellerInput" id="sellerInput" class="form-control" value="" disabled="true">
            </div>
        </div>

        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">开票员</label>
                <div class="dropdown">
                    <span class="caret"></span>
                    <input type="hidden" value="" id="billingUser" name="billingUser">
                    <input type="button" class="dropdown-toggle select-btn" <#if systemConfigYn.defaultPersonDateModifiable?? && systemConfigYn.defaultPersonDateModifiable == 1>data-toggle="dropdown"<#else>readonly</#if> value="" aria-haspopup="true" aria-expanded="false">
                    <ul class="dropdown-menu select-single">
                        <#if (employeeDtos)??>
                            <#list employeeDtos as dtos>
                                <li><a href="#{dtos.id}">${dtos.name}</a></li>
                            </#list>
                        </#if>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">上级单号</label>
                <input type="text" name= "parentBillNo" id="parentBillNo" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.parentBillNo)!}" readonly>
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">总金额</label>
                <input type="text" name= "taxAmountSum" id="taxAmountSum" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.taxAmountSum)!?c}" readonly>
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">运输方式</label>
                <input type="text" name= "transportationMode" id="transportationMode" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.transportationMode)!}">
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">运输单号</label>
                <input type="text" name= "transportationCode" id="transportationCode" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.transportationCode)!}">
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">备注</label>
                <input type="text" name= "remarks" id="remarks" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.remarks)!}">
            </div>
        </div>
    </form>
</div>
<div class="panel panel-default publicPanel" id="b_table">
    <div class="row">
        <div class="input-group box_zll">
            <button class="btn btn-addbag" id="addRow">新增行</button>
            <button class="btn btn-removebag" id="deleteRow">删除行</button>
            <#-- <button class="btn btn-redactbag" id="filtrkolumn" style="float:right;">设置显示列</button>-->
            <button class="btn btn-redactbag" id="traceCode" type="button">扫追溯码</button>
            <button class="btn btn-redactbag" id="udiCode" type="button">扫UDI码</button>
        </div>
    </div>
    <table id="grid-table"></table>
    <#--<div id="grid-pager" style="margin-top: 20px !important;"></div>-->
</div>
</body>
<script src="/static/assets/js/GridSub.js"></script>
<script src="/static/js/purchase/purchaseOutTicketAdd.js?v=${jsVersion}"></script>

</html>
