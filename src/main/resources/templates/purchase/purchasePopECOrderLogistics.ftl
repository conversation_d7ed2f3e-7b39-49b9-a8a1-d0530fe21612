<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="utf-8" />
    <title>Dashboard - Ace Admin</title>
    <meta name="google" content="notranslate" />
    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="stylesheet" href="/static/assets/css/element-ui.min.css" />
    <#include "/common/top-common.ftl"/>
    <style>
       .empty {
            margin-top: 100px;
            text-align: center;
            font-size: 24px;
       }
       .el-timeline {
            padding-left: 10px;
            height: calc(100vh - 100px);
            overflow: auto;
        }
        .way-bill {
            padding-left: 10px;
            margin-bottom: 10px;
            color: #909399;
        }
    </style>
</head>
<body id="innerBody">
    <div id="vue-ele">
        <template>
            <el-tabs v-if="tabList.length" v-model="activeName" type="card" @tab-click="handleClick">
                <el-tab-pane v-for="(item, index) in tabList"  :label="item.label" :name="item.name">
                    <div class="way-bill">
                        <span style="margin-right: 20px;">运单号:{{item.waybillNo}}</span>
                        <span>状态:{{item.statusText}}</span>
                    </div>
                    <el-timeline>
                        <el-timeline-item
                        v-for="(activity, i) in activities[index]"
                        :key="activity.deliveryTime"
                        :color="activity.color"
                        :timestamp="activity.deliveryTime">
                        {{activity.description}}
                        </el-timeline-item>
                    </el-timeline>
                </el-tab-pane>
            </el-tabs>
            <div v-else class="empty">
                暂无物流包裹
            </div>
        </template>
    </div>
    
</div>
</body>
<script src="/static/assets/js/vue.min.js?v=${jsVersion}"></script>
<script src="/static/assets/js/element-ui.min.js?v=${jsVersion}"></script>
<script src="/static/js/purchase/purchasePopECOrderLogistics.js?v=${jsVersion}"></script>

</html>

