<!DOCTYPE html>
<html lang="en">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>Dashboard - Ace Admin</title>

		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
	</head>
	<body id="innerBody">
		<div id="toolbox" class="toolbox">
			<button onclick="location.href='/purchase/reBill/toRefundList'" class="btn btn-return">  返回</button>
			<button id="formSubmit" disabled class="btn btn-redactbag">  提交</button>
			<button id="getBill" disabled class="btn btn-redactbag">  提取采购入库单</button>
			<button id="printPage" class="btn btn-return btn-print">  打印</button>
			<#if (saasPurchaseRetrieveBillInfoVO.settlementStatus?? && saasPurchaseRetrieveBillInfoVO.settlementStatus!=2)>
        		<button id="settlement" class="btn btn-return">结算</button>
      		</#if>
		</div>
		<div class="panel panel-default publicPanel cgtbjd">
			<form id="formData" class="commonValidate" style="padding-bottom: 10px;">
				<input type="hidden" value="${(saasPurchaseRetrieveBillInfoVO.id)!}" id="billId">
				<input type="hidden" value="${(saasPurchaseRetrieveBillInfoVO.billNo)!}" id="billNo">
				<input type="hidden" name="guid" value="${(saasPurchaseRetrieveBillInfoVO.guid)!}" id="guid">
				<input type="hidden" id="settlementStatus" name="settlementStatus" value="${(saasPurchaseRetrieveBillInfoVO.settlementStatus)!}">
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">单据编号</label>
						<input type="text" name="saasPurchaseRetrieveBillInfoVO.billNo" id="billNo" value="${(saasPurchaseRetrieveBillInfoVO.billNo)!}" class="form-control" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><span class="danger">*</span>开票日期</label>
						<#if (saasPurchaseRetrieveBillInfoVO.billTime)??>
							<input type="text" name="saasPurchaseRetrieveBillInfoVO.billTime" id="billTime" value="${(saasPurchaseRetrieveBillInfoVO.billTime)?string("yyyy-MM-dd")}" required="required" class="Wdate form-control" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'})" style="height: inherit;" readonly/>
						<#else>
							<input type="text" name= "saasPurchaseRetrieveBillInfoVO.billTime" id="billTime" class="form-control" required="required" class="Wdate form-control" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd'})" style="height: inherit;" readonly/>
						</#if>
					</div>
				</div>
				
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><span class="danger">*</span>供应商编号</label>
						<input type="text" name= "saasPurchaseRetrieveBillInfoVO.pharmacyPref" id="supplierNo" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.pharmacyPref)!}" required="required" readonly>
                        <input type="hidden" name= "saasPurchaseRetrieveBillInfoVO.supplierNo" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.supplierNo)!}" required="required" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><span class="danger">*</span>供应商名称</label>
						<input type="text" name= "saasPurchaseRetrieveBillInfoVO.supplierName" id="supplierName" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.supplierName)!}"  required="required" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">销售员</label>
						<input type="text" name= "salesperson" id="salesperson" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.salespersonName)!}" readonly>
					</div>
      			</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><span class="danger">*</span>开票员</label>
						<input type="text" name= "saasPurchaseRetrieveBillInfoVO.billingUser" id="billingUser" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.billingUser)!}" required="required" readonly >
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><span class="danger">*</span>上级单号</label>
						<input type="text" name= "saasPurchaseRetrieveBillInfoVO.parentBillNo" id="parentBillNo" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.parentBillNo)!}" required="required" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">冲价总金额</label>
						<input type="text" name= "saasPurchaseRetrieveBillInfoVO.taxAmountSum" id="taxAmountSum" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.taxChargeAmount)!?c}" readonly>
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">备注</label>
						<input type="text" name= "saasPurchaseRetrieveBillInfoVO.remarks" id="remarks" class="form-control" value="${(saasPurchaseRetrieveBillInfoVO.remarks)!}" readonly>
					</div>
				</div>
			</form>
		</div>
		<div class="panel panel-default publicPanel" id="b_table" style="top: 10px;">
            <table id="grid-table"></table>
            <div id="grid-pager"></div>
		</div>
	</body>
    <script src="/static/js/purchase/purchaseRefundDetail.js?v=${jsVersion}"></script>

</html>
