<!DOCTYPE html>
<html lang="en">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>Dashboard - Ace Admin</title>

		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>

	</head>
	<body id="innerBody">
        <div class="panel panel-default publicPanel newPanel" style="display: none">
            <table id="summgrid-table"></table>
        </div>
        <div id="toolbox" class="row toolbox">
            <div class="col-sm-3 col-md-3 col-lg-3" style="padding: 0;display: flex">
                <button id="exportInventoryManagementExcelBtn" type="button" class="btn btn-redactbag">导出Excel</button>
                <button type="button" class="btn btn-redactbag" id="summary-filter">汇总统计</button>
            </div>
            <#if (organSignType)??>
            <div class="col-sm-9 col-md-9 col-lg-9" style="text-align: right;">
                
<#--                山西省-->
                <#if (city)?? && city == '忻州市' >
                    <button id="sumBtn" type="button"  class="btn btn-return" >  支付汇总统计</button>
                <#elseif organSignType == '6' >
                    <button id="sumBtn" type="button"  class="btn btn-return" >  支付汇总统计</button>
                <#else>
                </#if>

                <#if organSignType?? && organSignType =='3'>
                    <button id="exportMedicalInsuranceInventoryManagementExcelBtn" type="button" class="btn btn-return" >医保进销存</button>
                </#if>

                <#-- 江苏省南通市启东市, 江苏省南通市南通海门区, 山西省忻州市 -->
                <button id="exportExcelQiDong" type="button" class="btn btn-return" >医保数据</button>

                <#--江西医保-->
                <input type="hidden" id="organSignType" value="${organSignType!''}">
				<button id="exportMedicalExcel" type="button" class="btn btn-return" >医保数据导出</button>
                <span style="color: #ce2725; margin: 0 5px;">${remark!''}</span>
            </div>
            </#if>
        </div>
		<!-- 工具栏 -->
		<div class="panel panel-default publicPanel newPanel">
			<form class="row form-inline">
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">开始日期</label>
                        <input type="text" name="startTime" id="startTime" value="${startTime}" class="Wdate form-control"  data-toggle="tooltip" data-placement="bottom" title=""  onfocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',maxDate:'#F{$dp.$D(\'endTime\');}'})" style="height: inherit;" required data-msg-required='请输入正确的日期'/>
                    </div>
                </div>
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">结束日期</label>
                        <input type="text" name="endTime" id="endTime" value="${endTime}" class="Wdate form-control"  data-toggle="tooltip" data-placement="bottom" title=""  onfocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'#F{$dp.$D(\'startTime\');}'})" style="height: inherit;" required data-msg-required='请输入正确的日期'/>
                    </div>
                </div>
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">商品信息</label>
                        <input type="text" id="mnemonicCode" name="mnemonicCode" class="form-control">
                    </div>
                </div>
                <div class="col-sm-3 marginTop5" style="text-align: right;line-height: 30px;">
                    <div class="input-group">
                        <button id="queryBtn" type="button"  class="btn btn-redactbag forbidRepeatSearch" >  查询</button>
                        <button id="cancelBtn" type="button"  class="btn btn-return forbidRepeatSearch" >   重置</button>
                        <button  class="btn btn-redactbag"  id="filtrkolumn">筛选列</button>

                    </div>
                </div>
			</form>
		</div>
		<div class="panel panel-default publicPanel" id="b_table">
            <table id="grid-table"></table>
            <div id="grid-pager" style="margin-top: 20px !important;"></div>
		</div>
<#include "../common/tb-filter-statistic.ftl"/>
	</body>
    <script src="/static/js/report/purchaseSaleStorageList.js?v=${jsVersion}"></script>

</html>

