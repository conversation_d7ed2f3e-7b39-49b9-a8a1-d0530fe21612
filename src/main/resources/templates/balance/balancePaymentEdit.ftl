<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="utf-8" />
    <title>Dashboard - Ace Admin</title>

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />

		<#include "/common/top-common.ftl"/>
</head>
<body id="innerBody">
<!-- 工具栏 -->
<div id="dictionary_tools" class="toolbox newToolbox">
    <button onclick="javascript: window.location.href = '/balance/payment/toList'" class="btn btn-return">  返回</button>
    <button id="formSave" class="btn btn-addbag" ps="true">  保存</button>
    <button id="formSubmit" class="btn btn-redactbag" ps="true">  提交</button>
    <#--<#if payment.status == 1>-->
        <#--<button id="formSave" class="btn btn-white btn-default btn-round">  保存</button>-->
    <#--</#if>-->
    <#--<#if payment.status == 2>-->
        <#--<button id="formSubmit" class="btn btn-white btn-default btn-round">  提交</button>-->
    <#--</#if>-->

</div>
<div class="panel panel-default publicPanel shfh">
    <input type="hidden" id="id" value="${(payment.id)?c}">
        <form class="validate box_zll2" action="save" method="post">
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">单据编号</label>
                        <input type="text" id="pref" name="pref"  class="form-control" readonly value="${(payment.pref)!}">
                    </div>
                </div>
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>开票日期</label>
                    <#--<#if (saasPurchaseBillInfoPo.billTime)??>-->
                    <#--<input id="d4311" type="datetime"  name="createTime" value="${saasPurchaseBillInfoPo.billTime?string("yyyy-MM-dd HH:mm:ss")}" class="form-control Wdate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd HH:mm:ss'})">-->
                    <#--<#else>-->
                        <input type="text" class="form-control  Wdate"  style="height: inherit;" readonly name="beginTimeStr" id="beginTimeStr" value="${(payment.beginTimeStr)!}">
                    <#--</#if>-->
                    </div>
                </div>
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>开票员</label>
                        <input type="hidden" class="form-control" readonly name="createUser" id="createUser" name="createUser" value="${(payment.createUser)!}">
                        <input type="text" class="form-control" readonly name="createUserName" id="createUserName" name="createUserName" value="${(payment.createUserName)!}">
                    </div>
                </div>
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>供应商编号</label>
                        <input type="text" name="providerPref" class="form-control" data-title="供应商编号" id="providerPref" fs="true" value="${(payment.providerPref)!}" style="display:none">
                        <input type="text" name="pharmacyPref" class="form-control" data-title="供应商编号" id="pharmacyPref" fs="true" value="${(payment.pharmacyPref)!}">
                        <i class="glyphicon glyphicon-search" id="searchProvider"></i>
                    </div>
                </div>
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>供应商名称</label>
                        <input type="text" class="form-control" readonly name="providerName" id="providerName" value="${(payment.providerName)!}"/>
                    </div>
                </div>
            <#--<div class="col-sm-3 marginTop5">-->
            <#--<div class="input-group">-->
            <#--<label class="input-group-addon"><span class="danger">*</span>开票员</label>-->
            <#--<input type="text" class="form-control" readonly name="saasPurchaseBillInfoPo.billingUser" value="${(saasPurchaseBillInfoPo.billingUser)!}">-->
            <#--<i class="glyphicon glyphicon-search"></i>-->
            <#--</div>-->
            <#--</div>-->
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">联系人</label>
                        <input type="text" class="form-control" readonly name="providerLinkman" id="providerLinkman"  value="${(payment.providerLinkman)!}">
                    </div>
                </div>
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">联系地址</label>
                        <input type="text" class="form-control" readonly name="providerAddr" id="providerAddr" value="${(payment.providerAddr)!}">
                    </div>
                </div>
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">开户银行</label>
                        <input type="text" class="form-control" readonly name="providerBranch" id="providerBranch" value="${(payment.providerBranch)!}">
                    </div>
                </div>
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">银行账户</label>
                        <input type="text" class="form-control" readonly name="bankCardNum" id="bankCardNum" value="${(payment.bankCardNum)!}">
                    </div>
                </div>
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">应付余额</label>
                        <input type="text" class="form-control" readonly name="amountTotal" id="amountTotal" value="${(payment.amountTotal)?c}">
                    </div>
                </div>
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">总付款金额</label>
                        <input type="text" class="form-control" readonly name="amountPayment" id="amountPayment" value="${(payment.amountPayment)?c}">
                    </div>
                </div>
                <div class="col-sm-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">备注</label>
                        <input type="text" class="form-control" name="remark" id="remark" value="${(payment.remark)!}">
                    </div>
                </div>
        </form>
    <div class="row">
        <div class="col-sm-6 marginTop5">
            <div class="input-group" style="padding-left: 20px;">
                <button  class="btn btn-redactbag"  id="addRow">新增行</button>
                <button  class="btn btn-removebag"  id="deleteRow">删除行</button>
                <#--<button  class="btn btn-redactbag"  id="filtrkolumn">筛选列</button>-->
            </div>
        </div>

    </div>
</div>

<div class="panel panel-default publicPanel" id="b_table">
        <table id="grid-table"></table>
</div>
</body>
<#--<script>-->
    <#--alert(0);-->
    <#--var dataJson = ;-->
    <#--alert(dataJson[0].id);-->
<#--</script>-->
<script src="/static/js/balance/banlacePaymentEdit.js?v=${jsVersion}"></script>
</html>