<!DOCTYPE html>
<html lang="en">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>Dashboard - Ace Admin</title>

		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
	</head>
	<body id="innerBody">
        <!-- 工具栏 -->
        <div id="toolbox" class="toolbox newToolbox">
            <a href="toAdd" class="btn btn-addbag btn-default btn-round"> 新增</a>
            <button class="btn btn-redactbag btn-default btn-round btnDelete" id="editBtn">  编辑</button>
            <button class="btn btn-removebag btn-default btn-round btnDelete" id="deleteBtn">  删除</button>
        </div>
		<div class="panel panel-default publicPanel newPanel">
            <input hidden="hidden" id="hbegin" value="${(begin)}">
            <input hidden="hidden" id="hend" value="${(end)}">
			<form class="commonValidate">
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">供货商</label>
                        <input type="text" id="providerName" class="form-control">
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">单据编号</label>
                        <input type="text" name="pref" id="billNo" class="form-control">
                    </div>
                </div>
                    <div class="col-xs-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon">开始时间</label>
                            <input type="text" name="beginTimeStr" id="beginTimeStr" class="Wdate form-control" value="${(begin)}"   onfocus="WdatePicker({maxDate:'#F{$dp.$D(\'endTimeStr\')}'})" style="height: inherit;"/>
                        </div>
                    </div>
                    <div class="col-xs-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon">结束时间</label>
                            <input type="text" name="endTimeStr" id="endTimeStr" class="Wdate form-control" value="${(end)}"  onfocus="WdatePicker({minDate:'#F{$dp.$D(\'beginTimeStr\')}'})" style="height: inherit;"/>
                        </div>
                    </div>
                <div class="col-sm-12 marginTop5">
                    <div class="input-group">
                        <button id="queryBtn" type="button" class="btn btn-redactbag forbidRepeatSearch">  查询</button>
                        <button id="resetBtn" type="button" class="btn btn-return">   重置</button>
                        <button id="filtrkolumn" type="button" class="btn btn-redactbag">筛选列</button>
                    </div>
                </div>
			</form>

		</div>
		<div class="panel panel-default publicPanel" id="b_table">
                <table id="grid-table"></table>
                <div id="grid-pager"></div>
		</div>


	</body>
    <script src="/static/js/balance/balancePaymentList.js?v=${jsVersion}"></script>

</html>

