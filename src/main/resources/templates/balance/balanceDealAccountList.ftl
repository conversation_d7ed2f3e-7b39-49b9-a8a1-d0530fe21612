<!DOCTYPE html>
<html lang="en">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>Dashboard - Ace Admin</title>

		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
        <style>
        td[aria-describedby="grid-table_balance"] {
            padding-right: 15px !important;
        }
        </style>
	</head>
	<body id="innerBody">
		<!-- 工具栏 -->
		<#--<div id="toolbox" class="toolbox">-->
			<#--<a href="toAdd" class="btn btn-white btn-default btn-round"> 新增</a>-->
			<#--<a class="btn btn-white btn-default btn-round btnEdit">  编辑</a>-->
		<#--</div>-->
        <div id="toolbox" class="toolbox newToolbox">
            <button id="exportBtn" type="button" class="btn btn-redactbag"> 导出Excel</button>
        </div>
		<div class="panel panel-default publicPanel newPanel">
            <input hidden="hidden" id="hbegin" value="${(begin)}">
            <input hidden="hidden" id="hend" value="${(end)}">
			<form class="commonValidate">
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">开始时间</label>
                        <input type="text" name="beginTime" id="beginTime" class="Wdate form-control" value="${(begin)}"   onfocus="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}'})" style="height: inherit;"/>
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">结束时间</label>
                        <input type="text" name="endTime" id="endTime" class="Wdate form-control" value="${(end)}"  onfocus="WdatePicker({minDate:'#F{$dp.$D(\'beginTime\')}'})" style="height: inherit;"/>
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">供货商</label>
                        <input type="text" id="providerName" class="form-control">
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <button id="queryBtn" type="button" class="btn btn-redactbag forbidRepeatSearch">  查询</button>
                        <button id="resetBtn" type="button" class="btn btn-return">   重置</button>
                        <button id="filtrkolumn" type="button" class="btn btn-redactbag">筛选列</button>
                    </div>
                </div>
			</form>

		</div>
		<div class="panel panel-default publicPanel" id="b_table">
                <table id="grid-table"></table>
                <div id="grid-pager" style="margin-top: 20px !important;"></div>
		</div>


	</body>
    <script src="/static/js/balance/balanceDealAccountList.js?v=${jsVersion}"></script>

</html>

