<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="utf-8" />
    <title>Dashboard - Ace Admin</title>

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />

		<#include "/common/top-common.ftl"/>
</head>
<body id="innerBody">
<!-- 工具栏 -->
<div id="dictionary_tools" class="toolbox newToolbox">
    <button onclick="javascript: window.location.href = '/balance/payment/toList'" class="btn btn-return">  返回</button>
    <button id="formSave" class="btn btn-addbag" ps="true">  保存</button>
    <button id="formSubmit" class="btn btn-redactbag" ps="true">  提交</button>
</div>
<div class="panel panel-default publicPanel newPanel">
    <form class="validate commonValidate" action="save" method="post">
        <input type="hidden" id="id" name="id" datatype="n" >
        <#--<input type="hidden" id="billId" value="${(saasPurchaseBillInfoPo.id)!}">-->
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">单据编号</label>
                <input type="text" id="pref" name="pref"  class="form-control" readonly>
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon"><span class="danger">*</span>开票日期</label>
                <#--<#if (saasPurchaseBillInfoPo.billTime)??>-->
                <#--<input id="d4311" type="datetime"  name="createTime" value="${saasPurchaseBillInfoPo.billTime?string("yyyy-MM-dd HH:mm:ss")}" class="form-control Wdate" onfocus="WdatePicker({dateFmt: 'yyyy-MM-dd HH:mm:ss'})">-->
                <#--<#else>-->
                <input type="text" class="form-control" readonly name="beginTimeStr" id="beginTimeStr" value="${date}">
                <#--</#if>-->
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon"><span class="danger">*</span>开票员</label>
                <input type="text" class="form-control" readonly name="createUser" id="createUser" value="${userName}">
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon"><span class="danger">*</span>供应商编号</label>
                <#--<input type="text" class="form-control" name="providerPref" id="providerPref"  >-->
                <input type="text" name="providerPref" class="form-control" data-title="供应商编号" id="providerPref" fs="true" style="display:none">
                <input type="text" name="pharmacyPref" class="form-control" data-title="供应商编号" id="pharmacyPref" fs="true">
                <i class="glyphicon glyphicon-search" id="searchProvider"></i>
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group" >
                <label class="input-group-addon"><span class="danger">*</span>供应商名称</label>
                <input type="text" class="form-control" readonly name="providerName" id="providerName"/>
            </div>
        </div>
        <#--<div class="col-sm-3 marginTop5">-->
        <#--<div class="input-group">-->
        <#--<label class="input-group-addon"><span class="danger">*</span>开票员</label>-->
        <#--<input type="text" class="form-control" readonly name="saasPurchaseBillInfoPo.billingUser" value="${(saasPurchaseBillInfoPo.billingUser)!}">-->
        <#--<i class="glyphicon glyphicon-search"></i>-->
        <#--</div>-->
        <#--</div>-->
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">联系人</label>
                <input type="text" class="form-control" readonly name="providerLinkman" id="providerLinkman" >
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">联系地址</label>
                <input type="text" class="form-control" readonly name="providerAddr" id="providerAddr">
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">开户银行</label>
                <input type="text" class="form-control" readonly name="providerBranch" id="providerBranch">
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">银行账户</label>
                <input type="text" class="form-control" readonly name="bankCardNum" id="bankCardNum" >
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">应付余额</label>
                <input type="text" class="form-control" readonly name="amountTotal" id="amountTotal" >
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">总付款金额</label>
                <input type="text" class="form-control" readonly name="amountPayment" id="amountPayment" >
            </div>
        </div>
        <div class="col-sm-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">备注</label>
                <input type="text" class="form-control" name="remark" id="remark" fs="true">
            </div>
        </div>
    </form>
</div>

<div class="panel panel-default publicPanel" id="b_table">
    <div class="row">
        <div class="input-group box_zll">
            <button  class="btn btn-addbag"  id="addRow">新增行</button>
            <button  class="btn btn-removebag"  id="deleteRow">删除行</button>
            <#--<button  class="btn btn-redactbag"  id="filtrkolumn">筛选列</button>-->
        </div>
    </div>
    <table id="grid-table"></table>
</div>
<#--<style>
#grid-table input{
margin: 0 auto;
display: block;
}

#deleteRow:hover{    background-color: #D15B47!important;
                    border-color: #D15B47;
}


</style>-->

</body>
<#--<script>-->
    <#--alert(0);-->
    <#--var dataJson = ;-->
    <#--alert(dataJson[0].id);-->
<#--</script>-->
<script src="/static/assets/js/GridSub.js">
</script>
<script src="/static/js/balance/banlacePaymentAdd.js?v=${jsVersion}"></script>
</html>