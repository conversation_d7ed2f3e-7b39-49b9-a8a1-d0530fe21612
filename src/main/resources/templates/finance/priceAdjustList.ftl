<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="utf-8" />
    <title>Dashboard - Ace Admin</title>

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />

    <#include "/common/top-common.ftl"/>

    <style>
        #jqgh_grid-table_cb{display: none;}
    </style>
</head>
<body id="innerBody">
<!-- 工具栏 -->
<div id="toolbox" class="toolbox newToolbox">
    <a href="/finance/adjust/toAdd" class="btn btn-addbag btn-default btn-round"> 新增</a>
    <a href="javascript:;" class="btn btn-redactbag btn-default btn-round" id="editAdjustBtn">  编辑</a>
    <!--<a href="javascript:;" class="btn btn-white btn-default btn-round btnDelete">  删除</a>-->
    <a href="javascript:;" class="btn btn-removebag btn-default btn-round btnDelete" id="deleteAdjustBtn" ps="true">  删除</a>
</div>
<div class="panel panel-default publicPanel newPanel">
    <form class="commonValidate">
        <div class="col-xs-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">单据编号</label>
                <input type="text" name="pref" id="pref" class="form-control">
            </div>
        </div>
        <div class="col-xs-3 marginTop5">
            <div class="input-group">
                <label class="input-group-addon">申请人</label>
                <input type="text" name="username" id="username" class="form-control">
            </div>
        </div>
        <div class="col-xs-3 marginTop5 ">
            <div class="input-group">
                <label class="input-group-addon">开始日期</label>
                <input type="text" name="starttime" id="date_start_inp" value="${starttime!''}" class="Wdate form-control"   onfocus="WdatePicker({maxDate:'#F{$dp.$D(\'date_end_inp\')}'})" style="height: inherit;"/>
            </div>
        </div>
        <div class="col-xs-3 marginTop5 ">
            <div class="input-group">
                <label class="input-group-addon">结束日期</label>
                <input type="text" name="endtime" id="date_end_inp" value="${endtime!''}" class="Wdate form-control"  onfocus="WdatePicker({minDate:'#F{$dp.$D(\'date_start_inp\')}'})" style="height: inherit;"/>
            </div>
        </div>
        <div class="col-xs-12 marginTop5">
            <div class="input-group">
                <button id="queryBtn" type="button" class="btn btn-redactbag forbidRepeatSearch">  查询</button>
                <button id="resertBtn" type="button" class="btn btn-return">   重置</button>
                <button class="btn btn-redactbag" id="filtrkolumn">筛选列</button>
            </div>
        </div>
    </form>
</div>
<div class="panel panel-default publicPanel" id="b_table">
    <table id="grid-table"></table>
    <div id="grid-pager" style="margin-top: 20px !important;"></div>
</div>
</body>

<script src="/static/js/finance/priceAdjustList.js?v=${jsVersion}"></script>

</html>
