<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="utf-8" />
    <title>Dashboard - Ace Admin</title>

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />

    <#include "/common/top-common.ftl"/>
</head>
<body id="innerBody">
<!-- 工具栏 -->
<div id="toolbox" class="toolbox newToolbox">
    <button id="downloadExcel" type="button" class="btn btn-redactbag"> 导出Excel</button>
</div>
<div class="panel panel-default publicPanel newPanel">
    <form class="commonValidate" id="searchForm">
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">单据编号</label>
                    <input type="text" name="pref" id="pref" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">商品</label>
                    <input type="text" name="proname" id="proname" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">申请人</label>
                    <input type="text" name="username" id="username" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">开始日期</label>
                    <input type="text" name="starttime" id="date_start_inp" value="${starttime!''}" class="Wdate form-control"   onfocus="WdatePicker({maxDate:'#F{$dp.$D(\'date_end_inp\')}'})" style="height: inherit;"/>
                </div>
            </div>

            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">结束日期</label>
                    <input type="text" name="endtime" id="date_end_inp" value="${endtime!''}" class="Wdate form-control"  onfocus="WdatePicker({minDate:'#F{$dp.$D(\'date_start_inp\')}'})" style="height: inherit;"/>
                </div>
            </div>

            <div class="col-xs-9 marginTop5">
                <div class="input-group">
                    <button id="queryBtn" type="button" class="btn btn-redactbag forbidRepeatSearch">  查询</button>
                    <button id="resertBtn" class="btn btn-return" type="button">   重置</button>
                    <button class="btn btn-redactbag" id="filtrkolumn">筛选列</button>
                </div>
            </div>

    </form>
</div>
<div class="panel panel-default publicPanel" id="b_table">
    <table id="grid-table"></table>
    <div id="grid-pager" style="margin-top: 20px !important;"></div>
    <div hidden>
        <input id="unitData" value='${unitList!""}'/>
    </div>
</div>

</body>

<script src="/static/js/finance/adjustDetailsList.js?v=${jsVersion}"></script>

</html>
