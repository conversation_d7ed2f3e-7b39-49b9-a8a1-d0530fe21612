<!DOCTYPE html>
<html lang="en">
<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
	<meta charset="utf-8" />
	<title>Dashboard - Ace Admin</title>


	<meta name="description" content="overview &amp; stats" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
	<#include "/common/top-common.ftl"/>
</head>
<style type="text/css">
    /*.newPanel .marginTop5{padding-left: 12px;padding-right: 12px;}*/
    #b_table{
        display: none;
    }
    .jqg-first-row-header{
        opacity: 0;
    }
    #b_table #gview_grid-table>.ui-jqgrid-bdiv {
        top: 0px !important;
    }
    .ui-th-column-header{
        text-align: center !important;
        border-top: 1px solid #ccc !important;
    }
    #b_table #gview_grid-table>.ui-jqgrid-bdiv {
        top: 80px !important;
    }
    .ui-jqgrid .ui-jqgrid-labels th.ui-th-column-header{
        text-align: center !important;
    }
    #grid-table_rn{
        width:36px;
    }
    /* 隐藏列*/
    #grid-table > tbody > .jqgfirstrow td:nth-child(2) {
        width: 0 !important;
        border-right-width: 0 !important;
    }
    .jqg-first-row-header .ui-first-th-ltr:nth-child(2){
        width: 0 !important;
        border-right-width: 0 !important;
    }
    .ui-sgcollapsed{
        border-right-width: 0px !important;
    }
    .ui-jqgrid-htable th.ui-first-th-ltr{
        height: 0px !important;
    }
</style>
<body id="innerBody">

<div class="panel panel-default publicPanel newPanel" style="display: none">
    <table id="summgrid-table"></table>
</div>

<div id="toolbox" class="toolbox newToolbox">

                    <button id="exportExcel" type="button" class="btn btn-redactbag"> 导出Excel</button>
                    <button type="button" class="btn btn-redactbag" id="summary-filter">汇总统计</button>
		</div>
<div class="panel panel-default publicPanel newPanel">
	<form class="commonValidate" id="searchForm">

            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">单据编号</label>
                    <input type="text" lit="seach" name="pref" id="pref" class="form-control">
                </div>
            </div>

            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">摘要</label>
                    <select class="form-control"  lit="seach"  name="summary" id="summary" >
                        <option value="">全部</option>
                    </select>
                </div>
            </div>

            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">商品</label>
                    <input type="text" lit="seach"  id="commodityVal" name="commodityVal" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">供应商</label>
                    <input type="text" lit="seach"  id="supplierName" name="supplierName" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5 ">
                <div class="input-group">
                    <label class="input-group-addon">开始日期</label>
                    <input type="text" id="startDate"  lit="seach"  name="CreateDate" class="form-control Wdate"   <#if beginTime??> value="${beginTime}"</#if>  onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endDate\');}'})"  style="height: inherit;">
                </div>
            </div>
            <div class="col-xs-3 marginTop5 ">
                <div class="input-group">
                    <label class="input-group-addon">结束日期</label>
                    <input type="text" id="endDate" lit="seach" name="endCreateTime"  class="form-control  Wdate"  <#if endTime??> value="${endTime}"</#if>  onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startDate\');}'})" style="height: inherit;">
                </div>
            </div>
            <div class="col-xs-6 marginTop5 ">
                <div class="input-group">
                    <button id="queryBtn" type="button" class="btn btn-redactbag forbidRepeatSearch">  查询</button>
                    <button id="disQueryCondition" type="reset" class="btn btn-return">  重置</button>
                    <button class="btn btn-redactbag" id="filtrkolumn">筛选列</button>
              </div>
            </div>
	</form>
</div>
<div class="panel panel-default publicPanel" id="b_table">
        <table id="grid-table"></table>
        <div id="grid-pager" style="margin-top: 20px !important;"></div>
</div>



<#include "../common/tb-filter-statistic.ftl"/>
</body>
<script src="/static/js/finance/financeList.js?v=${jsVersion}"></script>

</html>

