<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta charset="utf-8"/>
    <link rel="icon" href="/static/assets/images/login/favicon.ico" type="image/x-icon"/>
    <title></title>
    <meta name="description" content="overview &amp; stats"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>
    <link rel="stylesheet" href="/static/assets/css/index.css?v=${jsVersion}"/>
    <#include "/common/top-common.ftl"/>
    <style>
        .dropdown {
            width: 100%;
        }

        .select-btn {
            text-align: left;
        }

        .select-single {
            text-align: center;
        }

        .btn-group {
            height: 36px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            position: relative;
        }

        .shownAllToggle {
            position: absolute;
            left: 0;
        }

        .btn {
            margin: 0 0 0 10px !important;
        }

        .togglg {
            display: none;
        }

        input[type=number] {
            -moz-appearance: textfield;
        }

        input[type=number]::-webkit-inner-spin-button,
        input[type=number]::-webkit-outer-spin-button {
            -webkit-appearance: none;
        }
    </style>
</head>
<body id="innerBody">

<div id="toolbox" class="toolbox newToolbox">
    <button id="downloadExcel" type="button" class="btn btn-redactbag"> 导出Excel
    </button>
</div>
<div id="searchbox" class="panel panel-default publicPanel newPanel">
    <form class="commonValidate row" id="searchForm">

        <input type="hidden" id="pref" name="pref" value="${saasProductStockReportVo.pref!""}">
        <input type="hidden" id="summaryNew" name="summaryNew" value="${saasProductStockReportVo.summary!""}">
        <input type="hidden" id="commodityVal" name="commodityVal" value="${saasProductStockReportVo.commodityVal!""}">
        <input type="hidden" id="supplierNameNew" name="supplierNameNew"
               value="${saasProductStockReportVo.supplierName!""}">
        <input type="hidden" id="dateStart" name="dateStart" value="${saasProductStockReportVo.dateStart!""}">
        <input type="hidden" id="dateEnd" name="dateEnd" value="${saasProductStockReportVo.dateEnd!""}">
        <input type="hidden" id="exportCnName" name="exportCnName" value="${saasProductStockReportVo.exportCnName!""}">
        <input type="hidden" id="exportEnName" name="exportEnName" value="${saasProductStockReportVo.exportEnName!""}">
        <input type="hidden" id="exportInputType" name="exportInputType"
               value="${saasProductStockReportVo.exportInputType!""}">
        <input type="hidden" id="typeFields" name="typeFields" value="${saasProductStockReportVo.typeFields!""}">
        <input type="hidden" id="collectFields" name="collectFields"
               value="${saasProductStockReportVo.collectFields!""}">

        <!--以下注释勿删-->
        <!--通用控件-->
        <!--
        <div class="col-xs-4 marginTop5 togglg">
            <div class="input-group input-group-tail">
                <label class="input-group-addon">商品编号</label>
                <div style="flex: 2">
                    <div class="dropdown">
                        <span class="caret"></span>
                        <input type="hidden" value="-1">
                        <input type="button"
                               class="dropdown-toggle select-btn"
                               data-toggle="dropdown"
                               data-val=""
                               aria-haspopup="true"
                               aria-expanded="false"
                        >
                        <ul class="dropdown-menu select-single">
                            <li><a href="0">精准查询</a></li>
                            <li><a href="1">模糊查询</a></li>
                            <li><a href="2">=</a></li>
                            <li><a href="3">≠</a></li>
                            <li><a href="4">></a></li>
                            <li><a href="5">>=</a></li>
                            <li><a href="6"><</a></li>
                            <li><a href="7"><=</a></li>
                        </ul>
                    </div>
                </div>
                <span class="transit">-</span>
                <div style="flex: 4">
                    <input type="text" class="form-control">
                </div>
            </div>
        </div>
        -->
        <!--摘要控件-->
        <!--
        <div class="col-xs-4 marginTop5 togglg">
            <div class="input-group input-group-tail">
                <label class="input-group-addon">商品编号</label>
                <div style="flex: 2">
                    <div class="dropdown">
                        <span class="caret"></span>
                        <input type="hidden" value="-1">
                        <input type="button"
                               class="dropdown-toggle select-btn"
                               data-toggle="dropdown"
                               data-val=""
                               aria-haspopup="true"
                               aria-expanded="false"
                        >
                        <ul class="dropdown-menu select-single">
                            <li><a href="0">精准查询</a></li>
                            <li><a href="1">模糊查询</a></li>
                            <li><a href="2">=</a></li>
                            <li><a href="3">≠</a></li>
                            <li><a href="4">></a></li>
                            <li><a href="5">>=</a></li>
                            <li><a href="6"><</a></li>
                            <li><a href="7"><=</a></li>
                        </ul>
                    </div>
                </div>
                <span class="transit">-</span>
                <div style="flex: 4">
                    <div class="dropdown">
                        <span class="caret"></span>
                        <input type="hidden" value="-1">
                        <input type="button"
                               class="dropdown-toggle select-btn"
                               data-toggle="dropdown"
                               data-val=""
                               aria-haspopup="true"
                               aria-expanded="false"
                        >
                        <ul class="dropdown-menu select-single">
                            <li><a href="0">精准查询</a></li>
                            <li><a href="1">模糊查询</a></li>
                            <li><a href="2">=</a></li>
                            <li><a href="3">≠</a></li>
                            <li><a href="4">></a></li>
                            <li><a href="5">>=</a></li>
                            <li><a href="6"><</a></li>
                            <li><a href="7"><=</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        -->
        <!--日期控件-->
        <!--
        <div class="col-xs-4 marginTop5">
            <div class="input-group input-group-tail">
                <label class="input-group-addon">商品编号</label>
                <div>
                    <input type="text" class="Wdate form-control" onfocus="WdatePicker()" />
                </div>
                <span class="transit">-</span>
                <div>
                    <input type="text" class="Wdate form-control" onfocus="WdatePicker()" />
                </div>
            </div>
        </div>
        -->

        <div class="col-xs-4 marginTop5  pull-right" id="btnBoxer">
            <div class="input-group btn-group">
                <a href="javascript:0" class="shownAllToggle">展开全部</a>
                <button type="button" class="btn btn-return" id="btnReset">重置</button>
                <button type="button" class="btn btn-redactbag" id="btnSearch">查询</button>
            </div>
        </div>
    </form>
</div>

<div class="panel panel-default publicPanel newPanel" id="b_table">
    <table id="grid-table"></table>
    <div id="grid-pager" style="margin-top: 20px !important;"></div>
</div>
</body>
<script>
    $(function () {

        //导出Excel
        $("#downloadExcel").on('click',debounceSearch(function () {
            // debugger;
            //  $("#searchForm").attr("action", "/finance/exportCommodityAccountExcle");
            // serializeForm()
            // $("#searchForm").submit();
            var data = serializeForm()
            var fileName = '商品账页查询' + formatDateym(new Date());
            exportModuleRequest.newAjaxExportBlob({
                url: '/report/productStockDetail/exportCommodityAccountExcle',
                data: data,
                success: function (res) {
                    blobExport(res, fileName)
                },
                template: function () {}
            })
        }));

        var colNames = [];
        var colModel = [];

        InitSearchparam();

        function InitSearchparam() {
            var exportCnName = $("#exportCnName").val();
            var exportEnName = $("#exportEnName").val();
            var exportInputType = $("#exportInputType").val();

            var columnArr = [];
            if (exportEnName && exportEnName.length) {
                var exportCnNameTmp = exportCnName.split(',');
                var exportEnNameTmp = exportEnName.split(',');
                var exportInputTypeTmp = exportInputType.split(',');

                exportEnNameTmp.forEach((item, index) => {
                    columnArr.push({
                        cnName: exportCnNameTmp[index],
                        enName: item,
                        inputType: exportInputTypeTmp[index]
                    })
                });
            }

            var htmlStr = [];
            if (columnArr.length) {

                $('.shownAllToggle')[columnArr.length > 8 ? 'show' : 'hide']();

                columnArr.forEach((item, index) => {
                    colNames.push(item.cnName);
                    var columnSorts = ['inNumber','inTotalPrice','outNumber','outTotalPrice','stockNumber','stockAmount','totalAcount']
                    if(columnSorts.indexOf(item.enName)>-1){
                        colModel.push({
                            name: item.enName,
                            index: item.enName,
                            sortable:true,
                            inputType: item.inputType
                        });
                    }
                    else {
                        colModel.push({
                            name: item.enName,
                            index: item.enName,
                            sortable:false,
                            inputType: item.inputType
                        });
                    }
                    htmlStr.push('<div class="col-xs-4 marginTop5 ' + (index > 7 ? 'togglg' : '') + '">');
                    htmlStr.push('<div class="input-group input-group-tail">');
                    htmlStr.push('<label class="input-group-addon" style="min-width:110px!important;">' + item.cnName + '</label>');

                    if (item.inputType === "dater") {
                        htmlStr.push('<div>');
                        htmlStr.push('<input type="text" class="Wdate form-control" name="' + (item.enName) + 'Start" id="' + (item.enName) + 'Start" onfocus="WdatePicker({dateFmt:\'yyyy-MM-dd\',maxDate:\'#F{$dp.$D(' + (item.enName) + 'End)}\'})" resetable="true"  />');
                        htmlStr.push('</div>');
                        htmlStr.push('<span class="transit">-</span>');
                        htmlStr.push('<div>');
                        htmlStr.push('<input type="text" class="Wdate form-control" name="' + (item.enName) + 'End" id="' + (item.enName) + 'End" onfocus="WdatePicker({dateFmt:\'yyyy-MM-dd\',minDate:\'#F{$dp.$D(' + (item.enName) + 'Start)}\'})" resetable="true"  />');
                        htmlStr.push('</div>');
                    } else {
                        htmlStr.push('<div style="flex: 2">');
                        htmlStr.push('<div class="dropdown">');
                        htmlStr.push('<span class="caret"></span>');
                        htmlStr.push('<input type="hidden" value="" name="' + (item.enName) + 'El">');
                        htmlStr.push('<input type="button" class="dropdown-toggle select-btn" data-toggle="dropdown" value="" data-val="" aria-haspopup="true" aria-expanded="false">');
                        htmlStr.push('<ul class="dropdown-menu select-single">');

                        if (item.inputType === "text") {
                            if (item.enName !== 'summary') {
                                htmlStr.push('<li><a href="1">模糊查询</a></li>');
                            }
                            htmlStr.push('<li><a href="0">精准查询</a></li>');
                        } else if (item.inputType === "number") {
                            htmlStr.push('<li><a href="2">=</a></li>');
                            htmlStr.push('<li><a href="3">≠</a></li>');
                            htmlStr.push('<li><a href="4">></a></li>');
                            htmlStr.push('<li><a href="5">>=</a></li>');
                            htmlStr.push('<li><a href="6"><</a></li>');
                            htmlStr.push('<li><a href="7"><=</a></li>');
                        }
                        htmlStr.push('</ul>');
                        htmlStr.push('</div>');
                        htmlStr.push('</div>');

                        htmlStr.push('<span class="transit">-</span>');

                        htmlStr.push('<div style="flex: 4">');
                        //摘要特殊处理
                        if (item.enName === 'summary') {
                            htmlStr.push('<div class="dropdown" id="' + item.enName + '">');
                            htmlStr.push('<span class="caret"></span>');
                            htmlStr.push('<input type="hidden" value="" name="' + (item.enName) + '">');
                            htmlStr.push('<input type="button"  class="dropdown-toggle select-btn" data-toggle="dropdown" data-val="" aria-haspopup="true" aria-expanded="false">');
                            htmlStr.push('<ul class="dropdown-menu select-single">');
                            //htmlStr.push('<li><a href="0">精准查询</a></li>');//示例勿删
                            htmlStr.push('</ul>');
                            htmlStr.push('</div>');
                        } else {
                            switch (item.inputType) {
                                case 'number':
                                    htmlStr.push('<input type="number" class="form-control" name="' + (item.enName) + '" resetable="true" >');
                                    break;
                                case 'text':
                                    htmlStr.push('<input type="text" class="form-control" name="' + (item.enName) + '" resetable="true" >');
                                    break;
                                default:
                                    htmlStr.push('<input type="text" class="form-control" name="' + (item.enName) + '" resetable="true" >');
                                    break;
                            }
                        }
                        htmlStr.push('</div>');
                    }

                    htmlStr.push('</div>');
                    htmlStr.push('</div>');
                });

                $(htmlStr.join('')).insertBefore($("#btnBoxer"));
                $('#b_table').css({
                    "margin-top": $("#toolbox").outerHeight(true) + $("#searchbox").outerHeight(true)
                });

                //设置初始值
                resetDropdownValue();
                isNumberValidate();

                //ajax获取摘要选项
                var $summary = $("#summary");
                if ($summary) {
                    getFinanceDictList(function (data) {
                        var liHtmlArr = [];
                        if (data && data.length) {
                            liHtmlArr.push('<li><a href="-1" class="active">全部</a></li>');
                            data.forEach((item, index) => {
                                liHtmlArr.push('<li><a href="' + item.value + '">' + item.name + '</a></li>');
                            });
                        }
                        $summary.find('.dropdown-menu').html(liHtmlArr.join(''));
                        $summary.find('input[type=hidden]').val("-1");
                        $summary.find('.select-btn').val("全部").attr({"data-val":"-1"});
                    });
                }

                initTable(serializeForm());
            }
        }

        //隐藏展示事件
        var toggleType = false;//false:超出隐藏,true:显示全部
        $(".shownAllToggle").on('click', function () {
            toggleType = !toggleType;
            $('.togglg')[toggleType ? 'show' : 'hide']();
            $(this).html(toggleType ? '收起全部' : '展开全部');
            $('#b_table').css({
                "margin-top": $("#toolbox").outerHeight(true) + $("#searchbox").outerHeight(true)
            })
        });

        // 下拉单选 菜单交互
        $(document).on('click', ".dropdown-menu.select-single>li>a", function (e) {
            e.preventDefault();
            var selector = $(this).parents(".dropdown-menu").prevAll('input.dropdown-toggle.select-btn')
            if ($(this).hasClass('active')) {
                $(this).removeClass('active');
            } else {
                $(this).addClass('active');
            }
            selector.val(e.target.innerText);
            selector.prev().val(e.target.attributes.href.value);
            selector.focus()
        });
        // 单选下拉回显
        $(document).on("click", ".dropdown-toggle.select-btn", function () {
            var key = $(this).prev().val();
            var ul = $(this).next('.dropdown-menu.select-single');
            ul.find('a[href="' + key + '"]').addClass('active').parent().siblings().children().removeClass('active')
        });

        //序列化查询表单参数
        function serializeForm() {
            // debugger;
            var formData = $("#searchForm").serializeArray();
            var requestDataObj = {};
            if (formData && formData.length) {
                formData.forEach(item => {
                    requestDataObj[item.name] = item.value
                })
            }

            // requestDataObj["pref"]=$("#pref").val();
            // requestDataObj["summaryNew"]=$("#summaryNew").val();
            // requestDataObj["commodityVal"]=$("#commodityVal").val();
            // requestDataObj["supplierNameNew"]=$("#supplierNameNew").val();
            // requestDataObj["dateStart"]=$("#dateStart").val();
            // requestDataObj["dateEnd"]=$("#dateEnd").val();

            console.log(requestDataObj);
            return requestDataObj;
        }

        //查询
        $("#btnSearch").on('click', function () {

            //if (validform().form()) {
            $("#grid-table").jqGrid('setGridParam', {
                postData: serializeForm(), page: 1
            }).trigger('reloadGrid');
            //}
        });


        //重置
        $("#btnReset").on('click', function () {
            $('[resetable="true"]').val('');
            resetDropdownValue();
            $("#btnSearch").trigger('click');
            /*
            $("#grid-table").jqGrid('setGridParam', {
                postData: serializeForm(), page: 1
            }).trigger('reloadGrid');
            */
        });

        function initTable(postData) {

            var headerWidth = 0;
            colModel.forEach((item, index) => {
                headerWidth += item.width || 150;
            });
            headerWidth += 35;
            var autoWidth = false;
            if (headerWidth < $('#b_table').width()) {
                autoWidth = true
            }

            $("#grid-table").jqGrid({
                url: "/report/productStockDetail/getCommodityAccountExclePageList",
                ajaxGridOptions: {featureType: 'export'},
                autowidth: autoWidth,
                postData: postData,
                jsonReader: {
                    root: "result.list",
                    page: "result.pageNum",
                    total: "result.pages",
                    records: "result.total",
                    // repeatitems: false,
                    //id: "0"
                },
                sortable: true,
                datatype: "json", //数据来源，本地数据（local，json,jsonp,xml等）
                height: "auto",//高度，表格高度。可为数值、百分比或'auto'
                mtype:"POST",//提交方式
                colNames: colNames,
                colModel: colModel,
                viewrecords: true,
                rowNum: 50,//每页显示记录数
                rowList: [10, 20, 30, 40, 50],//用于改变显示行数的下拉列表框的元素数组。
                pager: '#grid-pager',//分页、按钮所在的浏览导航栏
                altRows: true,//设置为交替行表格,默认为false
                //toppager: true,//是否在上面显示浏览导航栏
                multiselect: false,//是否多选
                rownumbers: true,
                pagerpos: 'right',
                recordpos: 'left',
                loadComplete: function () {
                    var table = $(this);
                    setTimeout(function () {
                        updatePagerIcons(table);
                    }, 0);
                },
                loadBeforeSend: function(xhr,ajaxConfig){
                    exportModuleRequest.changeHostname(xhr,ajaxConfig);
                }
            });
        }

        //查询摘要
        function getFinanceDictList(cb) {
            $.ajax({
                type: "get",
                url: "/finance/getDictList",
                data: {"bussinessId": 20096},
                dataType: "json",
                contentType: "application/json",
                success: function (data) {
                    if (data.code == 0) {
                        cb(data.result);
                    }
                }
            });
        }

        //设置Dropdown初始值
        function resetDropdownValue() {
            var $dropdown = $(".dropdown");
            var hiddenInput = void 0;
            var selectInputBtn = void 0;
            var selectSingleUl = void 0;
            var firstLi = void 0;
            var firstLiA = void 0;

            $.each($dropdown, (index, item) => {
                hiddenInput = $(item).find('input[type=hidden]');//隐藏域
                selectInputBtn = $(item).find('input.select-btn');//button
                selectSingleUl = $(item).find('.select-single');//ul
                firstLi = selectSingleUl.find('li:first');//第一项
                firstLiA = firstLi.find('a');

                hiddenInput.val(firstLiA.attr('href'));
                selectInputBtn.val(firstLiA.html()).attr({"data-val": firstLiA.attr('href')});

                firstLi.siblings().find('a').removeClass('active');
                firstLiA.addClass('active');
            });
        }

        //校验 开始
        function validform() {
            return $(".commonValidate").validate({
                errorPlacement: function (error, element) {
                    if (element.data('toggle') == "tooltip") {
                        $(element).tooltip('destroy');
                    }
                    var placement = element.data('placement') ? element.data('placement') : 'bottom';
                    element.data({
                        title: '<span style="color:#d43f3a" class="glyphicon glyphicon-xclamation-sign"></span>' + error.html(),
                        html: true,
                        toggle: 'tooltip',
                        placement: placement,
                        container: 'body'
                    });
                    element.tooltip('show');
                    setTimeout(function () {
                        element.tooltip('destroy');
                    }, 1000)
                }
            })
        }

        //校验 结束
        function isNumberValidate() {
            //统计条目数
            $('body').on('blur','input[name="totalAcount"]', function (e) {
                var reg1=new RegExp(/^[0-9]\d*$/);
                if(!reg1.test(e.target.value)){
                    e.target.value='';
                }
            });
            $('body').on('blur','input[name="inNumber"]', function (e) {
                var reg1=new RegExp(/^[0-9]\d*$/);
                if(!reg1.test(e.target.value)){
                    e.target.value='';
                }
            });
            $('body').on('blur','input[name="outNumber"]', function (e) {
                var reg1=new RegExp(/^[0-9]\d*$/);
                if(!reg1.test(e.target.value)){
                    e.target.value='';
                }
            });
            $('body').on('blur','input[name="stockNumber"]', function (e) {
                var reg1=new RegExp(/^[0-9]\d*$/);
                if(!reg1.test(e.target.value)){
                    e.target.value='';
                }
            });
        }

    });
</script>
</html>