<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Dashboard - Ace Admin</title>
    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <#include "/common/top-common.ftl"/>
</head>
<body style="background: #fff">
	<div class="panel panel-default publicPanel newPanel">
        <div class="row dialog-search">
            <div class="col-xs-4 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">商品编号</label>
                    <input type="text" name="seach" id="search_val" class="form-control">
                </div>
            </div>
            <div class="col-xs-4 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">打印状态</label>
                    <select id="printTypeBtn" name="printTypeBtn" class="form-control">
                        <option value=''>全部</option>
                        <option value='0'>未打印</option>
                        <option value='1'>已打印</option>
                    </select>
                </div>
            </div>
            <div class="col-xs-4 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">处方分类</label>
                    <select class="form-control" id="prescriptionClassification" name="prescriptionClassification">
                        <option value="">全部</option>
                        <#if (cfDict)??>
                            <#list cfDict as cf>
                                <option value="#{cf.id}">${cf.name}</option>
                            </#list>
                        </#if>
                    </select>
                </div>
            </div>
            <div class="col-xs-4 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">库存</label>
                    <select class="form-control" id="stockNum" name="stockNum">
                        <option value="-1">全部</option>
                        <option value="0">0库存</option>
                        <option value="1" selected>非0库存</option>
                    </select>
                </div>
            </div>
            <div class="col-xs-8 marginTop5">
                <div class="input-group">
                    <button type="button" class="btn btn-redactbag" id="btn_search">  查询</button>
                </div>
            </div>
        </div>
	</div>
    <div class="panel panel-default publicPanel" style="margin: 0;" id="b_table">
        <table id="grid-table"></table>
        <div id="grid-pager" style="margin-top: 20px !important;"></div>
        <div class="panel panel-default publicPanel" id="b_table">
            <div hidden>
                <input id="unitList" value='${unitList!""}' />
            </div>
            <div hidden>
                <input id="cfList" value='${cfList!""}' />
            </div>
        </div>
    </div>
	<div class="footer-btns text-right btn_sub_can_div " style="">
        <button type="button" class="btn btn-return" id="btn_modalClose">取消</button>
        <button type="button" class="btn btn-redactbag ui-dialog-ok" id="btn_submit">确定</button>
	</div>
</body>
<script type="text/javascript" src="/static/js/basicinfo/printAdd.js?v=${jsVersion}"></script>
</html>