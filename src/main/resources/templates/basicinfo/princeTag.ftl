<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        html, body {
            background: #FFFFFF;
            font-family: PingFangSC-Regular;
            font-size: 12px;
            color: #000000;
            height: 625px;
            overflow-y: scroll;
        }

        .content {
            width: 1161px;
            height: 100%;
            margin: 0 auto;
        }

        .wrapper {
            height: 248px;
            width: 386px;
            background: url("../../static/assets/bg_tag.png") no-repeat;
            float: left;
            margin: 0 1px 1px 0;
        }

        .clearfix:after {
            visibility: hidden;
            display: block;
            font-size: 0;
            content: " ";
            clear: both;
            height: 0;
        }

        .clearfix {
            *zoom: 1;
        }

        .position {
            width: 100%;
            height: 100%;
            box-sizing: border-box;
            position: relative;
        }

        .name {
            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: #000000;
            font-weight: bold;
            position: absolute;
            top: 38px;
            left: 200px;
        }

        .ling-price {
            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: #000000;
            position: absolute;
            top: 86px;
            left: 79px;
            font-weight: bold;
        }

        .company-name {
            position: absolute;
            top: 86px;
            left: 226px;
        }

        .address {
            position: absolute;
            top: 124px;
            left: 206px;
        }

        .code {
            position: absolute;
            top: 124px;
            left: 308px;
        }

        .guige {
            position: absolute;
            top: 166px;
            left: 200px;
        }

        .unit {
            position: absolute;
            top: 166px;
            left: 335px;
        }

        .member-name {
            font-family: PingFangSC-Medium;
            font-size: 14px;
            color: #000000;
            font-weight: bold;
            position: absolute;
            top: 132px;
            left: 53px;
        }

        .member {
            font-family: PingFangSC-Medium;
            font-size: 24px;
            color: #000000;
            font-weight: bold;
            position: absolute;
            top: 150px;
            left: 53px;
        }

    </style>
</head>
<body>

<div class="content clearfix">

<#--<div class="wrapper ">-->
<#--<div class="position">-->
<#--<div class="name">测试库存小数2（拆零）</div>-->
<#--<div class="ling-price">11.9</div>-->
<#--<div class="company-name">北京华润有限公司嗷嗷</div>-->
<#--<div class="address">北京</div>-->
<#--<div class="code">A1C1</div>-->
<#--<div class="guige">0.02*1</div>-->
<#--<div class="unit">盒</div>-->
<#--<div class="member-name">会员价：</div>-->
<#--<div class="member">10.9</div>-->
<#--</div>-->
<#--</div>-->
<#--<div class="wrapper ">-->
<#--</div>-->
<#--<div class="wrapper ">-->
<#--</div>-->
<#--<div class="wrapper ">-->
<#--</div>-->
<#--<div class="wrapper "></div>-->
<#--<div class="wrapper "></div>-->
<#--<div class="wrapper "></div>-->
<#--<div class="wrapper "></div>-->
<#--<div class="wrapper "></div>-->
<#--<div class="wrapper "></div>-->
<#--<div class="wrapper "></div>-->
<#--<div class="wrapper "></div>-->
<#--<div class="wrapper "></div>-->
<#--<div class="wrapper "></div>-->
<#--<div class="wrapper "></div>-->
<#--<div class="wrapper "></div>-->
<#--<div class="wrapper "></div>-->
<#--<div class="wrapper "></div>-->
<#--<div class="wrapper "></div>-->
<#--<div class="wrapper "></div>-->
<#--<div class="wrapper "></div>-->

</div>

<script src="/static/assets/js/jquery-2.1.4.min.js"></script>
<script type="text/javascript">
    $(function () {

        var dialog = parent.dialog.get(window);
        var list = dialog.data.list;

        var str = '';
        $.each(list, function (index, item) {


            str += '<div class="wrapper ">\n' +
                    '        <div class="position">\n' +
                    '            <div class="name">' + item.commonName + '</div>\n' +
                    '            <div class="ling-price">' + item.retailPrice + '</div>\n' +
                    '            <div class="company-name">' + item.manufacturer + '</div>\n' +
                    '            <div class="address">北京</div>\n' +
                    '            <div class="code">' + item.pharmacyPref + '</div>\n' +
                    '            <div class="guige">' + item.attributeSpecification + '</div>\n' +
                    '            <div class="unit">' + item.unitId + '</div>\n' +
                    '            <div class="member-name">会员价：</div>\n' +
                    '            <div class="member">' + item.vipPrice + '</div>\n' +
                    '        </div>\n' +
                    '    </div>';
            var current = index + 1;
            if(current % 21 == 0){
                str += '<div style="margin-bottom: 20px;opacity: 0;">123</div>'
            }

        })

        if (list.length <= 21) {
            for (var i = 0; i< (21-list.length); i++){
                str += '<div class="wrapper "></div>'
            }
        }else {
            for (var i = 0; i< (21-list.length % 21); i++){
                str += '<div class="wrapper "></div>'
            }
        }

        $('.content').empty();
        $('.content').append(str);

    })
</script>

</body>
</html>
