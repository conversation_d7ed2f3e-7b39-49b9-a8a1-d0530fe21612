<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="utf-8" />
    <title>Dashboard - Ace Admin</title>

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />

    <#include "/common/top-common.ftl"/>
</head>

<body id="innerBody">
<!-- 工具栏 -->
<div id="dictionary_tools" class="toolbox">
    <button type="button" onclick="location.href = '/baseinfo/proScattered/toList'" class="btn btn-return">  返回</button>
    <button type="button" id="formSubmit" class="btn btn-redactbag" ps="true">  提交</button>
</div>
<form id="formData" method="post">
    <div class="panel panel-default publicPanel newPanel">
        <h4 class="publicPanelTitle">拆零前</h4>
        <div class="commonValidate row">
            <div class="col-xs-3" hidden>
                <div class="control-group">
                    <label class="control-label">商品ID</label>
                    <div class="col-sm-9">
                        <input type="text" name="productId" id="productId" class="form-control">
                    </div>
                </div>
            </div>

            <div class="col-xs-3 marginTop5">
                <div class="input-group" style="position: relative">
                    <label class="input-group-addon">商品编号</label>
                    <input type="hidden" name="pref" id="queryProduct">
                    <input type="text" name="pharmacyPref" id="pharmacyPref" class="form-control">
                    <i class="glyphicon glyphicon-search"></i>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">通用名</label>
                    <input type="text" readonly name="productName" id="productName" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">商品规格</label>
                    <input type="text" readonly name="attributeSpecification" id="attributeSpecification" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">包装单位</label>
                    <select name="productUnitId" disabled id="productUnitId" class="form-control" required="required">
                        <#if (unitDict)??>
                            <#list unitDict as unit>
                                <option value="#{unit.id}">${unit.name}</option>
                            </#list>
                        </#if>
                    </select>
                </div>
            </div>

            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">生产厂家</label>
                    <input type="text" readonly name="manufacturer" id="manufacturer" class="form-control">
                </div>
            </div>
        </div>
    </div>
    
    <div class="panel panel-default publicPanel newPanel">
        <h4 class="publicPanelTitle">拆零后</h4>
        <div class="commonValidate row">
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">拆零数量</label>
                    <input type="text" id="scatteredNumber" name="scatteredNumber" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">拆零规格</label>
                    <input type="text" name="scatteredSpecification" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">拆零单位</label>
                    <select name="unitId" id="" class="form-control" required="required">
                        <#if (unitDict)??>
                            <#list unitDict as unit>
                                <option value="#{unit.id}">${unit.name}</option>
                            </#list>
                        </#if>
                    </select>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">是否启用</label>
                    <select name="status" id="" class="form-control" required="required">
                        <option value="0">否</option>
                        <option value="1" selected>是</option>
                    </select>
                </div>
            </div>

            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">备注</label>
                    <input type="text" name="remark" class="form-control">
                </div>
            </div>
        </div>
    </div>
</form>

</body>

<script src="/static/js/basicinfo/addPScattered.js?v=${jsVersion}"></script>

</html>