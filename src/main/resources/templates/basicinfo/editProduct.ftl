<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <title>Dashboard - Ace Admin</title>

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />

    <#include "/common/top-common.ftl"/>
    <style>
        html{
            overflow-y: auto;
        }
        .input-group .input-group-addon {
            min-width: 90px;
        }

        .marginTop5 {
            padding: 0 4px;
            margin-top: 6px;
        }

        .container {
            margin: 0;
        }

        .panel-content {
            padding: 10px;
        }

        .panel select{
            height: 36px;
        }

        .panel-content-title {
            font-size: 16px;
            font-weight: bold;
            text-align: left;
        }

        .panel-content-title::before {
            content: '';
            display: inline-block;
            width: 3px;
            height: 16px;
            background: linear-gradient(#090200, #431006);
            margin-right: 8px;
            position: relative;
            top: 1px;
        }

        .autocomplete-suggestions {
            border-radius: 4px;
            border: 1px solid #ccc;
            background: #ffffff;
            overflow: auto;
        }

        .autocomplete-suggestion,
        .autocomplete-no-suggestion {
            padding: 6px 12px;
        }

        .autocomplete-selected {
            background: #66AFE9;
            color: #ffffff;
            cursor: pointer;
        }

        .autocomplete-suggestions strong {
            color: #FF6633;
        }
        html{
            overflow-y: auto;
        }
        .Wdate{
            border: 1px solid #D5D5D5;
        }
      .input-group .red{
            background-color: #E5000F !important;
            color: #fff !important;
        }

    </style>
</head>
<body id="innerBody">
<!-- 工具栏 -->
<div id="dictionary_tools" class="toolbox">
    <button type="button" onclick="location.href = '<#if backUrl??>${backUrl}</#if>'" class="btn btn-return">  返回</button>
    <button type="button" id="formSubmit" class="btn btn-redactbag" ps="true">  提交</button>
    <button type="submit" id="exportExcelBtn" class="btn btn-redactbag" ps="true"> 导出Excel</button>
    <button type="button" id="deleteProBtn" class="btn btn-removebag"> 删除商品</button>
</div>
<form id="saveProduct" method="post">
    <div class="panel panel-default publicPanel newPanel">
        <h4 class="publicPanelTitle">基础属性</h4>
        <div class="commonValidate">
            <div class="col-xs-3 marginTop5" hidden>
                <div class="input-group">
                    <label class="input-group-addon">商品ID</label>
                    <input readonly type="text" id="proId" name="id" value=${result.id?c} class="form-control">
                    <input readonly type="hidden" id="status" name="status" value=${result.status?c} class="form-control">
                    <input readonly type="text" name="pref" value="${result.pref!""}" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">商品编号</label>
                    <input disabled type="text" name="pharmacyPref" value="${result.pharmacyPref!""}" class="form-control" disabled="disabled">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon"><span class="danger">*</span>通用名</label>
                    <input type="text" class="form-control" name="commonName" value="${result.commonName!""}"
                           required="required" data-msg-required='该字段为必填项' id="commonName" <#if (result.standardLibraryId)??>disabled</#if>>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">商品名称</label>
                    <input type="text" name="productName" id="productName" value="${result.productName!""}" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">标准库ID</label>
                    <#if (result.standardLibraryId)??>
                        <input readonly type="text" name="standardLibraryId" value="${result.standardLibraryId?c}"
                               class="form-control" data-title="标准库ID" id="standardLibraryId">
                    <#else>
                        <input readonly type="text" name="standardLibraryId" value=""
                               class="form-control" data-title="标准库ID" id="standardLibraryId">
                    </#if>
                    <div class="glyphicon glyphicon-search" id="searchId"></div>
                </div>
            </div>

            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon"><span class="danger">*</span>规格/型号</label>
                    <input type="text" name="attributeSpecification" value="${result.attributeSpecification!""}"
                           class="form-control" required="required" data-msg-required='该字段为必填项' id="attributeSpecification" <#if (result.standardLibraryId)??>disabled</#if>>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon"><span class="danger">*</span>单位</label>
                    <input type="text" id="unitId" class="form-control" value="${unitName!''}" placeholder="">
                </div>
                <div hidden>
                    <input type="text" name="unitId" id="unitKey" class="form-control" value="${result.unitId?c}" hidden>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon"><span class="danger">*</span>剂型</label>
                    <input type="text" id="dosageFormId" class="form-control" value="${agentName!''}" placeholder="">
                </div>
                <div hidden>
                    <input type="text" name="dosageFormId" id="dosageFormKey" class="form-control" value="${result.dosageFormId?c}" placeholder="" hidden>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">条形码</label>
                    <input type="text" name="barCode" value="${result.barCode!""}" class="form-control">
                </div>
            </div>

            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon"><span class="danger">*</span>批准文号</label>
                    <input type="text" name="approvalNumber" value="${result.approvalNumber!""}" class="form-control"
                           required="required" data-msg-required='该字段为必填项' id="approvalNumber">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon"><span class="danger">*</span>生产厂家</label>
                    <input type="text" name="manufacturer" value="${result.manufacturer!""}" class="form-control"
                           required="required" data-msg-required='该字段为必填项' id="manufacturer" <#if (result.standardLibraryId)??>disabled</#if>>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">产地</label>
                    <input type="text" name="producingArea" value="${result.producingArea!""}" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">助记码</label>
                    <input type="text" name="mnemonicCode" id = "mnemonicCode" value="${result.mnemonicCode!""}" class="form-control">
                </div>
            </div>
        </div>
    </div>

    <div class="panel panel-default publicPanel newPanel">
        <h4 class="publicPanelTitle">经营属性</h4>
        <div class="commonValidate">
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">商品类别</label>
                    <div class="dropdown">
                        <span class="caret"></span>
                        <input type="hidden"
                               value="${result.productType?c}"
                               name="productType">
                        <input type="button" class="dropdown-toggle select-btn"
                               data-toggle="dropdown"
                            <#if (proTypeDict)??>
                                <#list proTypeDict as ptype>
                                    <#if "${result.productType?c}"=="${ptype.id?c}">value="${ptype.name}"</#if>
                                </#list>
                            </#if>
                               aria-haspopup="true"
                               aria-expanded="false">
                        <ul class="dropdown-menu select-single">
                            <li><a href=""></a></li>
                            <#if (proTypeDict)??>
                                <#list proTypeDict as ptype>
                                    <li><a href="#{ptype.id}">${ptype.name}</a></li>
                                </#list>
                            </#if>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">存储条件</label>
                    <div class="dropdown">
                        <span class="caret"></span>
                        <input type="hidden" value="${result.storageCondition!-1}" name="storageCondition">
                        <input type="button" class="dropdown-toggle select-btn"
                               data-toggle="dropdown"
                            <#if (storeDict)??>
                                <#list storeDict as store>
                                    <#if "${result.storageCondition!-1}"=="${store.id?c}">value="${store.name}"</#if>
                                </#list>
                            </#if>
                               aria-haspopup="true"
                               aria-expanded="false">
                        <ul class="dropdown-menu select-single">
                            <li><a href="-1"></a></li>
                            <#if (storeDict)??>
                                <#list storeDict as store>
                                    <li><a href="#{store.id}">${store.name}</a></li>
                                </#list>
                            </#if>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">ABC分类</label>
                    <div class="dropdown">
                        <span class="caret"></span>
                        <input type="hidden" value="${result.abcDividing!-1}" name="abcDividing">
                        <input type="button" class="dropdown-toggle select-btn"
                               data-toggle="dropdown"
                            <#if (abcDict)??>
                                <#list abcDict as abc>
                                    <#if "${result.abcDividing!-1}"=="${abc.id?c}">value="${abc.name}"</#if>
                                </#list>
                            </#if>
                               aria-haspopup="true"
                               aria-expanded="false">
                        <ul class="dropdown-menu select-single">
                            <li><a href="-1"></a></li>
                            <#if (abcDict)??>
                                <#list abcDict as abc>
                                    <li><a href="#{abc.id}">${abc.name}</a></li>
                                </#list>
                            </#if>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">架位</label>
                    <div class="dropdown">
                        <span class="caret"></span>
                        <input type="hidden" value="${(result.shelfPosition!0)?c}" name="shelfPosition">
                        <input type="button" class="dropdown-toggle select-btn"
                               data-toggle="dropdown"
                            <#if (positionList)??>
                                <#list positionList as position>
                                    <#if "${(result.shelfPosition!0)?c}"=="${position.id?c}">value="${position.name}"</#if>
                                </#list>
                            </#if>
                               aria-haspopup="true"
                               aria-expanded="false">
                        <ul class="dropdown-menu select-single">
                            <li><a href="0"></a></li>
                            <#if (positionList)??>
                                <#list positionList as position>
                                    <li><a href="#{position.id}">${position.name}</a></li>
                                </#list>
                            </#if>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon"><span class="danger">*</span>含特殊药品复方制剂</label>
                    <div class="dropdown">
                        <span class="caret"></span>
                        <input type="hidden" value="${result.containingHempYn!""}" name="containingHempYn">
                        <input type="button" class="dropdown-toggle select-btn"
                               data-toggle="dropdown"
                               value="<#if "${result.containingHempYn!-1}"== "0">否</#if><#if "${result.containingHempYn!-1}"== "1">是</#if>"
                               aria-haspopup="true"
                               aria-expanded="false">
                        <ul class="dropdown-menu select-single">
                            <li><a href="-1"></a></li>
                            <li><a href="0">否</a></li>
                            <li><a href="1">是</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon"><span class="danger">*</span>处方分类</label>
                    <div class="dropdown">
                        <span class="caret"></span>
                        <input type="hidden" value="${result.prescriptionClassification!-1}" name="prescriptionClassification" id="prescriptionClassification">
                        <input type="button" class="dropdown-toggle select-btn"
                               data-toggle="dropdown"
                            <#if (cfDict)??>
                                <#list cfDict as cf>
                                    <#if "${result.prescriptionClassification!-1}"=="${cf.id?c}"> value="${cf.name!""}"</#if>
                                </#list>
                            </#if>
                               aria-haspopup="true"
                               aria-expanded="false">
                        <ul class="dropdown-menu select-single">
                            <li><a href="-1"></a></li>
                            <#if (cfDict)??>
                                <#list cfDict as cf>
                                    <li><a href="#{cf.id}">${cf.name}</a></li>
                                </#list>
                            </#if>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">登记处方</label>
                    <div class="dropdown">
                        <span class="caret"></span>
                        <input type="hidden" value="${result.prescriptionYn!""}" name="prescriptionYn">
                        <input type="button" class="dropdown-toggle select-btn"
                               data-toggle="dropdown"
                               value="<#if "${result.prescriptionYn!-1}"== "0">否</#if><#if "${result.prescriptionYn!-1}"== "1">是</#if>"
                               aria-haspopup="true"
                               aria-expanded="false">
                        <ul class="dropdown-menu select-single">
                            <li><a href="-1"></a></li>
                            <li><a href="0">否</a></li>
                            <li><a href="1">是</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon"><span class="danger">*</span>经营范围</label>
                    <input type="text" id="businessScope" class="form-control" value="${scopeName!''}" placeholder="">
                </div>
                <div hidden>
                    <input type="text" name="businessScope" id="businessScopeKey" class="form-control" value="${result.businessScope?c}" placeholder="" hidden>
                </div>
            </div>

            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">养护类型</label>

                    <div class="dropdown">
                        <span class="caret"></span>
                        <input type="hidden" value="${result.maintenanceType!1}" name="maintenanceType">
                        <input type="button" class="dropdown-toggle select-btn"
                               data-toggle="dropdown"
                            <#if (yhDict)??>
                                <#list yhDict as yh>
                                    <#if "${result.maintenanceType!1}"=="${yh.id?c}">value="${yh.name!""}" </#if>
                                </#list>
                            </#if>
                               aria-haspopup="true"
                               aria-expanded="false">
                        <ul class="dropdown-menu select-single">
                            <#if (yhDict)??>
                                <#list yhDict as yh>
                                    <li><a href="#{yh.id}">${yh.name}</a></li>
                                </#list>
                            </#if>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">商品功能分类</label>
                    <div class="dropdown">
                        <span class="caret"></span>
                        <input type="hidden" value="${result.productFunctionCatagory!-1}" name="productFunctionCatagory">
                        <input type="button" class="dropdown-toggle select-btn"
                               data-toggle="dropdown"
                            <#if (ptypeDict)??>
                                <#list ptypeDict as fu>
                                    <#if "${result.productFunctionCatagory!-1}"=="${fu.id?c}">value="${fu.name!""}"</#if>
                                </#list>
                            </#if>
                               aria-haspopup="true"
                               aria-expanded="false">
                        <ul class="dropdown-menu select-single">
                            <li><a href="-1"></a></li>
                            <#if (ptypeDict)??>
                                <#list ptypeDict as fu>
                                    <li><a href="#{fu.id}">${fu.name}</a></li>
                                </#list>
                            </#if>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <button type="button" class="btn btn-redactbag" onclick="btn_alertFile()" id="number">暂无附件</button>
                    <input name="imgUrl" id="filepaths" value="${result.imgUrl!""}" type="hidden">
                </div>
            </div>
            <div class="col-xs-12" style="padding: 0;">
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>进项税率</label>
                        <input type="text" name="incomeTaxRate" value="${result.incomeTaxRate?c}" class="form-control" required="required" data-msg-required='该字段为必填项'>
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>销项税率</label>
                        <input type="text" name="ouputTaxRate" value="${result.ouputTaxRate?c}" class="form-control" required="required" data-msg-required='该字段为必填项'>
                    </div>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon"><span class="danger">*</span>零售价</label>
                    <input type="text" name="retailPrice" value="${result.retailPrice?c}" class="form-control" required="required" data-msg-required='该字段为必填项'>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">会员价</label>
                    <input type="text" name="vipPrice" value="${result.vipPrice?c}" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">积分商品</label>
                    <div class="dropdown">
                        <span class="caret"></span>
                        <input type="hidden" value="${result.scoreProductYn!""}" name="scoreProductYn">
                        <input type="button" class="dropdown-toggle select-btn"
                               data-toggle="dropdown"
                               value="<#if "${result.scoreProductYn!-1}"== "0">否</#if><#if "${result.scoreProductYn!-1}"== "1">是</#if>"
                               aria-haspopup="true"
                               aria-expanded="false">
                        <ul class="dropdown-menu select-single">
                            <li><a href="-1"></a></li>
                            <li><a href="0">否</a></li>
                            <li><a href="1">是</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">积分倍数</label>
                    <input type="text" name="scoreRate" value="<#if (result.scoreRate)??>${result.scoreRate?c}</#if>" class="form-control">
                </div>
            </div>

            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">特价商品</label>
                    <div class="dropdown">
                        <span class="caret"></span>
                        <input type="hidden" value="${result.special!-1}" name="special">
                        <input type="button" class="dropdown-toggle select-btn"
                               data-toggle="dropdown"
                               value="<#if "${result.special!-1}"== "0">否</#if><#if "${result.special!-1}"== "1">是</#if>"
                               aria-haspopup="true"
                               aria-expanded="false">
                        <ul class="dropdown-menu select-single">
                            <li><a href="-1"></a></li>
                            <li><a href="0">否</a></li>
                            <li><a href="1">是</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">库存上限</label>
                    <input type="text" name="storeMaxLimit" value="<#if result.storeMaxLimit??>${result.storeMaxLimit?c}</#if>" class="form-control">
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">库存下限</label>
                    <input type="text" name="storeMinLimit" value="<#if result.storeMaxLimit??>${result.storeMinLimit?c}</#if>" class="form-control">
                </div>
            </div>

            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">是否启用</label>
                    <div class="dropdown">
                        <span class="caret"></span>
                        <input type="hidden" value="${result.used!-1}" name="used">
                        <input type="button" class="dropdown-toggle select-btn"
                               data-toggle="dropdown"
                               value="<#if "${result.used!-1}"== "0">否</#if><#if "${result.used!-1}"== "1">是</#if>"
                               aria-haspopup="true"
                               aria-expanded="false">
                        <ul class="dropdown-menu select-single">
                            <li><a href="-1"></a></li>
                            <li><a href="0">否</a></li>
                            <li><a href="1">是</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-xs-3 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">分组</label>
                    <div class="dropdown">
                        <span class="caret"></span>
                        <input type="hidden" value="${result.isHidden!-1}" name="isHidden">
                        <input type="button" class="dropdown-toggle select-btn"
                               data-toggle="dropdown"
                               value="<#if "${result.isHidden!-1}"== "0">否</#if><#if "${result.isHidden!-1}"== "1">是</#if>"
                               aria-haspopup="true"
                               aria-expanded="false">
                        <ul class="dropdown-menu select-single">
                            <li><a href="-1"></a></li>
                            <li><a href="0">否</a></li>
                            <li><a href="1">是</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <#if (roleExt)?? && roleExt == 1>
        <div class="panel pxxtanel-efault publicPanel newPanel">
            <h4 class="publicPanelTitle">扩展属性</h4>
            <div class="commonValidate">
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">半年内最高进价</label>
                        <input disabled type="text" name="" <#if inventoryVo.highestCostPriceHalfYear == 0>value=""<#else >value="${inventoryVo.highestCostPriceHalfYear!''}"</#if> class="form-control">
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">半年内最低进价</label>
                        <input disabled type="text" class="form-control" <#if inventoryVo.minNumCostPriceHalfYear == 0>value=""<#else >value="${inventoryVo.minNumCostPriceHalfYear!''}"</#if>>
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">库存数量</label>
                        <input disabled type="text" <#if inventoryVo.stockAmount == 0>value=""<#else >value="${inventoryVo.stockAmount!''}"</#if> class="form-control">
                    </div>
                </div>

                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">历史最高进价</label>
                        <input disabled type="text" <#if inventoryVo.historyHighestCostPrice == 0>value=""<#else >value="${inventoryVo.historyHighestCostPrice!''}"</#if> class="form-control">
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">历史最低进价</label>
                        <input disabled type="text" <#if inventoryVo.historyMinNumCostPrice == 0>value=""<#else >value="${inventoryVo.historyMinNumCostPrice!''}"</#if> class="form-control">
                    </div>
                </div>

            <#--<div class="col-xs-3 marginTop5">-->
            <#--<div class="input-group">-->
            <#--<label class="input-group-addon">销售提成方案</label>-->
            <#--<input type="text" value="" class="form-control" readonly>-->
            <#--</div>-->
            <#--</div>-->
            </div>
        </div>
    </#if>


    <div class="panel panel-default publicPanel newPanel">
        <h4 class="publicPanelTitle">
            资质属性
            <label id="aptitude" class="xlose" style="float: right; text-align: left;border: none cursor: pointer;"><span><span id="openStatus">展开</span><i class="glyphicon glyphicon-chevron-down ctrlBtn"></i></span></label>
        </h4>
        <div class="commonValidate" id="aptitudeContainer" style="display: none;">
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">药品注册证 </label>
                    <input value= "${result.productExtMess.drugRegisCard!""}" type="text" name="productExtMess.drugRegisCard" maxlength="30"
                           <#if ((result.productExtMess.drcvExpireYn)?? && result.productExtMess.drcvExpireYn==1)>class="form-control red"<#else>class="form-control"</#if>>
                </div>
            </div>
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">药品注册证效期  </label>
                    <input value= "${result.productExtMess.drugRegisCardValidity!""}" name="productExtMess.drugRegisCardValidity"
                           <#if ((result.productExtMess.drcvExpireYn)?? && result.productExtMess.drcvExpireYn==1)>class="form-control Wdate red"<#else>class="form-control Wdate"</#if> onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})"" >
                </div>
            </div>

            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">GMP证书 </label>
                    <input value= "${result.productExtMess.GMPCertificate!""}" name="productExtMess.GMPCertificate" type="text" maxlength="30"
                           <#if (result.productExtMess.gmpcvExpireYn)?? && result.productExtMess.gmpcvExpireYn==1>class="form-control red"<#else>class="form-control"</#if>>
                </div>
            </div>
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">GMP证书效期  </label>
                    <input value= "${result.productExtMess.GMPCertificateValidity!""}" name="productExtMess.GMPCertificateValidity"
                           <#if (result.productExtMess.gmpcvExpireYn)?? && result.productExtMess.gmpcvExpireYn==1>class="form-control Wdate red"<#else>class="form-control Wdate"</#if> onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})" >
                </div>
            </div>

            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">药品再注册证  </label>
                    <input value= "${result.productExtMess.drugRegisterCardOnce!""}" name="productExtMess.drugRegisterCardOnce" type="text" maxlength="30"
                           <#if (result.productExtMess.drcovExpireYn)?? && result.productExtMess.drcovExpireYn==1>class="form-control red"<#else>class="form-control"</#if>>
                </div>
            </div>
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon"> 药品再注册证效期 </label>
                    <input value= "${result.productExtMess.drugRegisterCardOnceValidity!""}" name="productExtMess.drugRegisterCardOnceValidity"
                           <#if (result.productExtMess.drcovExpireYn)?? && result.productExtMess.drcovExpireYn==1>class="form-control Wdate red"<#else>class="form-control Wdate"</#if> onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})"" >
                </div>
            </div>

            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">药品补充申请批件  </label>
                    <input value= "${result.productExtMess.drugReplenishInstructions!""}" name="productExtMess.drugReplenishInstructions" type="text" maxlength="30"
                           <#if (result.productExtMess.drivExpireYn)?? && result.productExtMess.drivExpireYn==1>class="form-control red"<#else>class="form-control"</#if>>
                </div>
            </div>
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">药品补充申请批件效期 </label>
                    <input value= "${result.productExtMess.drugReplenishInstructionsValidity!""}" name="productExtMess.drugReplenishInstructionsValidity"
                           <#if (result.productExtMess.drivExpireYn)?? && result.productExtMess.drivExpireYn==1>class="form-control Wdate red"<#else>class="form-control Wdate"</#if> onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})" >
                </div>
            </div>

            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">医药产品注册证 </label>
                    <input value= "${result.productExtMess.medicineProRegisCard!""}" name="productExtMess.medicineProRegisCard" type="text" maxlength="30"
                           <#if (result.productExtMess.mprcvExpireYn)?? && result.productExtMess.mprcvExpireYn==1>class="form-control red"<#else>class="form-control"</#if>>
                </div>
            </div>
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">医药产品注册证效期  </label>
                    <input value= "${result.productExtMess.medicineProRegisCardValidity!""}" name="productExtMess.medicineProRegisCardValidity"
                           <#if (result.productExtMess.mprcvExpireYn)?? && result.productExtMess.mprcvExpireYn==1>class="form-control Wdate red"<#else>class="form-control Wdate"</#if> onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})" >
                </div>
            </div>

            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">进口药品注册证 </label>
                    <input value= "${result.productExtMess.importDrugRegisCard!""}" name="productExtMess.importDrugRegisCard" type="text" maxlength="30"
                           <#if (result.productExtMess.idrcvExpireYn)?? && result.productExtMess.idrcvExpireYn==1>class="form-control red"<#else>class="form-control"</#if>>
                </div>
            </div>
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">进口药品注册证效期  </label>
                    <input value= "${result.productExtMess.importDrugRegisCardValidity!""}" name="productExtMess.importDrugRegisCardValidity"
                           <#if (result.productExtMess.idrcvExpireYn)?? && result.productExtMess.idrcvExpireYn==1>class="form-control Wdate red"<#else>class="form-control Wdate"</#if> onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})" >
                </div>
            </div>

            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">进口药品再注册证  </label>
                    <input value= "${result.productExtMess.importDrugRegisCardOnce!""}" name="productExtMess.importDrugRegisCardOnce" type="text" maxlength="30"
                           <#if (result.productExtMess.idrcovExpireYn)?? && result.productExtMess.idrcovExpireYn==1>class="form-control red"<#else>class="form-control"</#if>>
                </div>
            </div>
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">进口药品再注册证效期 </label>
                    <input value= "${result.productExtMess.importDrugRegisCardOnceValidity!""}" name="productExtMess.importDrugRegisCardOnceValidity"
                           <#if (result.productExtMess.idrcovExpireYn)?? && result.productExtMess.idrcovExpireYn==1>class="form-control Wdate red"<#else>class="form-control Wdate"</#if> onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})" >
                </div>
            </div>

            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">进口药品补充申请批件  </label>
                    <input value= "${result.productExtMess.importDrugReplenishInstructions!""}" name="productExtMess.importDrugReplenishInstructions" type="text" maxlength="30"
                           <#if (result.productExtMess.idrivExpireYn)?? && result.productExtMess.idrivExpireYn==1>class="form-control red"<#else>class="form-control"</#if>>
                </div>
            </div>
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">进口药品补充申请批件效期 </label>
                    <input value= "${result.productExtMess.importDrugReplenishInstructionsValidity!""}" name="productExtMess.importDrugReplenishInstructionsValidity"
                           <#if (result.productExtMess.idrivExpireYn)?? && result.productExtMess.idrivExpireYn==1>class="form-control Wdate red"<#else>class="form-control Wdate"</#if> onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})" >
                </div>
            </div>

            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">第二类医疗器械经营备案凭证 </label>
                    <input value= "${result.productExtMess.secondMedicalEquipOperateCard!""}" name="productExtMess.secondMedicalEquipOperateCard" type="text" maxlength="30"
                           <#if (result.productExtMess.smeocvExpireYn)?? && result.productExtMess.smeocvExpireYn==1>class="form-control red"<#else>class="form-control"</#if>>
                </div>
            </div>
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">第二类医疗器械经营备案凭证效期  </label>
                    <input value= "${result.productExtMess.secondMedicalEquipOperateCardValidity!""}" name="productExtMess.secondMedicalEquipOperateCardValidity"
                           <#if (result.productExtMess.smeocvExpireYn)?? && result.productExtMess.smeocvExpireYn==1>class="form-control Wdate red"<#else>class="form-control Wdate"</#if> onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})" >
                </div>
            </div>

            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">医疗器械产品注册证 </label>
                    <input value= "${result.productExtMess.medicalEquipRegisCard!""}" name="productExtMess.medicalEquipRegisCard" type="text" maxlength="30"
                           <#if (result.productExtMess.mercvExpireYn)?? && result.productExtMess.mercvExpireYn==1>class="form-control red"<#else>class="form-control"</#if>>
                </div>
            </div>
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">医疗器械产品注册证效期  </label>
                    <input value= "${result.productExtMess.medicalEquipRegisCardValidity!""}" name="productExtMess.medicalEquipRegisCardValidity"
                           <#if (result.productExtMess.mercvExpireYn)?? && result.productExtMess.mercvExpireYn==1>class="form-control Wdate red"<#else>class="form-control Wdate"</#if> onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})" >
                </div>
            </div>

            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">医疗器械产品再次注册证 </label>
                    <input value= "${result.productExtMess.medicalEquipRegisCardOnce!""}" name="productExtMess.medicalEquipRegisCardOnce" type="text" maxlength="30"
                           <#if (result.productExtMess.mercovExpireYn)?? && result.productExtMess.mercovExpireYn==1>class="form-control red"<#else>class="form-control"</#if>>
                </div>
            </div>
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">医疗器械产品再次注册证效期  </label>
                    <input value= "${result.productExtMess.medicalEquipRegisCardOnceValidity!""}" name="productExtMess.medicalEquipRegisCardOnceValidity"
                           <#if (result.productExtMess.mercovExpireYn)?? && result.productExtMess.mercovExpireYn==1>class="form-control Wdate red"<#else>class="form-control Wdate"</#if> onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})" >
                </div>
            </div>

            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">保健食品许可证 </label>
                    <input value= "${result.productExtMess.healthFoodLicence!""}" name="productExtMess.healthFoodLicence" type="text" maxlength="30"
                           <#if (result.productExtMess.hflvExpireYn)?? && result.productExtMess.hflvExpireYn==1>class="form-control red"<#else>class="form-control"</#if>>
                </div>
            </div>
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">保健食品许可证效期  </label>
                    <input value= "${result.productExtMess.healthFoodLicenceValidity!""}" name="productExtMess.healthFoodLicenceValidity"
                           <#if (result.productExtMess.hflvExpireYn)?? && result.productExtMess.hflvExpireYn==1>class="form-control Wdate red"<#else>class="form-control Wdate"</#if> onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})" >
                </div>
            </div>

            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">消毒产品生产企业卫生许可证 </label>
                    <input value= "${result.productExtMess.disinfectProFirmSanitationCard!""}" name="productExtMess.disinfectProFirmSanitationCard" type="text" maxlength="30"
                           <#if (result.productExtMess.dpfscvExpireYn)?? && result.productExtMess.dpfscvExpireYn==1>class="form-control red"<#else>class="form-control"</#if>>
                </div>
            </div>
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">消毒产品生产企业卫生许可证效期  </label>
                    <input value= "${result.productExtMess.disinfectProFirmSanitationCardValidity!""}" name="productExtMess.disinfectProFirmSanitationCardValidity"
                           <#if (result.productExtMess.dpfscvExpireYn)?? && result.productExtMess.dpfscvExpireYn==1>class="form-control Wdate red"<#else>class="form-control Wdate"</#if> onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})" >
                </div>
            </div>

            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">国产特殊用途化妆品卫生许可批件 </label>
                    <input value= "${result.productExtMess.specialCosmeticsSanitationLicence!""}" name="productExtMess.specialCosmeticsSanitationLicence" type="text" maxlength="30"
                           <#if (result.productExtMess.scslvExpireYn)?? && result.productExtMess.scslvExpireYn==1>class="form-control red"<#else>class="form-control"</#if>>
                </div>
            </div>
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">国产特殊用途化妆品卫生许可批件效期  </label>
                    <input value= "${result.productExtMess.specialCosmeticsSanitationLicenceValidity!""}" name="productExtMess.specialCosmeticsSanitationLicenceValidity"
                           <#if (result.productExtMess.scslvExpireYn)?? && result.productExtMess.scslvExpireYn==1>class="form-control Wdate red"<#else>class="form-control Wdate"</#if> onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})" >
                </div>
            </div>

            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">国产非特殊化妆品备案登记凭证  </label>
                    <input value= "${result.productExtMess.cosmeticsRegisCard!""}" name="productExtMess.cosmeticsRegisCard" type="text" maxlength="30"
                           <#if (result.productExtMess.crcvExpireYn)?? && result.productExtMess.crcvExpireYn==1>class="form-control red"<#else>class="form-control"</#if>>
                </div>
            </div>
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">国产非特殊化妆品备案登记凭证效期 </label>
                    <input value= "${result.productExtMess.cosmeticsRegisCardValidity!""}" name="productExtMess.cosmeticsRegisCardValidity"
                           <#if (result.productExtMess.crcvExpireYn)?? && result.productExtMess.crcvExpireYn==1>class="form-control Wdate red"<#else>class="form-control Wdate"</#if> onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})" >
                </div>
            </div>

            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">进口特殊用途化妆品卫生许可批件 </label>
                    <input value= "${result.productExtMess.importSpecialCosmeticsSanitationLicence!""}" name="productExtMess.importSpecialCosmeticsSanitationLicence" type="text" maxlength="30"
                           <#if (result.productExtMess.iscslvExpireYn)?? && result.productExtMess.iscslvExpireYn==1>class="form-control red"<#else>class="form-control"</#if>>
                </div>
            </div>
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">进口特殊用途化妆品卫生许可批件效期  </label>
                    <input value= "${result.productExtMess.importSpecialCosmeticsSanitationLicenceValidity!""}" name="productExtMess.importSpecialCosmeticsSanitationLicenceValidity"
                           <#if (result.productExtMess.iscslvExpireYn)?? && result.productExtMess.iscslvExpireYn==1>class="form-control Wdate red"<#else>class="form-control Wdate"</#if> onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})" >
                </div>
            </div>

            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">进口非特殊用途化妆品备案登记凭证  </label>
                    <input value= "${result.productExtMess.importCosmeticsRegisCard!""}"  name="productExtMess.importCosmeticsRegisCard" type="text" maxlength="30"
                           <#if (result.productExtMess.icrcvExpireYn)?? && result.productExtMess.icrcvExpireYn==1>class="form-control red"<#else>class="form-control"</#if>>
                </div>
            </div>
            <div class="col-xs-6 marginTop5">
                <div class="input-group">
                    <label class="input-group-addon">进口非特殊用途化妆品备案登记凭证效期 </label>
                    <input value= "${result.productExtMess.importCosmeticsRegisCardValidity!""}" name="productExtMess.importCosmeticsRegisCardValidity"
                           <#if (result.productExtMess.icrcvExpireYn)?? && result.productExtMess.icrcvExpireYn==1>class="form-control Wdate red"<#else>class="form-control Wdate"</#if> onfocus="WdatePicker({minDate:'%y-%M-{%d+1}'})" >
                </div>
            </div>

        </div>
    </div>

</form>
<div hidden>
    <input type="text" id="unitDict" <#if (unitDict)??>value='${unitDict}'<#else >value=""</#if> hidden>
    <input type="text" id="agentDict" <#if (agentDict)??>value='${agentDict}'<#else >value=""</#if> hidden>
    <input type="text" id="scopeDict" <#if (scopeDict)??>value='${scopeDict}'<#else >value=""</#if> hidden>
</div>
</body>
<script src="/static/assets/js/JsonExportExcel.min.js"></script>
<script src="/static/js/basicinfo/addProduct.js?v=${jsVersion}"></script>
<script src="/static/assets/js/pinyin.js"></script>
<script type="text/javascript">
    $(function(){
        var temp =  "${result.imgUrl!""}";
        if('' == temp){
            return;
        }
        var array = temp.split(",");
        $("#number").text("导入"+array.length+"个附件");
    })
</script>
</html>