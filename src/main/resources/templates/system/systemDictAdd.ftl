<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta charset="utf-8" />
    <title>Dashboard - Ace Admin</title>

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
<#include "/common/top-common.ftl"/>
</head>
<body id="innerBody">
<!-- 工具栏 -->
<div id="dictionary_tools" class="toolbox newToolbox">
    <button onclick="javascript: window.location.href = '/system/dict/toList'" class="btn btn-return">  返回</button>
    <button id="formSubmit" class="btn btn-redactbag"><i class="glyphicon glyphicon-floppy-saved"></i>提交</button>
</div>
<div class="panel panel-default">
    <div class="container-fluid">
        <form class="commonValidate" action="save" method="post">
            <input type="hidden" name="id" value="${systemDict.id!""}">
            <input type="hidden" name="description" value="${systemDict.description!""}" id="description">
            <div class="row">
                <div class="col-sm-4 col-md-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>下拉框类型</label>
                        <select name="bussinessId" class="form-control" required="required" id="bussinessId">
                             <option value=""></option>
                              <#list bussinessTypeList as bussinessType>
                                  <option value="${bussinessType.bussinessId!?c}" <#if systemDict.bussinessId??&&bussinessType.bussinessId==systemDict.bussinessId> selected = "selected"</#if>>${bussinessType.description!}</option>
                              </#list>
                        </select>
                    </div>
                </div>
            </div>
            <#if systemDict.id??>
            <div class="row">
                <div class="col-sm-4 col-md-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>下拉框Key</label>
                        <input type="text" disabled="disabled" name="value" <#if systemDict.value??>value="${systemDict.value!?c}"</#if> class="form-control">
                    </div>
                </div>
            </div>
            </#if>
            <div class="row">
                <div class="col-sm-4 col-md-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>下拉框值</label>
                        <input type="text" up='up' name="name"  maxlength="45" placeholder=""  value="${systemDict.name!}" class="form-control" required="required">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-4 col-md-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>排序</label>
                        <input type="zzs" up='up' min="1" maxlength="9" name="sort" <#if systemDict.sort??>value="${systemDict.sort!?c}"</#if> class="form-control" required="required">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-4 col-md-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>状态</label>
                        <select name="yn" value="${systemDict.yn!}"class="form-control" required="required">
                            <option value=""></option>
                            <option value="1" <#if systemDict.yn??&&systemDict.yn==1> selected = "selected"</#if>>启用</option>
                            <option value="0" <#if systemDict.yn??&&systemDict.yn==0> selected = "selected"</#if>>禁用</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-4 col-md-3 marginTop5">
                </div>
            </div>
            <button class="hideSubmit" type="submit">提交</button>
        </form>

    </div>
</div>
</body>
<script src="/static/js/system/systemDictAdd.js?v=${jsVersion}"></script>
</html>