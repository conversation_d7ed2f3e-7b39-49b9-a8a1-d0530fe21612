<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>数据初始化</title>
    <#include "/common/top-common.ftl"/>
</head>
<body>
<style>
    html, body, .toolbox {
        height: 100%
    }

    .colorFF0000 {
        color: #FF0000;
    }

    .dataMenu .item {
        display: flex;
    }

    .dataMenu .item div {
        margin: 5px 30px 5px 0;
    }

    .dataMenu .allElection {
        margin: 15px 0;
    }

    .btn {
        width: 100px;
        height: 36px;
    }

    .btn:disabled, .btn:disabled:hover {
        background-color: #999999 !important;
        color: #ffffff !important;
        border: 0 !important;
        padding: 0 !important;
    }

    .btn-submit {
        margin-top: 10px
    }
    .toolbox{
        padding-left: 26px;
    }

</style>
<div class="toolbox">
    <h2 class="colorFF0000" style="padding: 0px;font-size: 20px;font-weight: 600">该操作不可逆，请谨慎操作！</h2>
    <span class="colorFF0000">注：数据初始化时请保证电脑处于联网状态，打开POS收银端并且不要做任何操作！</span>
    <h4 style="padding: 10px 0">药店名称：${drugstoreName}</h4>
    <div style="padding: 0 0 10px">请选择需要清空的数据：</div>
    <div class="dataMenu">
        <div class="item data">
            <div><input name="other" type="checkbox" value="productAndInventory">商品资料+商品结存</div>
            <div><input name="other" type="checkbox" value="inventory">商品结存</div>
            <div><input name="other" type="checkbox" value="providerAndDealAccount">供应商资料+供应商结存</div>
            <div><input name="other" type="checkbox" value="dealAccount">供应商结存</div>
        </div>
        <div class="item data">
            <div><input name="other" type="checkbox" value="member">会员信息</div>
            <div><input name="other" type="checkbox" value="purchaseHistory">历史采购记录</div>
            <div><input name="other" type="checkbox" value="orderHistory">历史销售记录</div>
        </div>
        <div class="item">
            <div><input type="checkbox" id="btn-allElection" name="all" value="all">清理全部数据资料（仅保留注册信息）</div>
        </div>
    </div>
    <p style="margin-top: 5px;">
        手机号：<span  id="mobile"></span>
        <i style="display:none" id="mobilehide">${phone}</i>
    </p>
    <div class="input-group">
        <input type="text" id="code" style="width:200px;">
        <button class="btn btn-redactbag" id="codeBtn" style="margin-left:10px;margin-top:-2px;">获取验证码</button>
    </div>
<div class="input-group" style="position: absolute;top: 10px;right: 5px;">
    <button class="btn btn-redactbag" id="btnSubmit" style="width:80px;">提交</button>
</div>
</div>
<script src="/static/js/system/dataInit.js?v=${jsVersion}"></script>


</body>
</html>
