<style>
    .tb-filter-statistic-shower {
        display: none;
    }

    .tb-filter-statistic-mask {
        opacity: 0.7;
        background: rgb(0, 0, 0);
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        user-select: none;
        z-index: 1024;
    }

    .tb-filter-statistic-wrapper {
        box-sizing: border-box;
        width: 540px;
        min-height: 400px;
        background-color: #fff;
        position: absolute;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        -moz-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
        -o-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        -webkit-border-radius: 6px;
        -moz-border-radius: 6px;
        border-radius: 6px;
        overflow: hidden;
        box-shadow: 0 0 8px rgba(0, 0, 0, 0.1), 0 0 256px rgba(255, 255, 255, .3);
        z-index: 1024;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    .tb-filter-statistic-wrapper .wrapper-body {
        box-sizing: border-box;
        width: 100%;
        margin-bottom: 55px;
        display: flex;
        justify-content: space-between;
    }

    .tb-filter-statistic-wrapper .wrapper-body .wrapper-body-item {
        box-sizing: border-box;
        width: 50%;
        border-right: 1px solid #CECECE;
    }

    .tb-filter-statistic-wrapper .wrapper-body .wrapper-body-item .item-title {
        box-sizing: border-box;
        width: 100%;
        height: 50px;
        line-height: 50px;
        text-align: center;
        background-color: #F6F6F6;
        border-bottom: 1px solid #CECECE;
        font-size: 15px;
        color: #333333;
    }

    .tb-filter-statistic-wrapper .wrapper-body .wrapper-body-item .item-content {
        box-sizing: border-box;
        width: 100%;
        min-height: 295px;
        max-height: 450px;
        padding: 10px 20px;
        overflow: auto;

    }

    .tb-filter-statistic-wrapper .wrapper-body .wrapper-body-item:last-child {
        border-right: 0;
    }

    .tb-filter-statistic-wrapper .wrapper-footer {
        box-sizing: border-box;
        width: 100%;
        height: 55px;
        border-top: 1px solid #CECECE;
        padding: 0 20px;
        position: absolute;
        bottom: 0;
        left: 0;
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }

    .tb-filter-statistic-wrapper .wrapper-footer .footer-btn-cancel {
        width: 92px;
        height: 36px;
        font-size: 15px;
        background:rgba(255,255,255,1);
        border-radius:4px;
        border:1px solid rgba(24,144,255,1);
        color: rgba(24,144,255,1);
        border-radius: 4px;
        margin-right: 16px;
    }

    .tb-filter-statistic-wrapper .wrapper-footer .footer-btn-ok {
        width: 92px;
        height: 36px;
        font-size: 15px;
        color: #FFFFFF;
        background:linear-gradient(360deg,rgba(24,144,255,1) 0%,rgba(20,172,255,1) 100%);
        border-radius:4px;
        border: none;
    }

    .tb-filter-statistic-wrapper .wrapper-footer button:focus {
        outline: 0;
    }

    .tb-filter-statistic-wrapper .wrapper-footer .footer-btn-cancel:active {
        outline: 0;
        -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
        box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    }

    .tb-filter-statistic-wrapper .wrapper-footer .footer-btn-ok:active {
        outline: 0;
        -webkit-box-shadow: inset 0 3px 5px rgba(24, 144, 255, 0.9);
        box-shadow: inset 0 3px 5px rgba(24, 144, 255, 0.9);
    }

    .tb-filter-statistic-wrapper .wrapper-footer .ckb-box {
        position: absolute;
        left: 20px;
    }

    .tb-filter-statistic-wrapper .wrapper-body .wrapper-body-item .item-content ul {
        padding: 0;
        margin: 0;
    }

    .tb-filter-statistic-wrapper .wrapper-body .wrapper-body-item .item-content ul li {
        margin: 5px 0;
    }

    .tb-filter-statistic-wrapper label.ckb-control {
        position: relative;
        padding-left: 25px;
        margin: 0;
        font-size: 15px;
        color: #666;
        cursor: pointer;
    }

    .tb-filter-statistic-wrapper label.ckb-control input[type='checkbox'] {
        cursor: pointer;
        position: absolute;
        top: 0;
        left: 0;
    }

    .tb-filter-statistic-wrapper label.ckb-control input[type='checkbox']:before {
        box-sizing: border-box;
        display: inline-block;
        position: absolute;
        top: -1px;
        left: 0;
        width: 16px;
        height: 16px;
        content: "";
        background-color: #fff;
        border: 1px solid #979797;
        border-radius: 2px;
        -webkit-transition: all .3s ease-in-out 0s;
        -o-transition: all .3s ease-in-out 0s;
        transition: all .3s ease-in-out 0s;
    }

    .tb-filter-statistic-wrapper label.ckb-control input[type='checkbox']:checked:before {
        background-color: #1890FF;
        border-color: #1890FF;
    }

    @font-face {
        font-family: "iconfont";
        src: url('../../static/assets/fonts/iconfont.eot?t=1529656685481');
        /* IE9*/
        src: url('../../static/assets/fonts/iconfont.ttf?t=1529656685481') format('truetype'),
            /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/ url('../../static/assets/fonts/iconfont.svg?t=1529656685481#iconfont') format('svg');
        /* iOS 4.1- */
    }

    .tb-filter-statistic-wrapper label.ckb-control input[type='checkbox']:checked:after {
        font-family: iconfont;
        content: "\e6f4";
        position: absolute;
        top: 1px;
        left: 0;
        display: inline-block;
        width: 16px;
        height: 16px;
        line-height: 16px;
        font-size: 12px;
        color: #fff;
        text-align: center;
    }

</style>
<div class="tb-filter-statistic-shower tb-filter-statistic-mask"></div>
<div class="tb-filter-statistic-shower tb-filter-statistic-wrapper">
    <form id="tb-filter-statistic-form">
        <div class="wrapper-body">
            <div class="wrapper-body-item">
                <div class="item-title">分类选项</div>
                <div class="item-content">
                    <ul id="tb-ckb-box-sortingOptions"></ul>
                </div>
            </div>
            <div class="wrapper-body-item">
                <div class="item-title">汇总选项</div>
                <div class="item-content">
                    <ul id="tb-ckb-box-collectOptions"></ul>
                </div>
            </div>
            <!--
            <div class="wrapper-body-item">
                <div class="item-title">平均值选项</div>
                <div class="item-content">
                    <ul>
                        <li>
                            <label class="ckb-control">
                                <input type="checkbox" name="averageOptions" value="stockAmount|库存数量">
                                库存数量
                            </label>
                        </li>
                        <li>
                            <label class="ckb-control">
                                <input type="checkbox" name="averageOptions" value="costUnitPrice|成本单价">
                                成本单价
                            </label>
                        </li>
                        <li>
                            <label class="ckb-control">
                                <input type="checkbox" name="averageOptions" value="retailPrice|零售价">
                                零售价
                            </label>
                        </li>
                        <li>
                            <label class="ckb-control">
                                <input type="checkbox" name="averageOptions" value="memberPrice|会员价">
                                会员价
                            </label>
                        </li>
                        <li>
                            <label class="ckb-control">
                                <input type="checkbox" name="averageOptions" value="stockSum|库存金额">
                                库存金额
                            </label>
                        </li>
                        <li>
                            <label class="ckb-control">
                                <input type="checkbox" name="averageOptions" value="taxPrice|最后含税进价">
                                最后含税进价
                            </label>
                        </li>
                    </ul>
                </div>
            </div>
            -->
        </div>
        <div class="wrapper-footer">
        <span class="ckb-box">
            <label class="ckb-control">
                <input type="checkbox" id="ckb_statistic_count" name="statisticalOptions"
                       value="totalAcount|统计条目数|number">
                统计条目数
            </label>
        </span>
            <button type="button" class="footer-btn-cancel" id="footer-btn-cancel">取消</button>
            <button type="button" class="footer-btn-ok" id="footer-btn-ok">确定</button>
        </div>
    </form>
</div>
<script>
    //汇总选项，配置JSON
    /*
    * 0:商品账页查询
    * 1:进销存报表
    * 2:销售商品明细
    * 3:零售流水对账
    * 4:收货明细查询
    * 5:销售结算明细
    * 6:近效期商品查询
    * 7:商品批号库存查询
    * 8:会员消费明细
    * 9:门店销售日报表
    * 10:销售员销售汇总查询
    * 11:收款方式对账单
    * 12:零售价查询
    * 13:商品库存查询
    * 14:零售交班
    * ...见报表汇总统计表
    * */
    var collectOptionsColumnConfig = {
        0: {
            statisticName: '商品账页查询',
            fieldsList: [
                {name: 'inNumber', txt: '入库数量'},
                {name: 'inTotalPrice', txt: '入库含税金额'},
                {name: 'outNumber', txt: '出库数量'},
                {name: 'outTotalPrice', txt: '出库含税金额'},
                {name: 'stockNumber', txt: '库存数量'},
                {name: 'lastCostPrice', txt: '含税成本金额'}
            ]
        },
        1: {
            statisticName: '进销存报表',
            fieldsList: []
        },
        2: {
            statisticName: '销售员销售汇总查询',
            fieldsList: [
                {name: 'costAmount', txt: '成本金额'},
                {name: 'actualAmount', txt: '实收金额'},
                {name: 'receivableAmount', txt: '商品原价'},
                {name: 'grossProfitAmount', txt: '毛利'},
                {name: 'memberActualAmount', txt: '会员销售额'}
            ]
        },
        3: {
            statisticName: '零售流水对账',
            fieldsList: [
                {name: 'receivableAmount', txt: '商品原价'},
                {name: 'actualAmount', txt: '实收金额'},
                {name: 'gatherAmount', txt: '收款合计'},
                {name: 'discountAmount', txt: '优惠金额'},
                {name: 'changeAmount', txt: '找零金额'},
                {name: 'cutAmount', txt: '抹零金额'}
            ]
        },
        4: {
            statisticName: '收货明细查询',
            fieldsList: [
                {name: 'productAmount', txt: '商品数量'},
                {name: 'productTaxPriceSum', txt: '金额'},
                {name: 'productDiscountTaxPriceSum', txt: '折后金额'},
                {name: 'productRejectionAmount', txt: '拒收数量'}
            ]
        },
        7: {
            statisticName: '商品批号库存查询',
            fieldsList: [
                {name: 'stockNumber', txt: '库存数量'},
                {name: 'stockAmount', txt: '库存金额'}
            ]
        },
        14: {
            statisticName: '零售交班',
            fieldsList: [
                {name: 'receivableAmount', txt: '商品原价'},
                {name: 'actualAmount', txt: '实收金额'},
                {name: 'discountAmount', txt: '优惠金额'},
                {name: 'gatherAmount', txt: '收款金额'},
                {name: 'changeAmount', txt: '找零金额'},
                {name: 'cutAmount', txt: '抹零金额'},
                {name: 'returnAmount', txt: '退货金额'},
                {name: 'costAmount', txt: '成本金额'},
                {name: 'grossProfitAmount', txt: '毛利'}
            ]
        },
    };
</script>
<script>
    //显示汇总弹框
    function tbFilterStatisticShown(tableId, collectOptionsObj, targetUrl, formId) {
        if (!(tableId && collectOptionsObj.fieldsList && collectOptionsObj.fieldsList.length)) {
            return false;
        }

        //分类选项字段
        //初始化table字段
        var colNames = $('#' + tableId).jqGrid('getGridParam', 'colNames');
        var colModel = $('#' + tableId).jqGrid('getGridParam', 'colModel');
        if (!(colModel && colModel.length)) {
            return false;
        }
        var sortingOptionsColArr = [];
        colModel.forEach((item, index) => {
            if ((item.hidden !== true)) {
                sortingOptionsColArr.push({
                    name: item.name,
                    txt: colNames[index],
                    inputType: item.inputType || 'text'
                })
            }
        });
        var sortingOptionsHtmlArr = [];
        if (sortingOptionsColArr.length) {
            sortingOptionsColArr.forEach((item) => {
                sortingOptionsHtmlArr.push('<li>');
                sortingOptionsHtmlArr.push('<label class="ckb-control">');
                sortingOptionsHtmlArr.push('<input type="checkbox" name="sortingOptions" value="' + item.name + '|' + item.txt + '|' + item.inputType + '">' + item.txt);
                sortingOptionsHtmlArr.push('</label>');
                sortingOptionsHtmlArr.push('</li>');
            });
        }
        $('#tb-ckb-box-sortingOptions').html().length ? '' : $('#tb-ckb-box-sortingOptions').html(sortingOptionsHtmlArr.join(''));

        //汇总选项字段
        var collectOptionsHtmlArr = [];
        var collectOptionsColArr = collectOptionsObj.fieldsList;
        if (collectOptionsColArr.length) {
            collectOptionsColArr.forEach((item) => {
                collectOptionsHtmlArr.push('<li>');
                collectOptionsHtmlArr.push('<label class="ckb-control">');
                collectOptionsHtmlArr.push('<input type="checkbox" name="collectOptions" value="' + item.name + '|' + item.txt + '|' + (item.inputType || 'text') + '">' + item.txt);
                collectOptionsHtmlArr.push('</label>');
                collectOptionsHtmlArr.push('</li>');
            });
        }
        $('#tb-ckb-box-collectOptions').html().length ? '' : $('#tb-ckb-box-collectOptions').html(collectOptionsHtmlArr.join(''));

        $("#footer-btn-ok").attr({
            "data-collect-options-obj": JSON.stringify(collectOptionsObj),
            "data-target-url": targetUrl,
            "data-search-form-id": formId || ''
        });

        $(".tb-filter-statistic-shower").show();
    }

    //取消
    $("#footer-btn-cancel").on('click', function () {
        $(".tb-filter-statistic-shower").hide();
    });

    //确定
    $("#footer-btn-ok").on('click', function () {

        //获取统计页面搜索条件
        var searchFormId = $(this).attr("data-search-form-id");
        var searchFormParamArrTmp = {};
        if (searchFormId) {
            var searchFormParamObj = $("#" + searchFormId).serializeArray();
            if (searchFormParamObj.length) {
                searchFormParamObj.forEach((item, index) => {
                    searchFormParamArrTmp[item.name] = item.value;
                })
            }
        }
        console.log(JSON.stringify(searchFormParamArrTmp));

        //获取选中的统计字段
        var paramObj = $('#tb-filter-statistic-form').serializeArray();
        if (!paramObj.length) {
            alertDialog_toast({
                title: '提示',
                content: '请选择分类选项或汇总选项'
            });
            return false;
        }
        var keyArrTmp = {};
        var keyStrTmp = [];
        paramObj.forEach((item, index) => {
            if (keyArrTmp.hasOwnProperty(item.name)) {
                keyArrTmp[item.name].push(item.value);
            } else {
                keyArrTmp[item.name] = [item.value];
            }
            keyStrTmp.push(item.value);
        });
        console.log(JSON.stringify(keyArrTmp));
        console.log(keyStrTmp.join(','));

        //汇总选项统计的字段
        var collectOptionsObj = JSON.parse($(this).attr("data-collect-options-obj"));

        //跳转url
        var targetUrl = $(this).attr("data-target-url");
        targetUrl += (targetUrl.indexOf('?') > -1 ? '&' : '?') + "exportJson=" + keyStrTmp.join(',') + "&typeFields=" + keyArrTmp["sortingOptions"] + "&collectFields=" + keyArrTmp["collectOptions"] + "," + keyArrTmp["statisticalOptions"];
        console.log(targetUrl);

        //页签id
        var hrefId = targetUrl.slice(0, targetUrl.indexOf('?')).replace(/\//g, function () {
            return '_';
        });

        $(".tb-filter-statistic-shower").hide();

        //源页面title
        var originPageTitle = parent.$.find("#nav-tab li.active a")[0].innerText;

        parent.openTabs(hrefId, '汇总统计-' + originPageTitle, encodeURI(targetUrl), {"reload": true});
    });
</script>
