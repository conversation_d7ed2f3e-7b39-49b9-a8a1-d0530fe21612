<!DOCTYPE html>
<html lang="en">

	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>会员等级</title>

		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
	</head>

	<body id="innerBody">
		<!-- 工具栏 -->
		<div id="dictionary_tools" class="toolbox newToolbox">
			<button onclick="javascript: window.location.href = '/member/level/toList'" type="button" class="btn btn-return">  返回</button>
			<button id="formSubmit" type="button" class="btn btn-redactbag" ps="true">提交</button>
            <button id="cancel" type="button" class="btn btn-return">重置</button>
		</div>
        <div class="panel panel-default publicPanel newPanel">
            <form class="commonValidate" action="save" method="post">
                <input type="hidden" name="id" id="id" value="${memberLevelPo.id!" "}">
                <div class="row">
                    <div class="col-sm-4 col-md-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon"><span class="danger">*</span>等级名称</label>
                            <input type="text" up='up' maxlength="20" name="name" id="name" <#if memberLevelPo.name??>value="${memberLevelPo.name!}"</#if> class="form-control" required="required" data-msg-required='该字段为必填项' maxlength="16">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-4 col-md-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon">上级等级</label>
                            <div class="dropdown">
                                <span class="caret"></span>
                                <input type="hidden" value="${memberLevelPo.upLevelId!""}" id="upLevelId" name="upLevelId">
                                <input type="button" class="dropdown-toggle select-btn"
                                       data-toggle="dropdown"
                                         <#list memberLevelList as memberLevel>
											 <#if memberLevelPo.upLevelId?? && memberLevel.id == memberLevelPo.upLevelId>
                                          value="${memberLevel.name}"</#if>
										 </#list>
                                       aria-haspopup="true"
                                       aria-expanded="false">
                                <ul class="dropdown-menu select-single">
                                    <li><a href=""></a></li>
                                <#list memberLevelList as memberLevel>
                                    <li><a href="${memberLevel.id?c}">${memberLevel.name}</a></li>
								</#list>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-4 col-md-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon"><span class="danger">*</span>升级积分</label>
                            <input type="text" name="upPoint" maxlength="6" id="upPoint" <#if memberLevelPo.upPoint??> value="${memberLevelPo.upPoint!?c}" </#if> class="form-control" required="required"
                                   pattern="^[1-9](\d+)?(\.\d{1,2})?$)|(^0$)|(^\d\.\d{1,2}$" data-msg-pattern= "请输入正确的积分数量" maxlength="12">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-4 col-md-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon"><span class="danger">*</span>折扣率</label>
                            <input type="text" up='up' name="discount" id="discount" <#if memberLevelPo.discount??>value="${memberLevelPo.discount!?c}"</#if> class="form-control" required="required"
                                   pattern="^100$|^(\d|[1-9]\d)(\.\d+)*$" data-msg-pattern= "0-100的数字">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-4 col-md-3 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon"><span class="danger">*</span>价格策略</label>
                            <select name="priceStrategy" value="${memberLevelPo.priceStrategy!" "}" class="form-control" required="required" id="priceStrategy">
                                <option value=""></option>
									<#list systemDictList as systemDict>
										<option value="${systemDict.id}" <#if memberLevelPo.priceStrategy??&&systemDict.id==memberLevelPo.priceStrategy> selected = "selected"</#if>>${systemDict.name}</option>
									</#list>
                            </select>
                        </div>
                    </div>
                    <div class="col-sm-4 col-md-3 marginTop5" id="memberPrice" style="display: none">
                        <div class="input-group" style="line-height: 36px">
                            <input type="checkbox" name="isSpecial"  value="${memberLevelPo.isSpecial!0}" <#if memberLevelPo.isSpecial?? && memberLevelPo.isSpecial == 1> checked</#if>>
                            特价商品使用会员价
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-4 col-md-3 marginTop5">
                    </div>
                </div>
                <button class="hideSubmit" type="submit">提交</button>
            </form>
		</div>
	</body>

    <script src="/static/js/member/memberLevelAdd.js?v=${jsVersion}"></script>

</html>