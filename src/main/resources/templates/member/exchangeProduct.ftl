<!DOCTYPE html>
<html lang="en">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>积分商品管理</title>
		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
        <style>
            #jqgh_grid-table_cb{display: none;}
        </style>
	</head>
	<body id="innerBody">
		<!-- 工具栏 -->
		<div id="toolbox" class="toolbox newToolbox">
			<a href="toAdd" id="addBtn" class="btn btn-addbag"> 新增</a>
            <button class="btn btn-removebag" id="btnDel"> 删除</button>
		</div>
		<div class="panel panel-default publicPanel newPanel">
			<form class="commonValidate"  id="searchMemberDataForm" style="overflow: hidden;" method="post">
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">商品名称</label>
						<input type="text" id="commonName" name="commonName" class="form-control form-inline">
					</div>
				</div>


                <div class="col-xs-3 marginTop5" >
                    <div class="input-group input-group-tail">
                        <label class="input-group-addon" >积分区间</label>
                        <div>
                            <input type="text" name="startPoint"  id="startPoint" class="form-control">
                        </div>
                        <span class="transit">-</span>
                        <div>
                            <input type="text" id="endPoint" name="endPoint" class="form-control">
                        </div>
					</div>
                </div>

                <div class="col-xs-6 marginTop5">
                    <div class="input-group">
                        <button id="queryBtn" type="button" class="btn btn-redactbag forbidRepeatSearch">  查询</button>
                        <button id="cancelBtn" type="button" class="btn btn-return">   重置</button>
                    </div>
                </div>

			</form>
		</div>
		<div class="panel panel-default publicPanel" id="b_table">
			<table id="grid-table"></table>
			<div id="grid-pager" style="margin-top: 20px !important;"></div>
		</div>
	</body>
    <script src="/static/js/member/exchangeProductList.js?v=${jsVersion}"></script>
</html>

