<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta charset="utf-8"/>
    <title>会员资料</title>
    <meta name="description" content="overview &amp; stats"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>
<#include "/common/top-common.ftl"/>
</head>
<style>
    html{
        overflow-y: auto!important
    }
</style>
<body id="innerBody">
<!-- 工具栏 -->
    <div id="dictionary_tools" class="toolbox newToolbox">
        <button onclick="javascript: window.location.href = '/member/data/toList'"  class="btn btn-return">返回</button>
        <button type="button" id="formSubmit" class="btn btn-redactbag">提交</button>
    </div>

    <#--  <form class="commonValidate common-reset" action="saveAndPrePay" method="post">  -->
    <form class="commonValidate common-reset" id="formInfo">

        <input type="hidden" name="id" value="${memberDataPo.id!""}" id="membetId">
        <input type="hidden" name="guid" value="${memberDataPo.guid!""}" id="guid">
        <div class="panel panel-default publicPanel newPanel">
            <h4 class="publicPanelTitle">基本设置</h4>
            <div class="row">
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>会员卡号</label>
                        <input type="text" up='up' name="cartNo" id="cartNo"
                               <#if memberDataPo.cartNo??>value="${memberDataPo.cartNo!}"</#if> class="form-control"
                               required="required" data-msg-required='该字段为必填项'>
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>会员级别</label>
                        <div class="dropdown" style="height: 36px;">
                            <span class="caret"></span>
                            <input type="hidden" value="${memberDataPo.vipLevelId!""}" id="vipLevelId" name="vipLevelId">
                            <input type="button" class="dropdown-toggle select-btn"
                                   data-toggle="dropdown"
                                    <#list memberLevelList as memberLevel>
                                        <#if memberDataPo.vipLevelId?? && memberLevel.id == memberDataPo.vipLevelId>
                                            value="${memberLevel.name}"</#if>
                                    </#list>
                                   aria-haspopup="true"
                                   aria-expanded="false">
                            <ul class="dropdown-menu select-single">
                                <li><a href=""></a></li>
                                <#list memberLevelList as memberLevel>
                                    <li><a href="${memberLevel.id?c}">${memberLevel.name}</a></li>
                                </#list>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>发卡人</label>
                        <div class="dropdown">
                            <span class="caret"></span>
                            <input type="hidden" value="${memberDataPo.createUser!""}" id="createUser" name="createUser">
                            <input type="button" class="dropdown-toggle select-btn"
                                   data-toggle="dropdown"
                                    <#list employeeList as employee>
                                        <#if memberDataPo.createUser?? && employee.employeeId?c == memberDataPo.createUser>
                                            value="${employee.name}"
                                        </#if>
                                    </#list>
                                   aria-haspopup="true"
                                   aria-expanded="false">
                            <ul class="dropdown-menu select-single">
                                <#list employeeList as employee>
                                    <li><a href="${employee.employeeId?c}">${employee.name}</a></li>
                                </#list>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">发卡时间</label>
                        <input type="text" up='up' id="sendCardTime" name="sendCardTime"
                               autocomplete="off"   <#if memberDataPo.sendCardTime??>value="${memberDataPo.sendCardTime!?string("yyyy-MM-dd")}"<#else> value="${.now?string("yyyy-MM-dd")}"</#if>
                               class="form-control Wdate" onfocus="WdatePicker()"
                               pattern="^\d{4}(\-|\/|\.)\d{1,2}\1\d{1,2}$" data-msg-pattern="日期格式不正确">
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">生效时间</label>
                        <input type="text" up='up' id="effectTime" name="effectTime"
                               autocomplete="off"   <#if memberDataPo.effectTime??>value="${memberDataPo.effectTime!?string("yyyy-MM-dd")}"<#else> value="${.now?string("yyyy-MM-dd")}"</#if>
                               class="form-control Wdate"  onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'expriedTime\');}'})" style="height: inherit;"
                               pattern="^\d{4}(\-|\/|\.)\d{1,2}\1\d{1,2}$" data-msg-pattern="日期格式不正确">
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">失效时间</label>
                        <input type="text" up='up' id="expriedTime" name="expriedTime" <#if memberDataPo.expriedTime??>value="${memberDataPo.expriedTime!?string("yyyy-MM-dd")}" <#else> value="${expireTime}"</#if> class="form-control Wdate"onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'effectTime\');}'})"
                               autocomplete="off"  pattern="^\d{4}(\-|\/|\.)\d{1,2}\1\d{1,2}$" data-msg-pattern= "日期格式不正确">
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>卡片状态</label>
                        <select name="state" value="${memberDataPo.state!""}" class="form-control" required="required">
                            <option value=""></option>
                            <option value="1" <#if memberDataPo.state?? && memberDataPo.state == 1>selected</#if>>启用</option>
                            <option value="0" <#if memberDataPo.state?? && memberDataPo.state == 0>selected</#if>>禁用</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel panel-default publicPanel newPanel">
            <h4 class="publicPanelTitle">会员信息</h4>
            <div class="row">
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>会员姓名</label>
                        <input type="text" up='up' id="name" name="name" <#if memberDataPo.name??>value="${memberDataPo.name!}"</#if>
                               class="form-control" required="required" data-msg-required='该字段为必填项' maxlength="16">
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>手机号码</label>
                        <input type="text" up='up' name="telephone" id="telephone"
                               <#if memberDataPo.telephone??>value="${memberDataPo.telephone!}"</#if> class="form-control"
                               required="required"
                               pattern="^\d{5,13}$" data-msg-pattern="请输入5-13位的数字" maxlength="13">
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>性别</label>
                        <select name="sex" id="sex" value="${memberDataPo.sex!""}" class="form-control" required="required">
                            <option value=""></option>
                            <#list systemDictList as systemDict>
                                <option value="${systemDict.id?c}"<#if memberDataPo.sex?? && systemDict.id == memberDataPo.sex>
                                    selected="selected"</#if>>${systemDict.name}</option>
                            </#list>
                        </select>
                    </div>
                </div>

                 <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">身份证号</label>
                        <input type="text" up='up' name="idCard" pattern="^[A-Za-z0-9]{0,18}$" data-msg-pattern="身份证号最长18位"
                               <#if memberDataPo.idCard??>value="${memberDataPo.idCard!}"</#if> class="form-control" maxlength="32">
                    </div>
                </div>

                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">会员生日</label>
                        <input type="text" up='up' id="birthday" name="birthday"
                               <#if memberDataPo.birthday??>value="${memberDataPo.birthday!?string("yyyy-MM-dd")}"</#if>
                               autocomplete="off"   class="form-control Wdate" onfocus="WdatePicker()"
                               pattern="^\d{4}(\-|\/|\.)\d{1,2}\1\d{1,2}$" data-msg-pattern="日期格式不正确">
                    </div>
                </div>

                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">会员年龄</label>
                        <input type="text" up='up' name="age" maxlength="3" pattern="^\d{0,3}$" data-msg-pattern="年龄不超过3位"  <#if memberDataPo.age?? && memberDataPo.age!=0>value="${memberDataPo.age!}"</#if> class="form-control" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}">
                    </div>
                </div>
               
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">微信绑定</label>
                        <input type="text" up='up' name="wechat"<#if memberDataPo.wechat??>value="${memberDataPo.wechat!}"</#if>
                               class="form-control" maxlength="16">
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">QQ绑定</label>
                        <input type="text" up='up' name="qq" <#if memberDataPo.qq??>value="${memberDataPo.qq!}"</#if>
                               class="form-control" maxlength="16">
                    </div>
                </div>

                <#--  <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">会员积分</label>
                        <input type="text" up='up' name="point" id="point"
                               <#if memberDataPo.point??>value="${memberDataPo.point!?c}"</#if> class="form-control"
                               pattern="^[1-9](\d+)?(\.\d{1,2})?$)|(^0$)|(^\d\.\d{1,2}$" data-msg-pattern="请输入正确的积分数量">
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">累计积分</label>
                        <input type="text" name="allPoint" id="allPoint"
                               <#if memberDataPo.allPoint??>value="${memberDataPo.allPoint!?c}"</#if> class="form-control"
                               disabled="disabled">
                    </div>
                </div>  -->
                <#--  <div class="col-xs-6 marginTop5" style="height: 36px;"></div>  -->

                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">详细地址</label>
                        <input type="text" up='up' name="address" maxlength="50" <#if memberDataPo.address??>value="${memberDataPo.address!}"</#if>
                               class="form-control">
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">备注信息</label>
                        <input type="text" up='up' name="remark" maxlength="40" <#if memberDataPo.remark??>value="${memberDataPo.remark!}"</#if>
                               class="form-control" autocomplete="new-password">
                    </div>
                </div>
            </div>

        </div>
        <div class="panel panel-default publicPanel newPanel">
            <h4 class="publicPanelTitle">储值信息</h4>
            <div class="row">
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">充值余额</label>
                        <input type="text" up='up' id="amount" name="amount" class="form-control" <#if memberDataPo.amount??>value="${memberDataPo.amount!?c}"</#if> oninput="value=value.replace(/[^0-9.]/g,'')" >
                    </div>
                </div>

                 <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">赠送金额</label>
                        <input type="text" up='up' id="bonus" name="bonus" class="form-control" <#if memberDataPo.bonus??>value="${memberDataPo.bonus!?c}"</#if> oninput="value=value.replace(/[^0-9.]/g,'')">
                    </div>
                </div>

                <#--  <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">会员积分</label>
                        <input type="text" up='up' id="memberPoint" name="memberPoint" class="form-control" oninput="value=value.replace(/[^0-9.]/g,'')">
                    </div>
                </div>
                
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">累计积分</label>
                        <input type="text" up='up' id="totalPoint" name="totalPoint" class="form-control" disabled>
                    </div>
                </div>  -->

                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">可用积分</label>
                        <input type="text" up='up' name="point" id="point" maxlength="8"
                               <#if memberDataPo.point??>value="${memberDataPo.point!?c}"</#if> class="form-control"
                               pattern="^[1-9](\d+)?(\.\d{1,2})?$)|(^0$)|(^\d\.\d{1,2}$" data-msg-pattern="请输入正确的积分数量">
                    </div>
                </div>
                <div class="col-xs-3 marginTop5">
                    <div class="input-group">
                        <label class="input-group-addon">累计积分</label>
                        <input type="text" name="allPoint" id="allPoint"
                               <#if memberDataPo.allPoint??>value="${memberDataPo.allPoint!?c}"</#if> class="form-control"
                               disabled="disabled">
                    </div>
                </div>

                <div class="col-xs-3 marginTop5" id="payTypeBox">
                    <div class="input-group">
                        <label class="input-group-addon">充值方式</label>
                        <select id="payType" name="payType" class="form-control">
                            <option value="1">现金</option>
                            <option value="2">微信</option>
                            <option value="3">支付宝</option>
                            <option value="4">银联</option>
                            <option value="9">其他</option>
                        </select>
                    </div>
                </div>
                
                <div class="col-xs-3 marginTop5 passwd" style="<#if memberDataPo.passwd??>display:none</#if>">
                    <div class="input-group">
                        <label class="input-group-addon"><span class="danger">*</span>消费密码</label>
                        <input type="<#if memberDataPo.passwd??>text<#else> password </#if>" pass='pass' id="passwd" name="passwd"  placeholder="设置6位数字" maxlength="6"
                                <#if memberDataPo.passwd??> value="${memberDataPo.passwd}" <#else> value="123456" </#if> class="form-control"
                               pattern="^[0-9]*$" data-msg-pattern="只为数字" autocomplete="new-password" required="required" data-msg-required='该字段为必填项'>
                    </div>
                </div>
                <div class="col-xs-3 marginTop5" style="padding-left: 42px;">
                    <div class="input-group">
                        <span style="margin-right: 10px;line-height: 36px;">初始密码为123456</span>
                        <input class="btn btn-redactbag"  type="button" id="resetPwd" value="重置密码"/>
                        <button class="hideSubmit" type="submit">提交</button>
                    </div>
                </div>
                <div class="col-xs-6 marginTop5"></div>
            </div>
        </div>
    </form>
</body>
<script src="/static/assets/js/md5.js"></script>
<script src="/static/js/member/memberDataAdd.js?v=${jsVersion}"></script>
</html>

