<!DOCTYPE html>
<html lang="en">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>会员积分变动</title>

		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
	</head>
	<body id="innerBody">
		<div class="panel panel-default publicPanel newPanel">
			<form class="row form-inline">
				<div class="col-sm-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">会员姓名</label>
						<input type="text" id="name" class="form-control">
					</div>
				</div>
				
				<div class="col-sm-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">手机号</label>
						<input type="text" id="phone_number" class="form-control">
					</div>
				</div>
				
				<div class="col-sm-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">日期</label>
						<input type="text" id="date" class="form-control Wdate" onfocus="WdatePicker()">
					</div>
				</div>
				
				<div class="col-sm-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">至</label>
						<input type="text" id="before_point" class="form-control">
					</div>
				</div>
				
				<div class="col-sm-3 marginTop5">
					<div class="input-group">
						<button id="search" type="button"  class="btn btn-redactbag" >  查询</button>
						<button id="cancel" type="button"  class="btn btn-return" >   重置</button>
						<button id="export" type="button"  class="btn btn-redactbag" >导出Excel</button>
                        <button  class="btn btn-redactbag"  id="filtrkolumn"">筛选列</button>
					</div>
				</div>
				
			</form>
		</div>
		<div class="panel panel-default publicPanel">
			<div class="dataTables_wrapper form-inline no-footer saasTableWrap">
                <table id="grid-table"></table>
                <div id="grid-pager" style="margin-top: 20px !important;"></div>
			</div>
		</div>

		
	</body>
    <script src="/static/js/member/memberPointVary.js?v=${jsVersion}"></script>

</html>

