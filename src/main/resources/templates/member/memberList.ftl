<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Dashboard - Ace Admin</title>
    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
</head>
<style type="text/css">
    .input-group-addon:first-child{min-width: inherit !important;}
</style>
<body style="background: #fff">
	<div class="panel panel-default publicPanel newPanel row">
			<div class="col-xs-4 marginTop5">
				<div class="input-group ">
					<label class="input-group-addon">会员姓名</label>
					<input type="text" lit="seach"  class="form-control" id="memberName" name="memberName">
				</div>
			</div>
            <div class="col-xs-4 marginTop5">
                <div class="input-group ">
                    <label class="input-group-addon">电话号码</label>
                    <input type="text" lit="seach"  class="form-control" id="telephone" name="telephone">
                </div>
            </div>

            <div class="col-xs-4 marginTop5">
                <div class="input-group ">
                    <label class="input-group-addon">会员卡号</label>
                    <input type="text"  lit="seach" id="cardNo"  name="cardNo" class="form-control">
                </div>
            </div>
            <div class="col-xs-12 marginTop5">
                <div class="input-group">
                    <button type="button" class="btn btn-redactbag" id="btn_search">查询</button>
                </div>
            </div>

	</div>
    <div class="row" style="margin: 0;" id="b_table">
        <table id="grid-table"></table>
        <div id="grid-pager" style=""></div>
    </div>
	<div class="footer-btns text-right btn_sub_can_div " style="">
        <button type="button" class="btn btn-redactbag ui-dialog-ok" id="btn_submit">确定</button>
        <button type="button" class="btn btn-return" id="btn_modalClose">取消</button>
	</div>
</body>
<script type="text/javascript" src="/static/js/member/memberList.js?v=${jsVersion}"></script>
</html>