<!DOCTYPE html>
<html lang="en">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>会员积分兑换</title>

		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
	</head>
	<#--<style type="text/css">
        #b_table #gview_grid-table{bottom: 0 !important;}
	</style>-->
	<body id="innerBody">
		<!-- 工具栏 -->
		<div id="toolbox" class="toolbox newToolbox">
			<button id="formSubmit" class="btn btn-redactbag"><i class="glyphicon glyphicon-floppy-saved"></i>提交</button>
		</div>
		<div class="panel panel-default publicPanel newPanel">
			<#--<h4 class="publicPanelTitle">积分兑换</h4>-->
			<form class="row form-inline  addSearch">
				<div class="col-sm-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">日期</label>
						<input type="text" id="date" value="${memberpointConvert.date!""}" class="form-control" readonly>
					</div>
				</div>
				
				<div class="col-sm-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><span class="danger">*</span>业务员</label>
						<input type="text" id="adminstrator"  name="salesman" value="${memberpointConvert.adminstrator!""}" class="form-control" readonly>
					</div>
				</div>
				
				<div class="col-sm-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><span class="danger">*</span>会员名称</label>
						<input type="text" id="memberName" name="memberName" class="form-control"   required>
						<input type="hidden" id="memberId" name="memberId" class="form-control" readonly>
						<input type="hidden" id="exchangePrice" value="0"/>
						<input type="hidden" id="exchangePoint" />
						<i class="glyphicon glyphicon-search"></i>
					</div>
				</div>
				
				<div class="col-sm-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><span class="danger">*</span>会员等级</label>
						<input type="text" id="member_level" class="form-control" readonly>
					</div>
				</div>
				
				<div class="col-sm-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon"><span class="danger">*</span>会员电话</label>
						<input type="text" id="memberPhone" name="memberPhone" class="form-control" readonly>
					</div>
				</div>
				
				<div class="col-sm-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">可用积分</label>
						<input type="text" id="point" value="${memberpointConvert.point!""}" class="form-control" readonly>
					</div>
				</div>

			</form>
		</div>
		<div class="panel panel-default publicPanel" id="b_table"><#--id="b_table"-->
			<div class="row">
				<div class="input-group box_zll">
					<button class="btn btn-primary btn-round"  id="addRow">新增行</button>
					<button class="btn btn-danger btn-round"  id="deleteRow">删除行</button>
				</div>
			</div>

			<table id="grid-table"></table>
			<#--<div id="grid-pager" style=""></div>-->
		</div>

	</body>
    <script src="/static/js/member/memberPointConvert.js?v=${jsVersion}"></script>


</html>

