<!DOCTYPE html>
<html lang="en">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>新增积分商品</title>
		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
	</head>
	<#--<style type="text/css">
        #b_table #gview_grid-table{bottom: 0 !important;}
	</style>-->
	<body id="innerBody">
		<!-- 工具栏 -->
		<div id="toolbox" class="toolbox newToolbox">
            <button onclick="javascript: window.location.href = '/exchangeProduct/toList'" class="btn btn-return">  返回</button>
            <button id="formSubmit" type="button" class="btn btn-redactbag">提交</button>
		</div>
		<div class="panel panel-default publicPanel" id="b_table">
			<div class="row">
				<div class="input-group box_zll">
					<button type="button" class="btn btn-redactbag"  id="addRow">新增行</button>
					<button type="button" class="btn btn-removebag"  id="deleteRow">删除行</button>
				</div>
			</div>
			<table id="grid-table"></table>
		</div>

	</body>
    <script src="/static/js/member/exchangeProductAdd.js?v=${jsVersion}"></script>


</html>

