<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta charset="utf-8"/>
    <title>Dashboard - Ace Admin</title>

    <meta name="description" content="overview &amp; stats"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>
<#include "/common/top-common.ftl"/>
</head>
<style type="text/css">
    html, body {
        height: 100%;
    }

    div, label, input, select, span, mark {
        font-size: 2rem !important;
    }

    #pos_date, div, label, input, select, span, mark {
        color: #000000 !important;
    }

    #pos_date {
        line-height: 23px !important;
    }

    .dfaa label, .dfaa div {
        font-size: 16px !important;
    }

    .ui-jqgrid tr.jqgrow td {
        font-size: 16px;
    }

    /*.ui-jqgrid-hdiv{overflow: scroll};*/
    /*.ui-jqgrid .ui-jqgrid-hdiv{}*/
    .removeScroll {
        overflow: auto !important;
    }

    /*.ui-jqgrid, .ui-jqgrid-view{height: 100%;}*/
    .ui-jqgrid {
        display: flex;
    }

    .ui-jqgrid-view {
        display: flex;
        flex-direction: column;
    }

    .ui-jqgrid-view .ui-corner-top {
        height: 40px;
    }

    .ui-jqgrid-bdiv {
        flex: 1
    }

    .tab_xuhao_style {
        text-aligin: center !important;
        width: 150px !important;
    }

    .body_div {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        width: 100%;
        height: 100%;
    }

    .timeout {
        color: #de4e4e !important
    }

    .soonTimeout {
        color: #e29830 !important
    }

    .bottomSum {
        color: red;
        font-weight: bold;
        font-size: 30px;
    }

    .inp_goodsInfoSearch {
        background: #A5D6A7;
        color: #555555;
        border: none !important;
    }

    .inp_goodsInfoSearch::-webkit-input-placeholder {
        color: #777777;
    }

    .inp_goodsInfoSearch:-moz-placeholder {
        color: #777777;
    }

    .inp_goodsInfoSearch::-moz-placeholder {
        color: #777777;
    }

    .inp_goodsInfoSearch:-ms-input-placeholder {
        color: #777777;
    }

    .form-control-feedback {
        pointer-events: stroke;
    }

    .dropdown-toggle {
        color: #555 !important;
        background-color: #fff !important;
    }

    .dropdown-toggle:hover {
        background-color: #EBEBEB !important;
    }

    .dropdown-toggle.focus, .dropdown-toggle:focus, .dropdown-toggle.focus, .dropdown-toggle:active:hover, .btn-default:focus:active, .dropdown-toggle:focus:hover, .dropdown-toggle:hover, .dropdown-toggle:active:hover, .dropdown-toggle:focus:active, .dropdown-toggle:focus:hover, .open > .btn-default.dropdown-toggle, .open > .btn-default.dropdown-toggle.focus, .open > .btn-default.dropdown-toggle:active, .open > .btn-default.dropdown-toggle:focus, .open > .btn-default.dropdown-toggle:hover, .open > .btn.dropdown-toggle, .open > .btn.dropdown-toggle.focus, .open > .btn.dropdown-toggle:active, .open > .btn.dropdown-toggle:focus, .open > .btn.dropdown-toggle:hover {
        background-color: #EBEBEB !important;
    }

    .btn-group > .btn > .caret {
        margin-top: auto;
    !important;
        border-top-color: #555;
        height: 6px;
        border-width: 6px;
    }

    .dropdown-menu button {
        width: 94%;
    }

    .dropdown-menu button:nth-child(1) {
        margin-bottom: 5px !important;
    }

    @media screen  and (max-width: 1300px) {
        .order-panel-body {
            min-width: 200px !important;
            padding: 0 10px 10px !important;
        }

        .orderSum {
            margin-top: 0 !important;
        }

        .input-group-addon:first-child {
            min-width: 5em !important;
        }

        #allDiscount {
            width: 80%;
        }

        .dfaa {
            background-color: #FEF3F0;
        }

        #pos_date {
            position: relative;
            top: -7px;
            color: #555 !important;
            line-height: 23px !important;
        }

        .bottomMony label, .bottomMony div {
            font-size: 24px !important;
        }

        div, label, input, select, span, mark {
            font-size: 1.4rem !important;
        }

        #pos_date, div, label, input, select, span, mark {
            color: #000 !important;
        }

        #pos_date {
            line-height: 23px !important;
            padding-right: 0px;
        }

        .dfaa label, .dfaa div {
            font-size: 16px !important;
        }

        .ui-jqgrid tr.jqgrow td {
            font-size: 16px;
        }

    }

    #toolbox .btn, #dictionary_tools button {
        font-size: 16px;
    }

    .btn-white.btn-default {
        border-color: #ABBAC3 !important;
        color: #555 !important;
        font-size: 16px;
    }
</style>
<body id="innerBody">
<div class="body_div">
    <div style="height: 222px;">
        <!-- 工具栏 -->
        <div id="toolbox" class="toolbox newToolbox">
            <button class="btn btn-white btn-default btn-round" onclick="btn_addMember()">新增会员(F1)</button>
            <button class="btn btn-white btn-default btn-round" id="upAccount" onclick="bnt_guazhang()">挂账(F5)</button>
            <button class="btn btn-white btn-default btn-round" onclick="btn_getGuzhang()">提取挂账(F6)</button>
            <button class="btn btn-white btn-default btn-round" onclick="btn_getChufang()">提取处方(F8)</button>
            <button class="btn btn-white btn-default btn-round" onclick="btn_tuihuo()">原单退货(F9)</button>
        <#--// -- 0626去除取消交易快捷键-->
            <button id="cancelTrBtn" class="btn btn-warning btn-round" onclick="cancelTrade()">取消交易</button>

            <button id="deleteProductBtn" class="btn  btn-warning btn-round" onclick="deleteProduct()">删除商品(Del)
            </button>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown"
                        aria-haspopup="true" aria-expanded="false">
                    更多
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <button class="btn btn-white btn-default btn-round" onclick="printLastOrder()">打印上一张小票</button>
                    <button class="btn btn-white btn-default btn-round" id="SSModel">切换到中药销售</button>

                </ul>
            </div>

            <div style="display: inline-block;background: #FFFFFF;
    border: 1px solid #D9D9D9;height: 30px;width: 30px;border-radius: 50%;text-align: center;line-height: 29px;top: 2px;position:relative"
                 id="ModelShow">常
            </div>

        </div>

        <div id="storageBlock" style="display:none;">
            <input type="hidden" id="memberId" name="memberId" class="form-control">
        <#--<input type="hidden" id="status" name="status" value="00" class="form-control">-->
            <input type="hidden" id="promotionId" name="promotionId" class="form-control">

            <input type="hidden" id="cashPay" name="cashPay" class="form-control">
            <input type="hidden" id="aliPay" name="aliPay" class="form-control">
            <input type="hidden" id="wechatPay" name="wechatPay" class="form-control">
            <input type="hidden" id="medicarePay" name="medicarePay" class="form-control">
            <input type="hidden" id="unionPay" name="unionPay" class="form-control">
            <input type="hidden" id="gatherAmount" name="gatherAmount" class="form-control">
            <input type="hidden" id="changeAmount" name="changeAmount" class="form-control">
            <input type="hidden" id="orderId" name="orderId" class="form-control">
            <input type="hidden" id="buyerName" name="buyerName" class="form-control">
            <input type="hidden" id="buyerIdcard" name="buyerIdcard" class="form-control">
            <input type="hidden" id="buyerMobile" name="buyerMobile" class="form-control">
            <input type="hidden" id="priceStrategy" name="priceStrategy" class="form-control"/>
            <input type="hidden" value="${saleIdObj}" id="saleIdObj" name="saleIdObj" class="form-control"/>
        </div>
        <div class="panel panel-default publicPanel" style="overflow: hidden; padding: 0;   background-color: #EBEDF1;">
            <form class="form-inline" id="posForm" style="overflow: hidden; display: flex;">
                <div class="panel-body " style="flex: 1;background-color: #FFF; padding-right: 0;margin-right: 20px;">
                    <div class="row">
                        <div class="col-xs-4">
                            <div class="input-group">
                                <label class="input-group-addon">日&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;期:</label>
                                <div class="form-control distime" id="pos_date"
                                     style="width: 120%;padding-left: 0;border: none;"></div>
                            </div>
                        </div>
                        <div class="col-xs-4">
                            <div class="input-group">
                                <label class="input-group-addon">销&nbsp;售&nbsp;员:</label>
                                <select id="saleId" name="saleId" class="form-control"></select>
                            </div>
                        </div>
                        <div class="col-xs-4">
                            <div class="input-group">
                                <label class="input-group-addon">整单扣率:</label>
                                <input id="allDiscount" name="allDiscount" type="number" maxlength="3" value="100"
                                       class="form-control" autocomplete="off">
                            </div>
                        </div>
                    </div>
                    <div class="row marginTop5">
                        <div class="col-xs-4">
                            <div class="input-group has-feedback">
                                <label class="input-group-addon">会员信息:</label>
                                <input id="memberName" name="memberName" type="text"
                                       class="form-control inp_memberSerch" onfocus="btn_enterEvent(this)"
                                       placeholder="" autocomplete="off">
                                <span class="glyphicon glyphicon-search form-control-feedback"></span>
                            </div>
                        </div>
                        <div class="col-xs-4">
                            <div class="input-group">
                            <#--<label class="input-group-addon">会员电话:</label>-->
                            <#--<input id="memberPhone" name="memberPhone" type="text" readonly class="form-control">-->
                                <span class="input-group-addon">会员电话:</span>
                                <mark class="form-control" id="memberPhone" name="memberPhone"
                                      style="border: none; padding-left: 0;"></mark>
                            </div>
                        </div>
                        <div class="col-xs-4">
                            <div class="input-group">
                            <#--<label class="input-group-addon">可用积分:</label>-->
                            <#--<input id="point" name="point" type="text" readonly class="form-control">-->
                                <span class="input-group-addon">可用积分:</span>
                                <mark class="form-control" id="point" name="point"
                                      style="border: none; padding-left: 0;"></mark>
                            </div>
                        </div>
                    </div>
                    <div class="row marginTop5">
                        <div class="col-xs-5">
                            <div class="input-group has-feedback" style="width:100%">
                                <label class="input-group-addon" style="width:5rem">商品信息:</label>
                                <input type="text" class="form-control inp_goodsInfoSearch" id="productName"
                                       name="name_goodinfo"
                                       onfocus="btn_enterEvent(this)" style="background: #A5D6A7; color: #555;"
                                       placeholder="双击、条码、助记码、商品编号、商品名称" autocomplete="off">
                                <span class="glyphicon glyphicon-search form-control-feedback"></span>
                            </div>
                        </div>
                        <div class="col-xs-3">
                            <div class="input-group" style="width:100%">
                                <label class="input-group-addon" style="width:2rem">数量:</label>
                                <div class="input-group" style="width:100%;max-width: 100px;">
                                    <input type="text" class="form-control" id="focusNumber" onkeyup="clearNoNum(this)" onafterpaste="clearNoNum(this)" autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-4">
                            <div class="input-group">
                                <label class="input-group-addon">修改数量、价格（F4)</label>
                                <div class="form-control" style="border: none;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="panel-body order-panel-body dfaa"
                     style="overflow: hidden;background-color: #fff;padding: 10px;min-width: 350px;">
                    <div class="row orderSum" style="background: #FEF3F0;margin-top: 20px;">
                        <div class="col-lg-6" style="line-height: 34px;">
                            <div class="form-group" style="    width: 100%;">
                                <label class="control-label" style=" color: #333333; font-weight: bolder;">上单实收:</label>
                                <div id="actualAmountOld" name="actualAmountOld" class="form-control "
                                     style="border: none;  color: #333333; font-weight: bolder;  background-color: transparent;"></div>
                            </div>
                        </div>
                        <div class="col-lg-6" style="line-height: 34px;">
                            <div class="form-group" style="    width: 100%;">
                                <label class="control-label" style=" color: #333333; font-weight: bolder;">上单应收:</label>
                                <div id="receivableAmountOld" name="receivableAmountOld" class="form-control "
                                     style="border: none; color: #333333; font-weight: bolder;   background-color: transparent;">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row marginTop5 orderSum"
                         style="background: #FEF3F0;color: #333333 !important;font-weight: bolder;">
                        <div class="col-lg-6" style="line-height: 34px;">
                            <div class="form-group" style="    width: 100%;">
                                <label class="control-label" style=" color: #333333; font-weight: bolder;">上单优惠:</label>
                                <div id="discountAmountOld" name="discountAmountOld" class="form-control "
                                     style="border: none;color: #333333; font-weight: bolder; background-color: transparent;"></div>
                            </div>
                        </div>
                        <div class="col-lg-6" style="line-height: 34px;">
                            <div class="form-group" style="    width: 100%;">
                                <label class="control-label" style=" color: #333333; font-weight: bolder;">上单找零:</label>
                                <div id="changeAmountOld" name="changeAmountOld" class="form-control "
                                     style="border: none;color: #333333; font-weight: bolder;    background-color: transparent;"></div>
                            </div>
                        </div>
                    </div>
            </form>
        </div>
    </div>
</div>

<div class="dataTables_wrapper form-inline no-footer saasTableWrap" style="overflow: auto; flex: 1; display: flex;">
    <table id="grid-table" class="pos_grid_table"></table>
</div>
<div class="panel panel-default publicPanel" style=" height: 56px;    width: 100%;">
    <!--position: absolute;bottom: 50px; width: 100%;-->
    <div class="row">
        <div class="col-sm-3">
            <div class="bottomMony">
                <label class="control-label" style="font-size:30px; !important">应收:</label>
                <div id="receivableAmountNew" name="receivableAmountNew" class="control-label bottomSum"
                     style="display: inline-block;">0.00
                </div>
            </div>
        </div>
        <div class="col-sm-3">
            <div class="bottomMony">
                <label class="control-label " style="font-size:30px; !important">实收:</label>
                <div id="actualAmountNew" name="actualAmountNew" class="control-label bottomSum"
                     style="display: inline-block;">0.00
                </div>
            </div>
        </div>
        <div class="col-sm-3">
            <div class="bottomMony">
                <label class="control-label " style="font-size:30px; !important">合计数量:</label>
                <div id="productNumNew" class="control-label bottomSum" style="    display: inline-block;">0</div>
            </div>
        </div>
        <div class="col-sm-3">
            <div class="bottomMony">
                <button class="btn btn-danger  col-sm-6 text-center" style="border-radius:5px" onclick="btn_payment()">
                    收款(F10)
                </button>
            </div>
        </div>
    </div>
</div>
<div class="loadingBlock" style="display:none;">
    <div class="la-ball-spin-clockwise la-2x">
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
    </div>
</div>
</body>

<script type="text/javascript">
    product_obj = "";
    batchNo_obj = "";
    var current_login_name = "";
    var w_width = $(window).width() * 0.8;
    var w_height = $(window).height() * 0.75;
    $.jgrid.col = {caption: "筛选列", bSubmit: "确定", bCancel: "取消"};
    var grid_selector = "#grid-table";
    var pager_selector = "#grid-pager";
    var unit_type_code = {};
    var unit_type_name = {};
    var prescription_id_type_code = {};
    var prescription_id_type_name = {};
    var sysConfig = {};
    function removeCache() {
        localStorage.removeItem('pos_cache');
    }
    function clearNoNum(obj){//保留两位小数
        obj.value = obj.value.replace(/[^(\-)\d.]/g,""); //清除“数字”和“.”以外的字符
        obj.value = obj.value.replace(/^\./g,""); //验证第一个字符是数字而不是.
        obj.value = obj.value.replace(/\.{2,}/g,"."); //只保留第一个. 清除多余的
        obj.value = obj.value.replace(".","$#$").replace(/\./g,"").replace("$#$",".");
        obj.value=obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');//只能输入两个小数
    }
    $(document).ready(function () {

        /*Number.prototype.toFixed=function(len)
        {
            var add = 0;
            var s,temp;
            var s1 = this + "";
            var start = s1.indexOf(".");
            if(s1.substr(start+len+1,1)>=5)add=1;
            var temp = Math.pow(10,len);
            s = Math.floor(this * temp) + add;
            return s/temp;
        }*/

        // toFixed兼容方法
        Number.prototype.toFixed = function (n) {
            if (n > 20 || n < 0) {
                throw new RangeError('toFixed() digits argument must be between 0 and 20');
            }
            const number = this;
            if (isNaN(number) || number >= Math.pow(10, 21)) {
                return number.toString();
            }
            if (typeof (n) == 'undefined' || n == 0) {
                return (Math.round(number)).toString();
            }

            let result = number.toString();
            const arr = result.split('.');

            // 整数的情况
            if (arr.length < 2) {
                result += '.';
                for (let i = 0; i < n; i += 1) {
                    result += '0';
                }
                return result;
            }

            const integer = arr[0];
            const decimal = arr[1];
            if (decimal.length == n) {
                return result;
            }
            if (decimal.length < n) {
                for (let i = 0; i < n - decimal.length; i += 1) {
                    result += '0';
                }
                return result;
            }
            result = integer + '.' + decimal.substr(0, n);
            const last = decimal.substr(n, 1);

            // 四舍五入，转换为整数再处理，避免浮点数精度的损失
            if (parseInt(last, 10) >= 5) {
                const x = Math.pow(10, n);
                result = (Math.round((parseFloat(result) * x)) + 1) / x;
                result = result.toFixed(n);
            }

            return result;
        };


        $.ajax({
            type: 'post',
            url: '/system/dict/getSystemDictByType',
            data: {bussinessId: 10004},
            success: function (res) {
                if (res) {
                    if (res.result) {
                        unit_type_code = res.result.reduce(function (result, curr) {
                            result[curr.value] = curr.name;
                            return result;
                        }, {});
                        unit_type_name = res.result.reduce(function (result, curr) {
                            result[curr.name] = curr.value;
                            return result;
                        }, {});
                    }
                }
            },
            error: function (err) {

            }
        })
        $.ajax({
            type: 'post',
            url: '/system/dict/getSystemDictByType',
            data: {bussinessId: 20013},
            success: function (res) {
                if (res) {
                    if (res.result) {
                        prescription_id_type_code = res.result.reduce(function (result, curr) {
                            result[curr.value] = curr.name;
                            return result;
                        }, {});
                        prescription_id_type_name = res.result.reduce(function (result, curr) {
                            result[curr.name] = curr.value;
                            return result;
                        }, {});
                    }
                }
            },
            error: function (err) {
            }
        })

        $.ajax({
            type: 'post',
            url: '/userrole/getCurrentLoginName',
            data: {},
            async: false,
            success: function (res) {
                if (res) {
                    if (res.result) {
                        current_login_name = res.result;
                    }
                }
            },
            error: function (err) {
            }
        })

        //加载控制面板
        $.ajax({
            type: 'post',
            url: '/sysconfig/query',
            data: {},
            async: false,
            success: function (res) {
                if (res) {
                    if (res.result) {
                        sysConfig = res.result;
                    }
                }
            },
            error: function (err) {
            }
        })
        var lastOrderSellerId = "";

//        if(sysConfig && sysConfig.savePreSaleYn == 1){
//            //加载控制面板
//            $.ajax({
//                type: 'post',
//                url: '/order/orderinfo/getLastOrderSellerId',
//                data: {},
//                async: false,
//                success: function (res) {
//                    if (res) {
//                        if (res.result) {
//                            lastOrderSellerId = res.result;
//                        }
//                    }
//                },
//                error: function (err) {
//                }
//            })
//        }

        $.ajax({
            type: 'post',
            url: '/userrole/getSaleUser',
            data: {},
            async: false,
            success: function (res) {
                if (res) {
                    if (res.result) {
                        if (res.result.length > 0) {
                            var str = "";
                            $(res.result).each(function (index, item) {
                                lastOrderSellerId = $("#saleIdObj").val();
                                if (lastOrderSellerId != '-1') {
                                    if (item.id == lastOrderSellerId) {
                                        str = str + "<option value='" + item.id + "'selected>" + item.realName + "</option>";
                                    } else {
                                        str = str + "<option value='" + item.id + "'>" + item.realName + "</option>";
                                    }
                                } else {
                                    if (item.username == current_login_name) {
                                        str = str + "<option value='" + item.id + "'selected>" + item.realName + "</option>";
                                    } else {
                                        str = str + "<option value='" + item.id + "'>" + item.realName + "</option>";
                                    }
                                }
                            });
                            $("#saleId").html(str);
                        }
                    }
                }
            },
            error: function (err) {
            }
        })
        /* 会员信息和其他输入框的关联*/
        $("#memberName").on("input", function () {
            //会员信息是不是清空
            var member_val = $("#memberName").val().trim();

            if (member_val == "") {
                $("#memberPhone").text('');//清空会员电话
                $("#point").text(''); //可用积分
                $('#storageBlock #priceStrategy').val('');
                $("#allDiscount").val(100).trigger('input'); //整单扣率还原100
                $('#storageBlock #memberId').val('');//清空会员ID
                //repExecute_table();

                //清空带入的会员信息
                $("#buyerName").val('');
                $("#buyerIdcard").val('');
                $("#buyerMobile").val('');

            }
            //根据是否会员切换显示字段
            if ($('#priceStrategy').val() == '2') {
                //==2  会员价
                $("#grid-table").setGridParam().hideCol("productRetailPrice").trigger("reloadGrid");//隐藏零售价
                $("#grid-table").setGridParam().showCol("productMemberPrice").trigger("reloadGrid");//展示会员价
            } else {
                // or 零售价
                $("#grid-table").setGridParam().showCol("productRetailPrice").trigger("reloadGrid");//展示零售价
                $("#grid-table").setGridParam().hideCol("productMemberPrice").trigger("reloadGrid");//隐藏会员价
            }

        });
        if (localStorage.getItem('pos_cache')) {
            var d = dialog({
                title: '提示',
                content: '是否加载上次缓存数据？',
                width: 200,
                height: 40,
                button: [
                    {
                        value: '确定',
                        callback: function () {
                            setCache();
                            removeCache();
                            setTimeout(function () {
                                repExecute_table();
                            }, 1)
                        },
                        autofocus: true
                    },
                    {
                        value: '取消',
                        callback: function () {
                            removeCache()
                        }
                    }
                ]
            }).showModal();
        }


        //$('#grid-table').jqGrid('getGridParam','colNames');

        var colNames = ["productId", "状态", "商品编号", "通用名", "商品名称", "规格", "单位", "生产厂家", "批号", "数量", "零售价", "会员价", "扣率", "成本价", "实际售价", "应收金额", "实际金额", "优惠金额", "产地", "处方分类", "处方id", "处方登记", "含特殊药品复方制剂", "批号库存", "总库存", "促销积分倍率", "脱单明细数量上限", "订单明细id", "到期时间", "是否真正参加促销", "提取处方数量"];
        var colModel = [
            {
                name: 'productId',
                index: 'productId',//索引。其和后台交互的参数为sidx
//                        key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                editable: true,
                edittype: "input",
                hidden: true,
                hidedlg: true,
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            }, {
                name: 'promotionType',
                index: 'promotionType',
                editable: true,//是否可编辑
                width: 50,
                edittype: "select",//可以编辑的类型。可选值：text, textarea, select, checkbox, password, button, image and file.s
                formatter: function (cellvalue) {
                    var item = '正常';
                    if (cellvalue == '0') {
                        item = '正常';
                    } else if (cellvalue == '1') {
                        item = "满折";
                    }
                    else if (cellvalue == '2') {
                        item = "满送";
                    }
                    else if (cellvalue == '3') {
                        item = "会员日";
                    }
                    else if (cellvalue == '4') {
                        item = "限量特价";
                    }
                    return item;
                },
                unformat: function (cellvalue, options, cell) {
                    var item = '';
                    if (cellvalue == '正常') {
                        item = 0;
                    } else if (cellvalue == "满折") {
                        item = 1;
                    } else if (cellvalue == "满送") {
                        item = 2;
                    } else if (cellvalue == "会员日") {
                        item = 3;
                    } else if (cellvalue == "限量特价") {
                        item = 4;
                    }
                    return item;
                }

            }, {
                name: 'productCode',
                index: 'productCode',
                sorttype: "double",
                width: 100,
                editable: true
            }, {
                name: 'commonName',
                index: 'commonName',
                editable: true,
                width: 110,
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            }, {
                name: 'productName',
                index: 'productName',
                editable: true,
                width: 110,
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            }, {
                name: 'productSpecifications',
                index: 'productSpecifications',
                width: 50,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'productUnit',
                index: 'productUnit',
                width: 50,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                },
                formatter: function (cellvalue) {
                    var str = '';
                    if (cellvalue != null && cellvalue != '' && cellvalue != -1) {
                        str = unit_type_code[cellvalue];
                    }
                    return str;
                },
                unformat: function (cellvalue, options, cell) {
                    var str = '';
                    if (cellvalue != null && cellvalue != '' && cellvalue != -1) {
                        str = unit_type_name[cellvalue];
                    }
                    return str;
                }
            }, {
                name: 'productManufacturer',
                index: 'productManufacturer',
                width: 150,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'batchNo',
                index: 'batchNo',
                width: 90,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'productNum',
                index: 'productNum',
                width: 50,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'productRetailPrice',
                index: 'productRetailPrice',
                width: 60,
                sortable: false,
                editable: true,
                edittype: "textarea"
            }, {
                name: 'productMemberPrice',
                index: 'productMemberPrice',
                width: 60,
                sortable: false,
                editable: true,
                hidden: true,
                edittype: "textarea"
            }, {
                name: 'discount',
                index: 'discount',
                width: 50,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10",
                }
            }, {
                name: 'costPrice',
                index: 'costPrice',
                width: 70,
                sortable: false,
                editable: true,
                hidden: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'actualUnitAmount',
                index: 'actualUnitAmount',
                width: 70,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'receivableAmount',
                index: 'receivableAmount',
                width: 70,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'actualAmount',
                index: 'actualAmount',
                width: 70,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'discountAmount',
                index: 'discountAmount',
                width: 70,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'productOriginAddress',
                index: 'productOriginAddress',
                width: 50,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'prescriptionClassification',
                index: 'prescriptionClassification',
                width: 70,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                },
                formatter: function (cellvalue) {
                    var str = '';
                    if (cellvalue != null && cellvalue != '' && cellvalue != -1) {
                        str = prescription_id_type_code[cellvalue];
                    }
                    return str;
                },
                unformat: function (cellvalue, options, cell) {
                    var str = '';
                    if (cellvalue != null && cellvalue != '' && cellvalue != -1) {
                        str = prescription_id_type_name[cellvalue];
                    }
                    return str;
                }
            }, {
                name: 'prescriptionId',
                index: 'prescriptionId',
                sortable: false,
                editable: true,
                edittype: "textarea",
                hidden: true,
                hidedlg: true,
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            }, {
                name: 'prescriptionYn',
                index: 'prescriptionYn',
                sortable: false,
                editable: true,
                width: 80,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                },
                formatter: function (cellvalue, options, rowObject) {
                    var str = '';
                    if (cellvalue) {
                        str = '是';
                    }
                    else {
                        str = '否';
                    }
                    return str;
                },
                unformat: function (cellvalue, options, cell) {
                    var item = '';
                    if (cellvalue == '否') {
                        item = 0;
                    } else if (cellvalue == "是") {
                        item = 1;
                    }
                    return item;
                }
            }, {
                name: 'bastYn',
                index: 'bastYn',
                width: 70,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                },
                formatter: function (cellvalue, options, rowObject) {
                    var str = '';
                    if (cellvalue) {
                        str = '是';
                    }
                    else {
                        str = '否';
                    }
                    return str;
                },
                unformat: function (cellvalue, options, cell) {
                    var item = '';
                    if (cellvalue == '否') {
                        item = 0;
                    } else if (cellvalue == "是") {
                        item = 1;
                    }
                    return item;
                }
            }, {
                name: 'stockNumber',
                index: 'stockNumber',
                width: 70,
                sortable: false,
                hidden: true,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            },
            {
                name: 'productStockNumber',
                index: 'productStockNumber',
                width: 70,
                sortable: false,
                editable: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            },
            {
                name: 'promotionIntegralMultiple',
                index: 'promotionIntegralMultiple',
                sortable: false,
                editable: true,
                hidden: true,
                edittype: "textarea",
                editoptions: {
                    rows: "2",
                    cols: "10"
                }
            },
            {
                name: 'RefundUpperlimit',
                index: 'RefundUpperlimit',
                sortable: false,
                editable: true,
                hidden: true,
                edittype: "text",
            },
            {
                name: 'orderItemId',
                index: 'orderItemId',
                sortable: false,
                editable: true,
                hidden: true
            },
            {
                name: 'expirationDate',
                index: 'expirationDate',
                sortable: false,
                editable: true,
                hidden: true,
                width: 70,
//                    hidden: true,
                formatter: function (cellvalue, v) {
                    $("#grid-table").find("tr[id=" + v.rowId + "]").removeClass('soonTimeout')//将要过期
                    $("#grid-table").find("tr[id=" + v.rowId + "]").removeClass('soonTimeout')//将要过期
                    if (formatDateym(new Date()) > cellvalue) {
                        setTimeout(function () {
                            $("#grid-table").find("tr[id=" + v.rowId + "]").addClass('timeout')//过期商品
                        }, 0)
                    } else {
                        if (DateDiff(formatDateym(new Date()), cellvalue) > -180) {
                            setTimeout(function () {
                                $("#grid-table").find("tr[id=" + v.rowId + "]").addClass('soonTimeout')//将要过期
                            }, 0)
                        }
                    }


                    var str = '';
                    if (cellvalue != null && cellvalue != '' && cellvalue != -1) {
                        str = formatDateym(cellvalue, false);
                    }
                    return str;
                }
            },
            {
                name: 'promotionFlag',
                index: 'promotionFlag',
                sortable: false,
                editable: true,
                hidden: true
            },
            {
                name: 'prescriptionNum',
                index: 'prescriptionNum',
                sortable: false,
                editable: true,
                hidden: true
            }];
        var localName = document.location.pathname + 'GridNames';
        var localModeol = document.location.pathname + 'GridModel';

        var colNames_local = localStorage.getItem(localName);
        var colNames_Model = localStorage.getItem(localModeol);

        if (colNames_local !== null && colNames_Model != null) {
            colNames = colNames_local.split(',');
            var bAry = [];
            colNames_Model = JSON.parse(colNames_Model);
            for (var i = 0; i < colNames_Model.length; i++) {
                for (var c = 0; c < colModel.length; c++) {
                    if (colModel[c].name == colNames_Model[i].name) {
                        bAry.push($.extend(colModel[c], colNames_Model[i]))
                    }
                }
            }
            colModel = bAry;
        }
        $(document).on("mouseup.jqGrid" + 'grid-table', function () {
            setTimeout(function () {
                var a = $('#grid-table').jqGrid('getGridParam', 'colNames');
                var b = $('#grid-table').jqGrid('getGridParam', 'colModel');
                var bAry = [];
                for (var i = 0; i < b.length; i++) {
                    bAry.push({name: b[i].name, width: b[i].width});
                }

                localStorage.setItem(localName, a.slice(1, a.length));
                localStorage.setItem(localModeol, JSON.stringify(bAry.slice(1, bAry.length)));
            }, 0)
        });

        var lastsel;
        $("#grid-table").jqGrid({
            sortable: true,
            //data: grid_data,//当 datatype 为"local" 时需填写
            datatype: "json", //数据来源，本地数据（local，json,jsonp,xml等）
            height: "auto",//高度，表格高度。可为数值、百分比或'auto'
            //mtype:"GET",//提交方式
            colNames: colNames,
            colModel: colModel,
            shrinkToFit: false,
            autoScroll: true,
            //shrinkToFit: true,
            viewrecords: true,//是否在浏览导航栏显示记录总数
            rowNum: 50,//每页显示记录数
            rowList: [10, 20, 30, 40, 50],//用于改变显示行数的下拉列表框的元素数组。
            pager: pager_selector,//分页、按钮所在的浏览导航栏
            altRows: true,//设置为交替行表格,默认为false
            rownumbers: true,
            //toppager: true,//是否在上面显示浏览导航栏
            multiselect: false,//是否多选
            //multikey: "ctrlKey",//是否只能用Ctrl按键多选
            multiboxonly: false,//是否只能点击复选框多选
            // subGrid : true,
            //sortname:'id',//默认的排序列名
            sortorder: 'asc',//默认的排序方式（asc升序，desc降序）
            //caption: "采购退货单列表",//表名
            autowidth: false,//自动宽

            /*
                        resizeStop: function () {
                            alert('1');
                        },
                        resizeStart: function () {
                            alert('1');
                        },
                        onSortCol: function () {
                            alert('1');
                        },
                        onHeaderClick: function () {
                            alert('1');
                        },*/


            ondblClickRow: function (id) {  //  双击行时触发  id:  当前行id
                open_priceNumberModify(); // 打开 修改价格，数量的modal  框

            },
            onSelectRow:function(id){
                if(id){//根据状态 判断输入框是否可用
                    var changeRowData = $("#grid-table").jqGrid("getRowData", id);
                    if(changeRowData.promotionType!="0" || window.orderStatus == 2){
                        $("#focusNumber").attr("disabled",true);
                    }else{
                        $("#focusNumber").attr("disabled",false);
                    }
                }

            },
            loadComplete: function () { // 当表格加载完成之后获取所有 行的 数量值 相加 ，赋值给 应收 、 实收 、 合计数量
                $(this).jqGrid("setCaption", "年毕业学生信息表");
                repExecute_table();
            }

        });
        //$("#grid-table").jqGrid('setLabel','rn', '序号', '');//tab_xuhao_style
        $("#grid-table").jqGrid('bindKeys', {
            "onEnter": function (rowid) {
                //alert("你enter了一行， id为:"+rowid)
            }
        });
    });
    var listenerkey = {
        upKey: function () {
            alert('shang  jian');
        }
    }

    //打印上一张小票
    function printLastOrder() {

        $.ajax({
            type: 'post',
            url: '/order/orderinfo/getprintLastOrderJSON',
            data: {},
            success: function (res) {
                if (res) {
                    if (res.result) {
                        //console.log(res.result);
                        $.ajax({
                            type: 'post',
                            url: '/print/bill/printBill',
                            data: {
                                jsonData: res.result
                            },
                            success: function (res) {

                            },
                            error: function (err) {

                            }
                        })
                    }
                }
            },
            error: function (err) {
            }
        })
    }

    //修改数量  价格 F4
    function btn_priceNumberModify() {
        //console.log('F4')
        var obj = $("#grid-table").jqGrid("getRowData");
        if (obj.length == 0) {//表格有数据时弹层
            btn_alertDialog('提示', '表格没有数据')
        } else {
            open_priceNumberModify()
        }
    }

    //打开 修改价格，数量的modal  框
    function open_priceNumberModify() {
        var id = jQuery("#grid-table").jqGrid('getGridParam', 'selrow'); // selrow	string	只读属性，最后选择行的id
        var changeProductInfo = $("#grid-table").jqGrid('getRowData', id);
        var bastYn_sum = 0;
        if (sysConfig.flaxNum) {
            $($("#grid-table").jqGrid('getRowData')).each(function (i, v) {
                if (v.bastYn == 1) {
                    bastYn_sum += Number(v.productNum);
                }
            })
        }
        /*将前台的商品选购数量 传到修改数量页面 start */
        var dataNumber=[];
        var gridTableID01=$("#grid-table").jqGrid('getDataIDs');
        $.each(gridTableID01,function(_i,_e){
            if(_e != id){ //当前正在编辑的行数据 不能传
                var rowDate =  $("#grid-table").jqGrid("getRowData",_e);
                dataNumber.push({"productId":rowDate.productId,"productNum":rowDate.productNum,"batchNo":rowDate.batchNo});
            }
        })

        /*将前台的商品选购数量 传到商品选择页面 end*/

        var changeNum = {
            "productStockNumber": changeProductInfo.stockNumber,
            "productId": changeProductInfo.productId,
            "num": changeProductInfo.productNum,
            "unitPirce": $('#priceStrategy').val() == '' || $('#priceStrategy').val() == '1' ? changeProductInfo.productRetailPrice : changeProductInfo.productMemberPrice,
            "discount": changeProductInfo.discount,
            "actualAmount": changeProductInfo.actualAmount,
            "actualUnitAmount":changeProductInfo.actualUnitAmount,
            "batchNo": changeProductInfo.batchNo,
            "updateProductDiscountYn": sysConfig.updateProductDiscountYn,
            "updateProductPrice": sysConfig.updateProductPrice,
            "promotionType": changeProductInfo.promotionType,
            "orderStatus": window.orderStatus,//1：收银模式2：退单模式==》只可修改数量
            "RefundUpperlimit": changeProductInfo.RefundUpperlimit,
            "prescriptionId": changeProductInfo.prescriptionId,
            "costPrice": changeProductInfo.costPrice,
            "prescriptionNum": changeProductInfo.prescriptionNum,
            "flaxNum": sysConfig.flaxNum,
            "bastYn": changeProductInfo.bastYn,
            "bastYn_sum": bastYn_sum - Number(changeProductInfo.productNum),
            "batchNoQueueYn":sysConfig.batchNoQueueYn //新增批号 先进先出配置状态
        };
        // 双击当前行，拿到当前行的ID 值。关闭modal  打开下一个modal .并将ID 值传递过去
        //var dialog = parent.dialog.get(window);
        dialog({
            url: '/order/orderinfo/toOrderChangeNumberView',
            title: '修改数量',
            width: 400,
            height: 300,
            data: {"changeNum": changeNum,"dataNumber": dataNumber}, // 给modal 要传递的 的数据  20180801新增了选购商品信息
            onclose: function () {
                if (this.returnValue) {
                    //console.log('修改数量 modal  返回的值  ：  ' + this.returnValue);
                    var change_info_obj = this.returnValue;
                    var usableBatchNo = change_info_obj.usableBatchNo;//可用库存信息
                    var curbatchNoNumber = 0;//当前批号库存
                    if(change_info_obj.usableBatchNo && change_info_obj.usableBatchNo.length){
                        $.each(change_info_obj.usableBatchNo,function (_index,_ele) {
                            if(_ele.lotNumber == change_info_obj.batchNo){
                                curbatchNoNumber = _ele.stockNumber;
                            }
                        })
                    }
                    change_info_obj.num = Number(change_info_obj.num).toFixed(2);//保留两位小数
                    //删除与当前行商品批号 一致的行
                    var _gridTableID=$("#grid-table").jqGrid('getDataIDs');
                    $.each(_gridTableID,function(_i,_e){
                        var rowDate =  $("#grid-table").jqGrid("getRowData",_e);
                        if(rowDate.productId == change_info_obj.productId && rowDate.batchNo == change_info_obj.batchNo && _e != $("#grid-table").jqGrid('getGridParam', 'selrow')){//商品 批号一致
                            change_info_obj.num += Number(rowDate.productNum);//商品 批号 一致 删掉一致行并将其数量加至总数量上面
                            $("#grid-table").jqGrid("delRowData",_e);

                        }
                    })
                    change_info_obj.num = isNaN(change_info_obj.num * 1) ?  0 : change_info_obj.num;
                    var id = jQuery("#grid-table").jqGrid('getGridParam', 'selrow');
                    if(sysConfig.batchNoQueueYn == 1 && curbatchNoNumber){//是否开启批号先进先出
                        $("#grid-table").jqGrid("setCell", id, "productNum", change_info_obj.num > curbatchNoNumber ? curbatchNoNumber : Number(parseFloat(change_info_obj.num)));
                    }else {
                        $("#grid-table").jqGrid("setCell", id, "productNum", Number(parseFloat(change_info_obj.num)));
                    }
                    if ($('#priceStrategy').val() == '' || $('#priceStrategy').val() == '1') {
                        $("#grid-table").jqGrid("setCell", id, "productRetailPrice", change_info_obj.unitPirce);
                    } else {
                        $("#grid-table").jqGrid("setCell", id, "productMemberPrice", change_info_obj.unitPirce);
                    }

                    $("#grid-table").jqGrid("setCell", id, "discount", change_info_obj.discount);
                    $("#grid-table").jqGrid("setCell", id, "actualAmount", change_info_obj.actualAmount);
                    $("#grid-table").jqGrid("setCell", id, "batchNo", change_info_obj.batchNo);

                    if (change_info_obj.expirationDate && change_info_obj.goodNumber) {
                        $("#grid-table").jqGrid("setCell", id, "expirationDate", change_info_obj.expirationDate);//过期时间
                        $("#grid-table").jqGrid("setCell", id, "stockNumber", change_info_obj.goodNumber);//批号库存
                    }


                    //如果数量大于当前批号数量
                    var gridTableID123=$("#grid-table").jqGrid('getDataIDs');//记录此时(123)的行
                    if(change_info_obj.num > curbatchNoNumber){
                        if(usableBatchNo && usableBatchNo.length){
                            var newNumber = (change_info_obj.num - curbatchNoNumber).toFixed(2);
                            $.each(usableBatchNo,function(_index,_ele){
                                if(_ele.lotNumber != change_info_obj.batchNo && newNumber > 0){
                                    var gridTableID=$("#grid-table").jqGrid('getDataIDs');//已经发生变化的行 以此添加行ID
                                    var endRowID = Number(gridTableID[gridTableID.length - 1]);
                                    var chongfuBatchNo = true;//是否已有批号
                                    $.each(gridTableID123,function(_i,_e){ //循环此时(123)的行
                                        var rowDate =  $("#grid-table").jqGrid("getRowData",_e);
                                        if(id != _e){//排除当前正在编辑的行
                                            if(rowDate.batchNo == _ele.lotNumber && rowDate.productId == changeNum.productId){
                                                chongfuBatchNo = false;
                                                if(_ele.stockNumber > rowDate.productNum && newNumber){
                                                    var this_number =  _ele.stockNumber > (Number(rowDate.productNum) + Number(newNumber)) ? (Number(rowDate.productNum) + Number(newNumber)) : _ele.stockNumber;
                                                    $("#grid-table").jqGrid("setCell", _e, "productNum", Number(parseFloat(this_number)));
                                                    newNumber -= Number(this_number-rowDate.productNum).toFixed(2);
                                                }
                                            }
                                        }
                                    })
                                    if(newNumber > 0 && chongfuBatchNo){
                                        var this_number = newNumber > _ele.stockNumber ? _ele.stockNumber : newNumber;
                                        var rowDate =  $("#grid-table").jqGrid("getRowData",id);//获取当前行数据
                                        $('#grid-table').jqGrid('addRowData', endRowID + 1, rowDate);
                                        $("#grid-table").jqGrid("setCell", endRowID + 1, "batchNo", _ele.lotNumber);
                                        $("#grid-table").jqGrid("setCell", endRowID + 1, "productNum", Number(parseFloat(this_number)));
                                        $("#grid-table").jqGrid("setCell", endRowID + 1, "stockNumber", _ele.stockNumber);
                                        newNumber -= this_number;
                                        newNumber.toFixed(2);
                                    }
                                }
                            })
                        }
                    }

                    setTimeout(function () {
                        repExecute_table(change_info_obj.discount);
                        $("#productName").focus();
                    })

                }
            }
        })
                .showModal();
        return false;
    }

    Math.formatFloat = function (f, digit) {
        var m = Math.pow(10, digit);
        return parseInt(f * m, 10) / m;
    }

    // 获取 表格的所有 信息   重复执行，操作  应收 、 实收、 合计数量‘
    function repExecute_table(oType) {

        var inp_discount_val = $('#allDiscount').val();// 全局扣率
        var allRowData = jQuery("#grid-table").jqGrid('getRowData');//表格数据
        var ids = $("#grid-table").jqGrid("getDataIDs");//表格所有ID
//总量归零
        var totalNum = 0,//总数量
                totalMoney = 0,//总应收
                factMoney = 0,//总实收
                discount_val = 0;//当前扣率


        $(allRowData).each(function (index, item) {
            var rowData = $("#grid-table").jqGrid("getRowData", item.id);//根据id获得本行的所有数据

            /********************* 计算取值  ********************/
                    //获取当前行的rowid
            var curRowId = ids[index];


            if (oType == 1 && item.promotionType == '0') {
                discount_val = Number(inp_discount_val)
            } else {
                discount_val = item.discount;
            }

            //discount_val = oType = 1 ? inp_discount_val : item.discount;


            //获取当前 商品 数量
            var curRowGoodNum = Number(item.productNum);

            //零售价---- 根据会员type变更
            var price = $('#priceStrategy').val() == '' || $('#priceStrategy').val() == '1' ? item.productRetailPrice : item.productMemberPrice;

            //实际售价	--零售价*扣率
            var actualUnitAmount = Number((Number(price) * toPercent(Number(discount_val))).toFixed(2));
            //var actualUnitAmount = Math.formatFloat(Number(price)/100 * Number(discount_val), 1);

            //应收金额	--零售价*数量
            var receivableAmount = (Number(price) * curRowGoodNum);

            //实际金额	--实际售价*数量
            var actualAmount = (actualUnitAmount * curRowGoodNum);

            /*********************  赋值   *******************/
            if (item.promotionType == '0' && window.orderStatus != 2) {
                //非退单模式-- 正常商品本地计算
                //实际售价
                $("#grid-table").jqGrid("setCell", curRowId, 'actualUnitAmount', actualUnitAmount.toFixed(2));
                //应收金额
                $("#grid-table").jqGrid("setCell", curRowId, 'receivableAmount', receivableAmount.toFixed(2));
                //实际金额
                $("#grid-table").jqGrid("setCell", curRowId, 'actualAmount', actualAmount.toFixed(2));
                //优惠金额
                $("#grid-table").jqGrid("setCell", curRowId, 'discountAmount', (receivableAmount - actualAmount).toFixed(2));
                //扣率
                $("#grid-table").jqGrid('setCell', curRowId, 'discount', discount_val);
            } else if (item.promotionType != '0' && (window.orderStatus == 1 || window.orderStatus == undefined)) {
                //非退单模式--  收银模式--带促销，直接按后台计算结果
                curRowGoodNum = item.productNum;
                receivableAmount = item.receivableAmount;
                actualAmount = item.actualAmount;
            } else if (window.orderStatus == 2) {
                curRowGoodNum = item.productNum;
                receivableAmount = item.receivableAmount; //item.receivableAmount;
                actualAmount = item.actualAmount; //item.actualAmount;
                $("#grid-table").jqGrid("setCell", curRowId, 'actualAmount', Number(actualAmount).toFixed(2));

            } else {
                $("#grid-table").jqGrid("setCell", curRowId, 'actualAmount', Number(actualAmount).toFixed(2));
                curRowGoodNum = item.productNum;
                receivableAmount = receivableAmount; //item.receivableAmount;
                actualAmount = actualAmount; //item.actualAmount;
            }


            totalNum += Number(Number(curRowGoodNum).toFixed(2));
            totalMoney += Number(Number(receivableAmount).toFixed(3));
            factMoney += Number(Number(actualAmount).toFixed(3));

            //获取某个子元素并设置它的属性title
            var cur = $("#" + ids[index] + ">td[aria-describedby='grid-table_actualUnitAmount']");


            if (sysConfig) {
                if (sysConfig.posShowPurpriceYn == 1) {//显示成本价
                    //cur[10].setAttribute('title', '成本价:' + item.costPrice);
                    //cur[14].setAttribute('title', '成本价:' + item.costPrice);
                    cur.attr('title', '成本价:' + item.costPrice);
                }
            }
        })

        $('#productNumNew').text(totalNum.toFixed(2));//20180912 数量支持小数 保留两位
        if (totalMoney < 0) {
            totalMoney = 0 - Math.abs(totalMoney).toFixed(2)
        }
            $('#receivableAmountNew').text(Number(totalMoney).toFixed(2));
        if (factMoney < 0) {
            factMoney = 0 - Math.abs(factMoney).toFixed(2)
        }
            $('#actualAmountNew').text(Number(factMoney).toFixed(2));

        var allRowIds = $("#grid-table").jqGrid('getDataIDs');
        $("#grid-table").setSelection(allRowIds[allRowIds.length - 1]);// 表格默认选中状态
        var ta = $('#grid-table>tbody>tr')[1];

    }

    $(document).keyup(function (e) {
        addLintenerKeyCode(e);
    });

    function addLintenerKeyCode(event) {
        switch (event.keyCode) {
            case 112:
                event.keyCode = 0;
                event.cancelBubble = true;
                event.preventDefault();
                if ($('.ui-dialog iframe').length) {
                    return false;
                }
                btn_addMember(); // F1 新增会员
                return false;
                break;
            case 115:
                event.keyCode = 0;
                event.cancelBubble = true;
                event.preventDefault();
                if ($('.ui-dialog iframe').length) {
                    return false;
                }
                btn_priceNumberModify(); //修改数量、价格  F4
                return false;
                break;
            case 116:
                event.keyCode = 0;
                event.cancelBubble = true;
                event.preventDefault();
                if ($('.ui-dialog iframe').length) {
                    return false;
                }
                bnt_guazhang(); // F5 挂账
                return false;
                break;
            case 117:
                event.keyCode = 0;
                event.cancelBubble = true;
                event.preventDefault();
                if ($('.ui-dialog iframe').length) {
                    return false;
                }
                btn_getGuzhang(); // F6提取挂账
                return false;
                break;
            case 119:
                event.keyCode = 0;
                event.cancelBubble = true;
                event.preventDefault();
                if ($('.ui-dialog iframe').length) {
                    return false;
                }
                btn_getChufang(); // F8 提取处方
                return false;
                break;
            case 120:
                event.keyCode = 0;
                event.cancelBubble = true;
                event.preventDefault();
                if ($('.ui-dialog iframe').length) {
                    return false;
                }
                btn_tuihuo(); // F9 原单退货
                return false;
                break;
                /*
                //0626,去除 取消交易ESC快捷键
                case 27:
                    event.keyCode = 0;
                    event.cancelBubble = true;
                    event.preventDefault();
                    cancelTrade(); // ESC 交易取消
                    return false;
                    break;*/
            case 46:
                event.keyCode = 0;
                event.cancelBubble = true;
                event.preventDefault();
                if ($(event.target).attr('id') == 'memberName') {
                    //如果在会员信息input内，则删除会员信息
                    $(event.target).val('').trigger('input');
                    $('#storageBlock #memberId').val('');//
                } else {
                    deleteProduct(); // Del  删除商品
                }

                return false;
                break;
            case 121:
                event.keyCode = 0;
                event.cancelBubble = true;
                event.preventDefault();
                if ($('.ui-dialog iframe').length) {
                    return false;
                }
                btn_payment(); // F10  收款
                return false;
                break;
        }
    }

    //切换模式1，收银模式 2，原单退货
    function switchMode(flag) {
        //console.log(flag);
        $('#storageBlock').find('input').val('');//清理隐藏域值

        if (flag == 2) {
            $('.form-inline input').prop('disabled', 'true');
            $('#upAccount').prop('disabled', 'true');
        } else {
            $('.form-inline input').removeAttr('disabled');
            $('#upAccount').removeAttr('disabled');
        }

    }

    //新增会员
    function btn_addMember() {

        //console.log('调用打开窗口方法');
        //console.log($('iframe'));

        $('iframe').remove();
        dialog({
            url: '/order/orderinfo/toOrderAddMemberView',
            title: '新增会员',
            width: w_width,
            height: 400,
            data: 'val值', // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    $('#pos_date').html("这里是modal 返回的值  " + this.returnValue);
                }

            }
        })
                .showModal();


        return false;
    }

    //提取挂账
    function btn_getGuzhang() {

        dialog({
            url: '/order/orderinfo/toOrderAccessAccountView',
            title: '提取挂账',
            width: w_width,
            height: w_height,
            data: 'val值', // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {

                    window.orderStatus = 1;//1:收银模式
                    switchMode(window.orderStatus);

                    $.ajax({
                        type: 'POST',
                        url: '/order/orderinfo/getOrderVoByTickNo',
                        data: {ticketNo: this.returnValue.ticketNo},
                        success: function (res) {
                            jQuery("#grid-table").jqGrid("clearGridData");
                            var orderInfoPo = res.result.orderInfoPo;
                            var orderDetailPos = res.result.orderDetailPos;
                            var memberDataPo = res.result.memberDataPo;
                            if (memberDataPo && memberDataPo.id) {
                                if (memberDataPo.expried_time && formatDateym(new Date()) > memberDataPo.expried_time) {
                                    btn_alertDialog('提示', '该会员已过期');
                                    $('#memberName').val('');
                                    $("#point").text('');
                                    $('#allDiscount').val(100);
                                    setTimeout(function () {
                                        $('#allDiscount').trigger('input');
                                    }, 10)

                                } else {
                                    $("#memberName").val(memberDataPo.name);
                                    $("#memberPhone").text(memberDataPo.phone_number);
                                    $("#point").text(memberDataPo.point);
                                    $("#memberId").val(memberDataPo.id);
                                    $('#allDiscount').val(memberDataPo.discount);

                                }
                            } else {
                                $("#memberName").val('');
                                $("#memberPhone").text('');
                                $("#point").text('');
                                $("#memberId").val('');
                                $('#allDiscount').val(100);
                            }

                            $('#priceStrategy').val(memberDataPo.priceStrategy);
                            if (memberDataPo && memberDataPo.priceStrategy == "2" && !memberDataPo.expried_time) {//==2 为展示会员价
                                $("#grid-table").setGridParam().hideCol("productRetailPrice").trigger("reloadGrid");//隐藏零售价
                                $("#grid-table").setGridParam().showCol("productMemberPrice").trigger("reloadGrid");//展示会员价
                            } else if (formatDateym(new Date()) < memberDataPo.expried_time) {
                                $("#grid-table").setGridParam().showCol("productRetailPrice").trigger("reloadGrid");//展示零售价
                                $("#grid-table").setGridParam().hideCol("productMemberPrice").trigger("reloadGrid");//隐藏会员价
                            }
                            //$("#memberName").val(memberDataPo.name || '');

                            $("#orderId").val(orderInfoPo.id);

                            if (orderInfoPo) {
                                $("#saleId").val(orderInfoPo.sellerId)
                            }
                            if (orderDetailPos) {
                                addRowForOrderDetailPos($("#grid-table"), orderDetailPos);
                            }
                        },
                        error: function (a, b, c) {
                            //console.log(c);
                        }
                    });
                }
            }
        })
                .showModal();
        return false;
    }

    //提取处方
    function btn_getChufang() {
        var prescriptionId_val = "";
        dialog({
            url: '/order/orderinfo/toOrderGetPrescriptionView',
            title: '提取处方',
            width: w_width,
            height: w_height,
            data: 'val值', // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue && this.returnValue.id) {

//                    window.orderStatus = 1;//1:收银模式
//                    switchMode(window.orderStatus);
                    //$('#memberPhone').text('');
                    //$('#pos_date').html("这里是modal 返回的值  " + this.returnValue);
                    if (this.returnValue) {
                        //console.log("start pre");
                        //console.log(this.returnValue);
                        prescriptionId_val = this.returnValue.id;
                        $.ajax({
                            type: 'POST',
                            url: '/order/orderinfo/getOrderVoByPrescriptionView',
                            data: {prescriptionId: this.returnValue.id},
                            success: function (res) {
                                jQuery("#grid-table").jqGrid("clearGridData");
                                // console.log(res);
                                $(res.result).each(function (index, item) {
                                    batchNo_obj = item.inventoryLotNumberPo;
                                    product_obj = item.productBaseinfoVo;
                                    product_obj.prescription_id = prescriptionId_val;
                                    addRow($("#grid-table"));
                                });
                            },
                            error: function (a, b, c) {
                                //console.log(c);
                            }
                        });
                    }
                }

            }
        })
                .showModal();


        return false;
    }

    //原单退货
    function btn_tuihuo() {
        dialog({
            url: '/order/orderinfo/toOrderReturnGoodsView',
            title: '原单退货',
            width: w_width,
            height: w_height,

            data: 'val值', // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    window.orderStatus = 2;//2:原单退货
                    switchMode(window.orderStatus);
                    $('#memberPhone').text('');
                    $("#grid-table").jqGrid("clearGridData");
                    $(this.returnValue.order_item_id_array).each(function (index, item) {
                        //console.log(item.productNum);
                        //item.productNum = '-' + item.productNum;

                        addRowForOrderDetailPosByReturn($("#grid-table"), item);
                    });
                    if (this.returnValue.order_id) {
                        $('#memberName').val(this.returnValue.order_id.memberName);
                        $('#memberPhone').text(this.returnValue.order_id.phoneNumber);
                        $('#allDiscount').val(this.returnValue.order_id.discount);
                        $('#point').text(this.returnValue.order_id.point);
                        $('#memberId').val(this.returnValue.order_id.memberId);
                        $("#priceStrategy").val(this.returnValue.order_id.priceStrategy);
                        //根据是否会员切换显示字段
                        if ($('#priceStrategy').val() == '2') {
                            //==2  会员价
                            $("#grid-table").setGridParam().hideCol("productRetailPrice").trigger("reloadGrid");//隐藏零售价
                            $("#grid-table").setGridParam().showCol("productMemberPrice").trigger("reloadGrid");//展示会员价
                        } else {
                            // or 零售价
                            $("#grid-table").setGridParam().showCol("productRetailPrice").trigger("reloadGrid");//展示零售价
                            $("#grid-table").setGridParam().hideCol("productMemberPrice").trigger("reloadGrid");//隐藏会员价
                        }
                    }
//                    $.ajax({
//                        type:'post',
//                        url:'/order/orderDetail/getOrderDetailsByIds',
//                        data:{ids:data_json},
//                        success:function(res){
//                            if(res.result){
//                                jQuery("#grid-table").jqGrid("clearGridData");
//                                $(res.result.list).each(function(index,item){
//                                    console.log(item);
//                                    addRowForOrderDetailPos($("#grid-table"),item);
//                                });
//                            }
//                        },
//                        error:function(err){
//
//                        }
//                    })

                }
            }
        }).showModal();
        return false;
    }

    //挂账
    function bnt_guazhang() {
        var allRowData = jQuery("#grid-table").jqGrid('getRowData')
        var data_json = JSON.stringify(allRowData);
        //判断如果有优惠商品则屏蔽会员信息
        var promotionTypeFlag = true;
        if (!$("#grid-table").jqGrid('getRowData').length) {
            btn_alertDialog('提示', '商品明细不能为空');
            promotionTypeFlag = false;
            return false;
        }
        $($("#grid-table").jqGrid('getRowData')).each(function (i, v) {
            if (v.promotionType != "0") {
                btn_alertDialog('提示', '包含优惠商品！');
                promotionTypeFlag = false;
                return false;
            }
        })
        if (!promotionTypeFlag) {
            return false;
        }

        if (allRowData.length > 0) {
            $.ajax({
                type: 'post',
                url: '/order/orderinfo/orderLedger',
                data: {
                    orderVoArray: data_json,
                    memberId: $("#memberId").val(),
                    saleId: $("#saleId").val(),
                    allDiscount: $("#allDiscount").val(),
                    status: "01",
                    promotionId: $("#promotionId").val(),
                    extractStatus: "0"
                },
                success: function (res) {
                    //console.log(res.result)
                    if (res.result) {
                        removeCache();
                        //表格清空 重新渲染 为空表
                        jQuery("#grid-table").jqGrid("clearGridData");
                        $("#memberName").val("");
                        $("#memberPhone").text("");
                        $("#point").text("");
                        $("#memberId").val("");
                        $("#allDiscount").val("100");
                        if (sysConfig && sysConfig.savePreSaleYn == 1) {
                            location.href = "/order/orderinfo/toOrderSettlementView?saleId=" + $("#saleId").val();
                        } else {
                            location.href = "/order/orderinfo/toOrderSettlementView";
                        }
                    } else {
                        btn_alertDialog('提示', "挂帐失败");
                    }
                },
                error: function (err) {

                }
            })
        }
    }

    //收款
    function btn_payment() {
        var allRowData = jQuery("#grid-table").jqGrid('getRowData');
        if (allRowData.length == 0) {
            btn_alertDialog('提示', '请选择商品！');
            setTimeout(function () {
                $('.inp_goodsInfoSearch').focus();
            }, 3000)
            return false;
        }

        var bastYn_sum = 0;
        if (sysConfig.flaxNum) {
            $(allRowData).each(function (i, v) {
                if (v.bastYn == 1) {
                    bastYn_sum += Number(v.productNum);
                }
            })
            if ((bastYn_sum) > sysConfig.flaxNum) {
                btn_alertDialog('提示', "购买的含麻商品超过" + sysConfig.flaxNum + "件");
                return false;
            }
        }


        var returnFlag = true;
        for (var i = 0; i < allRowData.length; i++) {
            if (Number(allRowData[i].productRetailPrice) < Number(allRowData[i].costPrice) && !allRowData[i].promotionType) {
                returnFlag = false;
            }
        }

        window.allRowData_ = JSON.parse(JSON.stringify(allRowData));
        //是否有低于成本价商品
        if (returnFlag) {
            ssmodel(allRowData);
        } else {
            var d = dialog({
                title: '提示',
                content: '实际售价低于成本价，是否继续收款？',
                width: 200,
                height: 40,
                button: [
                    {
                        value: '确定',
                        callback: function () {
                            ssmodel(allRowData);
                        },
                        autofocus: true
                    },
                    {
                        value: '取消',
                        callback: function () {
                            //console.log('因有零售价小于成本价的正常商品，所以中止收款！');
                        }
                    }
                ]
            });
            d.showModal();
        }


    }

    //处理中药模式判断的函数
    function ssmodel(allRowData) {
        //如果为 中药模式，提示份数
        if (window.SSmodel && SSmodel == 'Cmedicine') {
            dialog({
                title: '提示份数',
                content: '重复购买<input onblur="this.value=this.value.replace(/\\D/g,\'\')" onafterpaste="this.value=this.value.replace(/\\D/g,\'\')"  id="fenshu" type="number" value="' + (window.fenshu || 1) + '" style="width: 60px;text-align: center" autofocus>份当前商品？',
                width: 280,
                height: 50,
                okValue: '确定',
                ok: function () {
                    if (!$('#fenshu').val() || $('#fenshu').val() <= 0) {
                        btn_alertDialog('提示', "请输入正确的数字！");
                        return false;
                    } else {
                        //调用收款函数----中药模式
                        var passFlag = false, hanmaNum = 0;
                        window.fenshu = $('#fenshu').val();
                        allRowData.map(function (e) {
                            if (Number(e.stockNumber) < Number($('#fenshu').val()) * Number(e.productNum)) {
                                passFlag = '数量超过批号库存！';
                            }

                            if (sysConfig.prescriptionYn && e.prescriptionYn && e.prescriptionNum && Number(e.prescriptionNum) < Number($('#fenshu').val()) * Number(e.productNum)) {
                                passFlag = '超过处方登记数量！';
                            }
                            e.productNum = Number($('#fenshu').val()) * Number(e.productNum);

                            hanmaNum += e.bastYn ? e.productNum : 0;
                            return e;
                        })
                        //if (sysConfig.flaxNum && bastYn_sum > sysConfig.flaxNum) {


                        if (sysConfig.flaxNum && hanmaNum > sysConfig.flaxNum) {
                            allRowData = JSON.parse(JSON.stringify(window.allRowData_));
                            btn_alertDialog('提示', "数量超过可含麻数量！");
                            return false;
                        }
                        if (passFlag) {
                            allRowData = JSON.parse(JSON.stringify(window.allRowData_));
                            btn_alertDialog('提示', passFlag);
                            return false;
                        }
                        //插入中药份数新数据
                        pay_function(allRowData);
                    }
                },


            }).showModal();
            setTimeout(function () {
                $('#fenshu').select()
            }, 100);
        } else {
            //调用收款函数
            pay_function(allRowData);
        }
    }

    function pay_function(allRowData) {
        window.stopLocal = true;
        var data_json = JSON.stringify(allRowData);
        if (window.orderStatus && window.orderStatus == 2) {
            toOrderPaymentView();//退单不调用促销服务
        } else {
            //调用促销服务
            $.ajax({
                type: 'post',
                async: false,
                url: '/promotion/queryPromotion',
                data: {
                    orderVoArray: data_json,
                    memberId: $("#memberId").val(),
                    saleId: $("#saleId").val(),
                    allDiscount: $("#allDiscount").val(),
                    status: "00",
                    promotionId: $("#promotionId").val(),
                    extractStatus: "1"
                },
                success: function (res) {
                    if (res) {
                        if (res.result) {
                            if (res.result.list.length > 0) {
                                var obj = {
                                    "list": res.result.list,
                                    "orderVoArray": data_json,
                                    "memberId": $("#memberId").val(),
                                    "saleId": $("#saleId").val(),
                                    "allDiscount": $("#allDiscount").val(),
                                    "status": "00",
                                    "promotionId": $("#promotionId").val(),
                                    "extractStatus": "1"
                                }
                                //2018.531增加jira SAAS-1347需求
                                if (res.result.list.length == 1) {
                                    var promotionId = res.result.list[0].id;
                                    $.ajax({
                                        type: 'post',
                                        url: '/promotion/computePromotionPrice',
                                        async: false,
                                        data: {
                                            orderVoArray: obj.orderVoArray,
                                            memberId: obj.memberId,
                                            saleId: obj.saleId,
                                            allDiscount: obj.allDiscount,
                                            status: obj.status,
                                            promotionId: promotionId,
                                            extractStatus: obj.extractStatus
                                        },
                                        success: function (res) {
                                            if (res && res.result) {
                                                jQuery("#grid-table").jqGrid("clearGridData");
                                                var orderInfoPo = res.result.orderInfoPo;  //this.returnValue.orderInfoPo;
                                                var orderDetailPos = res.result.orderDetailPos; // this.returnValue.orderDetailPos;
                                                //var promotionId = promotionId; //this.returnValue.promotionId;
                                                $("#promotionId").val(promotionId);
                                                if (res.result.memberDataPo) {
                                                    var memberDataPo = res.result.memberDataPo; // this.returnValue.memberDataPo;
                                                    $("#memberName").val(memberDataPo.name);
                                                    $("#memberPhone").text(memberDataPo.phone_number);
                                                    $("#point").text(memberDataPo.point);
                                                }

                                                addRowForOrderDetailPos($("#grid-table"), orderDetailPos);
                                                validateBastYn();
                                            }
                                            else {
                                                validateBastYn();
                                            }
                                        },
                                        error: function (err) {

                                        }
                                    })
                                    return false;
                                }

                                dialog({
                                    url: '/order/orderinfo/toOrderPromotionPlanView',
                                    title: '可用促销',
                                    width: w_width * 0.8,
                                    async: false,
                                    height: 400,
                                    padding: 10,
                                    data: obj, // 给modal 要传递的 的数据
                                    onclose: function () {
                                        if (this.returnValue) {
                                            //console.log(this.returnValue);
                                            jQuery("#grid-table").jqGrid("clearGridData");
                                            var orderInfoPo = this.returnValue.orderInfoPo;
                                            var orderDetailPos = this.returnValue.orderDetailPos;
                                            var promotionId = this.returnValue.promotionId;
                                            $("#promotionId").val(promotionId);
                                            if (res.result.memberDataPo) {
                                                var memberDataPo = this.returnValue.memberDataPo;
                                                $("#memberName").val(memberDataPo.name);
                                                $("#memberPhone").text(memberDataPo.phone_number);
                                                $("#point").text(memberDataPo.point);
                                            }
                                            //                                            $("#saleId").val(orderInfoPo.sellerId)
                                            addRowForOrderDetailPos($("#grid-table"), orderDetailPos);
                                            validateBastYn();
                                        }
                                        else {
                                            jQuery("#grid-table").jqGrid("clearGridData");
                                            addRowForOrderDetailPos($("#grid-table"), allRowData);
                                            validateBastYn();
                                        }
                                    }
                                }).showModal();
                                return false;
                            }
                        }
                    }
                    jQuery("#grid-table").jqGrid("clearGridData");
                    addRowForOrderDetailPos($("#grid-table"), allRowData);
                    validateBastYn();

                },
                error: function (err) {
                    window.stopLocal = false;
                }
            })
        }
    }


    function validateBastYn() {

        //含麻药品判断
        var allRowData = jQuery("#grid-table").jqGrid('getRowData');
        var bast_num = 0;
        var bast_yn_status = false;
        $(allRowData).each(function (index, item) {
            //console.log(item);
            //console.log(item.bastYn);
            if (item.bastYn == 1) {
                bast_num = bast_num + Number(item.productNum);
                bast_yn_status = true;
            }
        })
        if (sysConfig) {
            if (sysConfig.idCardYn == 1) {
                if (bast_yn_status) {
                    if (sysConfig.flaxNum) {
                        if (Number(bast_num) > sysConfig.flaxNum) {
                            btn_alertDialog('提示', "购买的含麻商品超过" + sysConfig.flaxNum + "件");
                            //alert("购买的含麻商品超过" + sysConfig.flaxNum + "件");
                            return false;
                        } else {
                            var IdentityRegi = {
                                buyerName: $("#buyerName").val(),
                                buyerIdcard: $("#buyerIdcard").val(),
                                buyerMobile: $("#buyerMobile").val()
                            };
                            dialog({
                                url: '/order/orderinfo/toOrderIdentityRegiView',
                                title: '含特殊药品复方制剂购买者登记',
                                width: 560,
                                height: w_height * 0.7,
                                data: IdentityRegi, // 给modal 要传递的 的数据
                                onclose: function () {
                                    if (this.returnValue) {
                                        $("#buyerName").val(this.returnValue.buyerName);
                                        $("#buyerIdcard").val(this.returnValue.buyerIdcard);
                                        $("#buyerMobile").val(this.returnValue.buyerMobile);
                                        toOrderPaymentView();
                                    } else {
                                        return false;
                                    }
                                }
                            }).showModal();
                            return false;
                        }
                    }
                }
            }
        }
        toOrderPaymentView();
    }


    function toOrderPaymentView() {
        var receivableAmountNew = $("#receivableAmountNew").text();
        var actualAmountNew = $("#actualAmountNew").text();
        var discountAmountNew = receivableAmountNew - actualAmountNew>0?(receivableAmountNew - actualAmountNew).toFixed(2):0;
        if (discountAmountNew < 0) {
            discountAmountNew = 0 - Math.abs(receivableAmountNew - actualAmountNew).toFixed(2);
        } else {
            discountAmountNew = Number(discountAmountNew).toFixed(2)
        }

        var orderPayView_obj = {
            "receivableAmountNew": receivableAmountNew,
            "actualAmountNew": actualAmountNew,
            "discountAmountNew": discountAmountNew,
            "orderStatus": window.orderStatus
        }
        dialog({
            url: '/order/orderinfo/toOrderPaymentView',
            title: '收款结算',
            width: 700,
            height: 480,
            padding: 5,
            data: orderPayView_obj, // 给modal 要传递的 的数据
            onclose: function () {
                //关闭结算界面
                if (this.returnValue) {
                    //console.log(this.returnValue)
                    if (this.returnValue) {
                        $("#cashPay").val(this.returnValue.cashPay);
                        $("#aliPay").val(this.returnValue.aliPay);
                        $("#wechatPay").val(this.returnValue.wechatPay);
                        $("#medicarePay").val(this.returnValue.medicarePay);
                        $("#unionPay").val(this.returnValue.unionPay);
                        $("#gatherAmount").val(this.returnValue.gatherAmount);
                        $("#changeAmount").val(this.returnValue.changeAmount);

                        var allRowData = jQuery("#grid-table").jqGrid('getRowData')
                        //console.log(allRowData)
                        var data_json = JSON.stringify(allRowData);
                        if (allRowData.length > 0) {
                            $.ajax({
                                type: 'post',
                                url: '/order/orderinfo/orderReceiveables',
                                data: {
                                    orderVoArray: data_json,
                                    memberId: $("#memberId").val(),
                                    saleId: $("#saleId").val(),
                                    allDiscount: $("#allDiscount").val(),
                                    status: "00",
                                    promotionId: $("#promotionId").val(),
                                    extractStatus: "0",
                                    orderId: $("#orderId").val(),
                                    cashPay: $("#cashPay").val(),
                                    aliPay: $("#aliPay").val(),
                                    wechatPay: $("#wechatPay").val(),
                                    medicarePay: $("#medicarePay").val(),
                                    unionPay: $("#unionPay").val(),
                                    gatherAmount: $("#gatherAmount").val(),
                                    changeAmount: $("#changeAmount").val(),
                                    buyerName: $("#buyerName").val(),
                                    buyerIdcard: $("#buyerIdcard").val(),
                                    buyerMobile: $("#buyerMobile").val()
                                },
                                success: function (res) {
                                    if (res.result) {
                                        removeCache();
                                        if (sysConfig) {
                                            if (sysConfig.printYn == 1) {
                                                $.ajax({
                                                    type: 'post',
                                                    url: '/print/bill/printBill',
                                                    data: {
                                                        jsonData: res.result
                                                    },
                                                    success: function (res) {

                                                    }
                                                })
                                                if (sysConfig && sysConfig.savePreSaleYn == 1) {
                                                    location.href = "/order/orderinfo/toOrderSettlementView?saleId=" + $("#saleId").val();
                                                } else
                                                    location.href = "/order/orderinfo/toOrderSettlementView";
                                            } else if (sysConfig.printYn == 2) {
                                                dialog({
                                                    title: '提示',
                                                    content: '您需要打印小票吗',
                                                    width: 200,
                                                    button: [
                                                        {
                                                            value: '确定',
                                                            callback: function () {
                                                                $.ajax({
                                                                    type: 'post',
                                                                    url: '/print/bill/printBill',
                                                                    async: false,
                                                                    data: {
                                                                        jsonData: res.result
                                                                    },
                                                                    success: function (res) {

                                                                    }
                                                                })
                                                                this.close().remove();
                                                                if (sysConfig && sysConfig.savePreSaleYn == 1) {
                                                                    location.href = "/order/orderinfo/toOrderSettlementView?saleId=" + $("#saleId").val();
                                                                } else
                                                                    location.href = "/order/orderinfo/toOrderSettlementView";
                                                            },
                                                            autofocus: true
                                                        },
                                                        {
                                                            value: '取消',
                                                            callback: function () {
                                                                //alert('您取消了')
                                                                if (sysConfig && sysConfig.savePreSaleYn == 1) {
                                                                    location.href = "/order/orderinfo/toOrderSettlementView?saleId=" + $("#saleId").val();
                                                                } else
                                                                    location.href = "/order/orderinfo/toOrderSettlementView";
                                                            }
                                                        }
                                                    ],
                                                    onclose: function () {// add 20180712
                                                        if (sysConfig && sysConfig.savePreSaleYn == 1) {
                                                            location.href = "/order/orderinfo/toOrderSettlementView?saleId=" + $("#saleId").val();
                                                        } else {
                                                            location.href = "/order/orderinfo/toOrderSettlementView";
                                                        }
                                                    }
                                                }).show();

                                            } else {
                                                if (sysConfig && sysConfig.savePreSaleYn == 1) {
                                                    location.href = "/order/orderinfo/toOrderSettlementView?saleId=" + $("#saleId").val();
                                                } else
                                                    location.href = "/order/orderinfo/toOrderSettlementView";
                                            }
                                        }

                                    }
                                    //location.href = "/order/orderinfo/toOrderSettlementView";
                                },
                                error: function (err) {
                                    btn_alertDialog('提示', '收款失败,商品库存数量不足');
                                    //alert("收款失败,商品库存数量补足");
//                                    location.href = "/order/orderinfo/toOrderSettlementView";
                                }
                            })
                        }

                    }
                } else {
                    //重置中药份数
                    window.stopLocal = false;
                    jQuery("#grid-table").jqGrid("clearGridData");
                    addRowForOrderDetailPos($("#grid-table"), window.allRowData_);
                }
            }
        })
                .showModal();
        return false;
    }

    //会员名称:
    function btn_memberSearch(e) {

        //判断如果有优惠商品则屏蔽会员信息
        var promotionTypeFlag = true;
        $($("#grid-table").jqGrid('getRowData')).each(function (i, v) {
            if (v.promotionType != "0") {
                //console.log('包含优惠商品！');
                promotionTypeFlag = false;
            }
        })
        if (!promotionTypeFlag) {
            btn_alertDialog('提示', '商品信息中含有促销商品，禁止再选择会员！');
            return false;
        }
        //查询会员信息
        $.ajax({
            type: 'post',
            url: '/member/data/mixQueryMember',
            data: {"name": $("#memberName").val()},
            async: false,
            success: function (res) {
                if (res && res.result && res.result.list && res.result.list.length == 1) {
                    var member_obj = res.result.list[0];
                    if (member_obj.expried_time && formatDateym(new Date()) > member_obj.expried_time) {
                        btn_alertDialog('提示', '该会员已过期');
                        return false;
                    }

                    $("#memberName").val(member_obj.name);
                    $("#memberPhone").text(member_obj.phone_number);
                    $("#point").text(member_obj.point);
                    $("#memberId").val(member_obj.id);
                    $("#allDiscount").val(member_obj.discount || 0);
                    $("#priceStrategy").val(member_obj.priceStrategy);
                    //带入会员信息
                    $("#buyerName").val(member_obj.name);
                    $("#buyerIdcard").val(member_obj.id_card);
                    $("#buyerMobile").val(member_obj.phone_number);

                    //console.log('会员状态',$('#priceStrategy').val())
                    //根据是否会员切换显示字段
                    if ($('#priceStrategy').val() == '2') {
                        //==2  会员价
                        $("#grid-table").setGridParam().hideCol("productRetailPrice").trigger("reloadGrid");//隐藏零售价
                        $("#grid-table").setGridParam().showCol("productMemberPrice").trigger("reloadGrid");//展示会员价
                    } else {
                        // or 零售价
                        $("#grid-table").setGridParam().showCol("productRetailPrice").trigger("reloadGrid");//展示零售价
                        $("#grid-table").setGridParam().hideCol("productMemberPrice").trigger("reloadGrid");//隐藏会员价
                    }
                    $('#allDiscount').trigger('input');//模拟input事件，重新计算价格
//                    $("#memberName").val("");
                    return false;
                }
                /*else if(res && res.result && res.result.list && res.result.list.length==0 ){
                    $("#memberName").val("");
                    return false;
                }*/
                else {
                    //promotionType
                    dialog({
                        url: '/order/orderinfo/toOrderMemberInfoView',
                        title: '会员信息',
                        width: w_width,
                        height: w_height,
                        data: {"name": $("#memberName").val()}, // 给modal 要传递的 的数据
                        onclose: function () {
                            if (this.returnValue) {
                                var member_obj = this.returnValue[0];
                                $("#memberName").val(member_obj.name);
                                $("#memberPhone").text(member_obj.phone_number);
                                $("#point").text(member_obj.point);
                                $("#memberId").val(member_obj.id);
                                $("#allDiscount").val(member_obj.discount || 0);
                                $("#priceStrategy").val(member_obj.priceStrategy);
                                //带入会员信息
                                $("#buyerName").val(member_obj.name);
                                $("#buyerIdcard").val(member_obj.id_card);
                                $("#buyerMobile").val(member_obj.phone_number);

                                //console.log('会员状态',$('#priceStrategy').val())
                                //根据是否会员切换显示字段
                                if ($('#priceStrategy').val() == '2') {
                                    //==2  会员价
                                    $("#grid-table").setGridParam().hideCol("productRetailPrice").trigger("reloadGrid");//隐藏零售价
                                    $("#grid-table").setGridParam().showCol("productMemberPrice").trigger("reloadGrid");//展示会员价
                                } else {
                                    // or 零售价
                                    $("#grid-table").setGridParam().showCol("productRetailPrice").trigger("reloadGrid");//展示零售价
                                    $("#grid-table").setGridParam().hideCol("productMemberPrice").trigger("reloadGrid");//隐藏会员价
                                }
                                $('#allDiscount').trigger('input');//模拟input事件，重新计算价格
                                $('.inp_goodsInfoSearch').focus();
                            }
                        }
                    })
                            .showModal();
                    return false;
                }
            },
            error: function (err) {
            }
        })

        $('.inp_goodsInfoSearch').focus();
    }

    //商品信息 查询
    function btn_memberInfoSearch() {
        /*将前台的商品选购数量 传到商品选择页面 start */
        var dataNumber=[];
        if($("#grid-table").jqGrid('getRowData').length>0){
            $.each($("#grid-table").jqGrid('getRowData'),function (_index,_ele) {
                dataNumber.push({"productId":_ele.productId,"productNum":_ele.productNum,"batchNo":_ele.batchNo});
            });
        }
        /*将前台的商品选购数量 传到商品选择页面 end*/
        var productName = $("#productName").val();
        var queryData = {
            productName: productName,
            sysConfig: sysConfig,
            gridData: $("#grid-table").jqGrid('getRowData'),
            dataNumberArr:dataNumber //add 20180713 将前台的商品选购数量 传到商品选择页面

        }
        $("#productName").val('');
        $.ajax({
            //查询商品
            type: 'post',
            url: '/order/orderinfo/orderProductInfoQuery',
            data: {"productName": productName},
            async: false,
            success: function (res) {
                //console.log(res);
                if (res && res.result && res.result.list.length > 0) {
                    if (res.result.list.length == 1) {
                        product_obj = res.result.list[0];
                        var ary = [], bastYn_sum = 0, isflag = true;
                        if (sysConfig.prescriptionYn == 1 && product_obj.prescriptionYn == 1) {
                            btn_alertDialog('提示', '该药品为处方药，请提取处方!');
                            return false;
                        }
                        $(queryData.gridData).each(function (i, v) {
                            //将数量已满的商品的批号传入，
                            if (v.productId == product_obj.id && v.productNum == v.stockNumber) {
                                ary.push(v.batchNo);
                            }
                            if (v.bastYn == 1) {
                                bastYn_sum += Number(v.productNum);
                                if (sysConfig.flaxNum && bastYn_sum > sysConfig.flaxNum) {
                                    isflag = false;
                                    return false;
                                }
                            }
                        })
                        if (!isflag) {
                            btn_alertDialog('提示', '数量大于含麻数量');
                            return false;
                        }
                        product_obj.ary = ary;
                        //查询批号
                        $.ajax({
                            type: 'post',
                            url: '/order/orderinfo/orderChooseBatchNoQuery',
                            data: {
                                "productId": product_obj.id,
                                "batchQueryJson": ary.join(','),
                                "batchNoQueueYn": queryData.sysConfig.batchNoQueueYn
                            },
                            async: false,
                            success: function (res) {
                                if (res && res.result && res.result.list.length > 0) {
                                    if (res.result.list.length == 1) {
                                        batchNo_obj = res.result.list[0];
                                        //console.log(DateDiff(formatDateym(new Date()), batchNo_obj.expirationDate));
                                        if (formatDateym(new Date()) > batchNo_obj.expirationDate) {
                                            btn_alertDialog('提示', ary.length ? '批号已选完' : '批号已过期');
                                        } else {
                                            returnErgodic();
                                        }
                                    } else {
                                        //查询批号
                                        dialog({
                                            url: '/order/orderinfo/toOrderChooseBatchNoView',
                                            title: '选择批号',
                                            width: w_width * 0.8,
                                            height: w_height * 0.8,
                                            //20180731新增选购商品信息 以便批号页面库存=总库存减去选购数量
                                            data: {"product_obj":product_obj,"dataNumberArr": queryData.dataNumberArr}, // 给modal 要传递的 的数据
                                            onclose: function () {
                                                if (this.returnValue) {
                                                    product_obj = this.returnValue[0];
                                                    batchNo_obj = this.returnValue[1];
                                                    returnErgodic();
                                                }
                                            }
                                        }).showModal();
                                        return false;
                                    }
                                } else {
                                    btn_alertDialog('提示', ary.length ? '批号已选完' : '批号已过期');
                                }
                            },
                            error: function (err) {
                            }
                        })
                    } else {
                        queryData.gridData = res;
                        dialog({
                            url: '/order/orderinfo/toOrderSettlementGoodInfoView',
                            title: '商品信息',
                            width: w_width,
                            height: w_height,
                            data: queryData, // 给modal 要传递的 的数据
                            onclose: function () {
                                //console.log(this.returnValue);
                                if (this.returnValue) {
                                    product_obj = this.returnValue[0];
                                    batchNo_obj = this.returnValue[1];
                                    product_obj.prescription_id = "";

                                    returnErgodic();
                                }
                            }
                        }).showModal();
                        return false;
                    }
                } else {
                    dialog({
                        url: '/order/orderinfo/toOrderSettlementGoodInfoView',
                        title: '商品信息',
                        width: w_width,
                        height: w_height,
                        data: queryData, // 给modal 要传递的 的数据
                        onclose: function () {
                            //console.log("1111111111111"+ this.returnValue);
                            if (this.returnValue) {
                                product_obj = this.returnValue[0];
                                batchNo_obj = this.returnValue[1];
                                product_obj.prescription_id = "";

                                returnErgodic();
                            }
                        }
                    })
                            .showModal();
                    return false;
                    goodinfo_showBool = false;//在注册事件时的标志，防止双击
                }

            },
            error: function (err) {
            }
        })

    }

    //商品信息查询遍历
    function returnErgodic() {

        var status = true;
        var goodianhao = product_obj.pref;// 返回的商品编号
        var goodPihao = batchNo_obj.lotNumber; // 返回的商品批号
        var allRowData = $("#grid-table").jqGrid('getRowData'); // 获取当前表格里的所有数据。

        var bastYn_sum = 0;
        if (sysConfig.flaxNum && product_obj.containingHempYn) {
            $(allRowData).each(function (i, v) {
                if (v.bastYn == 1) {
                    bastYn_sum += Number(v.productNum);
                }
            })
            if ((bastYn_sum + 1) > sysConfig.flaxNum) {
                btn_alertDialog('提示', "购买的含麻商品超过" + sysConfig.flaxNum + "件");
                return false;
            }

        }
        if (allRowData) {
            if (allRowData.length == 0) {
                addRow($("#grid-table"));
                status = false;
            }
        }
        var ids = $("#grid-table").jqGrid("getDataIDs");
        if(sysConfig.batchNoQueueYn == 1 && status){//如果开启批号先先出 add 20180807
            var _usableBatchNo=[];//储存未过期 可用批号
            var _usableNumber = 0;//可用总数量
            $.ajax({//获取当前商品可用批号  可用数量
                url:"/order/orderinfo/orderChooseBatchNoQuery",
                type:"post",
                data:{"productId":product_obj.id},
                success:function(data){
                    if(data.result.list.length){
                        $.each(data.result.list,function (_index,_ele) {
                            if(formatDateym(new Date()) < _ele.expirationDate){
                                _usableBatchNo.push(_ele);
                                _usableNumber += _ele.stockNumber;
                            }
                        });
                    }
                    $.each(_usableBatchNo,function (_index,_ele) {
                        var continueFlag = true;//是否继续循环
                        var _usableNumber2 = _usableNumber; //初始化可用数量
                        $.each(allRowData,function(_i,_e){
                            if(_e.productId == product_obj.id){
                                var num = Number(_e.productNum);//获取当前匹配列的 数量 列的值
                                var _num = _e.prescriptionId ? _e.prescriptionNum : _ele.stockNumber;
                                _usableNumber2 -= num;
                                //如果已有当前批号的商品 且有可用库存时  直接改变那一行的值
                                if (_e.batchNo == _ele.lotNumber && _e.productNum < _ele.stockNumber && _e.promotionType == "0" && continueFlag) { //  比较批号相等 且 当前批号库存有剩余
                                    if (num < _num) {
                                        num = num + 1;
                                        $("#grid-table").jqGrid('setCell', ids[_i], 'productNum', num);  // 修改指定列的数量值
                                    } else {
                                        $("#grid-table").jqGrid('setCell', ids[_i], 'productNum', _num);  // 修改指定列的数量值
                                        _e.prescriptionNum ? btn_alertDialog('提示', '数量不能大于处方登记数量') : btn_alertDialog('提示', '数量不能大于当前批号库存数量');
                                    }
                                    continueFlag = false;
                                    status = false;
                                    return false;
                                }else if(_e.batchNo == _ele.lotNumber && _e.productNum >= _ele.stockNumber){
                                    continueFlag = false;
                                }
                            }

                        })
                        if(status && continueFlag ){ //如果没有当前批号 或者可用库存已经用完事  按照先进先出的规则  改变批号
                            batchNo_obj.lotNumber = _ele.lotNumber;
                            continueFlag = false;
                            addRow($("#grid-table"));
                            status = false;
                            return false;
                        }
                        if(_usableNumber2 <= 0){//说明库存不够
                            btn_alertDialog('提示', '数量不能大于当前可用库存');
                            status = false;
                            return false;
                        }
                    })

                    setTimeout(function () {
                        repExecute_table(2);
                    })
                }
            })
        }else{
            for (var i = 0; i < allRowData.length; i++) { // 遍历  表格的所有数据
                if (allRowData[i].productCode == goodianhao && allRowData[i].batchNo == goodPihao && allRowData[i].promotionType == "0") { //  比较 商品编号 和批号   全相等
                    var curRowData = $("#grid-table").jqGrid("getRowData", ids[i]);
                    //console.log(curRowData);
                    var num = Number(curRowData.productNum);//获取当前匹配列的 数量 列的值
                    var _num = curRowData.prescriptionId ? curRowData.prescriptionNum : curRowData.stockNumber
                    if (num < _num) {
                        num = num + 1;
                        $("#grid-table").jqGrid('setCell', ids[i], 'productNum', num);  // 修改指定列的数量值

                    } else {
                        $("#grid-table").jqGrid('setCell', ids[i], 'productNum', _num);  // 修改指定列的数量值
                        curRowData.prescriptionNum ? btn_alertDialog('提示', '数量不能大于处方登记数量') : btn_alertDialog('提示', '数量不能大于当前批号库存数量');

                    }
                    status = false;
                }
            }
            if (status) {
                addRow($("#grid-table"));
                status = false;

            }
            setTimeout(function () {
                repExecute_table(2);
            })
        }
        setTimeout(function () {
            if (sysConfig.saleAmountYn) {//如果控制面板默认数量 开启
                $("#focusNumber").val("1").focus().select();
            }else{
                $("#productName").focus();
            }
        },100)
        goodinfo_showBool = false;//在注册事件时的标志，防止双击

    }
    $("#focusNumber").keyup(function (e) {//数量修改框
        var focusNumberVal = Number($(this).val());
        if (e.keyCode == 13) {
            if ( focusNumberVal != 0) {
                var selectId = $("#grid-table").jqGrid('getGridParam', 'selrow');
                var changeRowData = $("#grid-table").jqGrid("getRowData", selectId);
                if(!selectId){
                    return false;
                }
                if(changeRowData.promotionType!="0" || window.orderStatus == 2){
                    return false;
                }
                $.ajax({//获取当前商品可用批号  可用数量
                    url:"/order/orderinfo/orderChooseBatchNoQuery",
                    type:"post",
                    data:{"productId":changeRowData.productId},
                    success:function(data) {
                        var usableBatchNo01 = [];
                        var usableNumber01 = 0;
                        if (data.result.list.length) {
                            $.each(data.result.list, function (_index, _ele) {
                                if (formatDateym(new Date()) < _ele.expirationDate) {
                                    usableBatchNo01.push(_ele);
                                    usableNumber01 += Number(_ele.stockNumber);
                                }
                            });
                            //console.log(usableBatchNo01);
                            //console.log(usableNumber01);
                            var bastYn_sum = 0;
                            if (sysConfig.flaxNum) {//已选购含麻商品总数
                                $($("#grid-table").jqGrid('getRowData')).each(function (i, v) {
                                    if (v.bastYn == 1) {
                                        bastYn_sum += Number(v.productNum);
                                    }
                                })
                            }

                            var focusNumberObj = {
                                batchNo: changeRowData.batchNo,
                                discount: changeRowData.discount,
                                unitPirce: $('#priceStrategy').val() == '' || $('#priceStrategy').val() == '1' ? changeRowData.productRetailPrice : changeRowData.productMemberPrice,
                                num: focusNumberVal,
                                actualAmount: changeRowData.actualAmount,
                                productId: changeRowData.productId,
                                flaxNum: sysConfig.flaxNum,
                                bastYn:changeRowData.bastYn,//含特殊药品复方制剂
                                bastYn_sum:bastYn_sum  - Number(changeRowData.productNum),//除当前行之外 含麻商品总数
                                stockNumber:changeRowData.stockNumber,//批号库存
                                usableNumber:usableNumber01,//可用总库存
                                usableBatchNo: usableBatchNo01
                            };
                            focusChangeNum(focusNumberObj);
                        }
                    }
                })
            }
        }
    })
    //数量修改框
    function focusChangeNum(focusNumberObj) {
        var change_info_obj = focusNumberObj;
        var usableBatchNo_02 = change_info_obj.usableBatchNo;//可用库存信息
        var curbatchNoNumber = 0;//当前批号库存
        var usableNumber2 = change_info_obj.usableNumber;//计算剩余总库存 即输入框最大值
        $($("#grid-table").jqGrid('getDataIDs')).each(function (_index, _ele){
            var row_data =  $("#grid-table").jqGrid("getRowData", _ele);
            if(_ele != $("#grid-table").jqGrid('getGridParam', 'selrow') && row_data.productId == change_info_obj.productId){
                usableNumber2 -= Number(row_data.productNum);
            }
        })
        if(change_info_obj.bastYn && Number(change_info_obj.num) +  Number(change_info_obj.bastYn_sum) > change_info_obj.flaxNum){
            btn_alertDialog('提示', '数量不可大于含特殊药品复方制剂零售数量');
            $("#focusNumber").blur().val("");
            $("#productName").focus();
            change_info_obj.num = change_info_obj.flaxNum - Number(change_info_obj.bastYn_sum);
        }
        if(sysConfig.batchNoQueueYn != 1 && change_info_obj.num > change_info_obj.stockNumber){
            btn_alertDialog('提示', '数量不可大于批号库存数量');
            $("#focusNumber").blur().val("");
            $("#productName").focus();
            change_info_obj.num = Number(change_info_obj.stockNumber);
        }
        if(sysConfig.batchNoQueueYn == 1 && change_info_obj.num > usableNumber2){
            btn_alertDialog('提示', '数量不可大于可用库存数量');
            $("#focusNumber").blur().val("");
            $("#productName").focus();
            change_info_obj.num = Number(usableNumber2);
        }

        if(usableBatchNo_02 &&usableBatchNo_02.length){
            $.each(usableBatchNo_02,function (_index,_ele) {
                if(_ele.lotNumber == change_info_obj.batchNo){
                    curbatchNoNumber = _ele.stockNumber;
                }
            })
        }
        change_info_obj.num = Number(change_info_obj.num).toFixed(2);
        //删除与当前行商品批号 一致的行
        var _gridTableID=$("#grid-table").jqGrid('getDataIDs');
        $.each(_gridTableID,function(_i,_e){
            var rowDate =  $("#grid-table").jqGrid("getRowData",_e);
            if(rowDate.productId == change_info_obj.productId && rowDate.batchNo == change_info_obj.batchNo && _e != $("#grid-table").jqGrid('getGridParam', 'selrow')){//商品 批号一致
                change_info_obj.num += Number(rowDate.productNum);//商品 批号 一致 删掉一致行并将其数量加至总数量上面
                $("#grid-table").jqGrid("delRowData",_e);

            }
        })
        change_info_obj.num = isNaN(change_info_obj.num * 1) ?  0 : change_info_obj.num;
        var id = jQuery("#grid-table").jqGrid('getGridParam', 'selrow');
        if(sysConfig.batchNoQueueYn == 1 && curbatchNoNumber){//是否开启批号先进先出
            $("#grid-table").jqGrid("setCell", id, "productNum", change_info_obj.num > curbatchNoNumber ? curbatchNoNumber : Number(parseFloat(change_info_obj.num)));
        }else {
            $("#grid-table").jqGrid("setCell", id, "productNum",  Number(parseFloat(change_info_obj.num)));
        }
        if ($('#priceStrategy').val() == '' || $('#priceStrategy').val() == '1') {
            $("#grid-table").jqGrid("setCell", id, "productRetailPrice", change_info_obj.unitPirce);
        } else {
            $("#grid-table").jqGrid("setCell", id, "productMemberPrice", change_info_obj.unitPirce);
        }

        $("#grid-table").jqGrid("setCell", id, "discount", change_info_obj.discount);
        $("#grid-table").jqGrid("setCell", id, "actualAmount", change_info_obj.actualAmount);
        $("#grid-table").jqGrid("setCell", id, "batchNo", change_info_obj.batchNo);

        if (change_info_obj.expirationDate && change_info_obj.goodNumber) {
            $("#grid-table").jqGrid("setCell", id, "expirationDate", change_info_obj.expirationDate);//过期时间
            $("#grid-table").jqGrid("setCell", id, "stockNumber", change_info_obj.goodNumber);//批号库存
        }


        //如果数量大于当前批号数量
        var gridTableID123=$("#grid-table").jqGrid('getDataIDs');//记录此时(123)的行
        if(change_info_obj.num > curbatchNoNumber){
            if(usableBatchNo_02 && usableBatchNo_02.length){
                var newNumber = (change_info_obj.num - curbatchNoNumber).toFixed(2);
                $.each(usableBatchNo_02,function(_index,_ele){
                    if(_ele.lotNumber != change_info_obj.batchNo && newNumber > 0){
                        var gridTableID=$("#grid-table").jqGrid('getDataIDs');//已经发生变化的行 以此添加行ID
                        var endRowID = Number(gridTableID[gridTableID.length - 1]);
                        var chongfuBatchNo = true;//是否已有批号
                        $.each(gridTableID123,function(_i,_e){ //循环此时(123)的行
                            var rowDate =  $("#grid-table").jqGrid("getRowData",_e);
                            if(rowDate.batchNo == _ele.lotNumber && rowDate.productId == change_info_obj.productId){
                                chongfuBatchNo = false;
                                if(_ele.stockNumber > rowDate.productNum && newNumber){
                                    var this_number =  _ele.stockNumber > (Number(rowDate.productNum) + Number(newNumber)) ? (Number(rowDate.productNum) + Number(newNumber)) : _ele.stockNumber;
                                    $("#grid-table").jqGrid("setCell", _e, "productNum", Number(parseFloat(this_number)));
                                    newNumber -= Number(this_number-rowDate.productNum).toFixed(2);
                                }
                            }
                        })
                        if(newNumber > 0 && chongfuBatchNo){
                            var this_number = newNumber > _ele.stockNumber ? _ele.stockNumber : newNumber;
                            var rowDate =  $("#grid-table").jqGrid("getRowData",id);//获取当前行数据
                            $('#grid-table').jqGrid('addRowData', endRowID + 1, rowDate);
                            $("#grid-table").jqGrid("setCell", endRowID + 1, "batchNo", _ele.lotNumber);
                            $("#grid-table").jqGrid("setCell", endRowID + 1, "productNum", Number(parseFloat(this_number)));
                            $("#grid-table").jqGrid("setCell", endRowID + 1, "stockNumber", _ele.stockNumber);
                            newNumber -= this_number;
                            newNumber.toFixed(2);
                        }
                    }
                })
            }
        }
        $("#focusNumber").val("");
        $("#productName").focus();
        setTimeout(function () {
            repExecute_table(change_info_obj.discount);
        })
    }


    //取消交易   -- 0626去除取消交易快捷键
    function cancelTrade() {
//      jQuery("#grid-table").jqGrid("clearGridData");///////
        removeCache();
        location.href = "/order/orderinfo/toOrderSettlementView";
    }


    //删除商品
    function deleteProduct() {
        var allRowIds = $("#grid-table").jqGrid('getDataIDs');
        var id = jQuery("#grid-table").jqGrid('getGridParam', 'selrow');
        if (!id) {
            btn_alertDialog('提示', '请选中商品');
            return false;
        }

        var promotionTypeFlag = true;
        $($("#grid-table").jqGrid('getRowData')).each(function (i, v) {
            if (v.promotionType != "0" && allRowIds[i] == id) {
                promotionTypeFlag = false;
                //$("#grid-table").find("tr[id=" + allRowIds[i] + "]").remove();
            }
        })
        if (!promotionTypeFlag) {
            $($("#grid-table").jqGrid('getRowData')).each(function (i, v) {
                //console.log(v.promotionType, allRowIds[i]);
                if (v.promotionType != "0") {
                    $("#grid-table").find("tr[id=" + allRowIds[i] + "]").remove();
                }
            })
        } else {
            if (allRowIds.length == 0) {
                return false
            }
            $("#grid-table").find("tr[id=" + id + "]").remove();
        }


        repExecute_table();
        $("#productName").focus();
    }

    newrowids = [];

    //新增商品明细
    function addRow($jqgrid) {
        var ids = $jqgrid.jqGrid("getDataIDs");
        var $firstTrRole = $("#grid-table").find("tr").eq(1).attr("role");
        var rowid = $firstTrRole == "row" ? Math.max.apply(Math, ids) : 100;
        var newrowid = parseInt(rowid) + 1;
        newrowids[newrowids.length] = newrowid;


        //定义row
        var dataRow = {
            productId: product_obj.id,
            promotionType: "正常",
            productCode: product_obj.pref,
            commonName: product_obj.commonName,
            productName: product_obj.productName,
            productSpecifications: product_obj.attributeSpecification,
            productUnit: product_obj.unitId,
            productManufacturer: product_obj.manufacturer,
            batchNo: batchNo_obj.lotNumber,
            productNum: product_obj.productNum || 1,
            productRetailPrice: product_obj.retailPrice,
            productMemberPrice: product_obj.vipPrice,
            discount: $('#allDiscount').val(),
            costPrice: product_obj.costPrice,
            actualUnitAmount: product_obj.retailPrice,
            receivableAmount: product_obj.retailPrice * 1,
            actualAmount: product_obj.retailPrice * 1,
            discountAmount: 0.00,
            productOriginAddress: product_obj.producingArea,
            prescriptionClassification: product_obj.prescriptionId,
            prescriptionId: product_obj.prescription_id,
            prescriptionYn: product_obj.prescriptionYn,
            bastYn: product_obj.containingHempYn,
            stockNumber: batchNo_obj.stockNumber,
            productStockNumber: product_obj.stockNum,
            promotionIntegralMultiple: 1,
            expirationDate: batchNo_obj.expirationDate,
            prescriptionNum: product_obj.productNum || 1,
        };


        //将新添加的行插入到最后一列
        $jqgrid.jqGrid("addRowData", newrowid, dataRow, "last");
        repExecute_table();
    }

    //新增商品明细
    function addRowForOrderDetailPos($jqgrid, orderDetailPos) {
        $(orderDetailPos).each(function (index, item) {
            var ids = $jqgrid.jqGrid("getDataIDs");
            // console.log(ids);
            var $firstTrRole = $("#grid-table").find("tr").eq(1).attr("role");
            var rowid = $firstTrRole == "row" ? Math.max.apply(Math, ids) : 100;
            //console.log(rowid);
            var newrowid = parseInt(rowid) + 1;
            newrowids[newrowids.length] = newrowid;
            //定义row
            var dataRow = {
                productId: item.id || item.productId,
                promotionType: item.promotionType,
                productCode: item.productCode,
                commonName: item.commonName,
                productName: item.productName,
                productSpecifications: item.productSpecifications,
                productUnit: item.productUnit,
                productManufacturer: item.productManufacturer,
                batchNo: item.batchNo,
                productNum: item.productNum,
                productRetailPrice: (Number(item.productRetailPrice) || 0).toFixed(2),
                productMemberPrice: (Number(item.productMemberPrice) || 0).toFixed(2),
                discount: item.discount,

                //costPrice: (item.costPrice || 0).toFixed(2),
                //costPrice：数据库6位，productUnitCostAmount：数据库2位
                costPrice: item.costPrice || item.productUnitCostAmount,

                prescriptionId: item.prescriptionId,
                actualUnitAmount: (Number(item.actualUnitAmount) || 0).toFixed(2),
                receivableAmount: (Number(item.receivableAmount) || 0).toFixed(2),
                actualAmount: (Number(item.actualAmount) || 0).toFixed(2),
                discountAmount: (Number(item.discountAmount) || 0).toFixed(2),
                productOriginAddress: item.productOriginAddress,
                prescriptionClassification: item.prescriptionClassification,
                prescriptionYn: item.prescriptionYn,
                prescriptionNum: item.prescriptionNum,
                bastYn: item.bastYn,
                stockNumber: item.stockNumber,
                productStockNumber: item.productStockNumber,
                promotionIntegralMultiple: item.promotionIntegralMultiple,
                RefundUpperlimit: item.refundableNumber || 1,
                expirationDate: item.expirationDate,
                promotionFlag: item.promotionFlag
            };
            //将新添加的行插入到最后一列
            $jqgrid.jqGrid("addRowData", newrowid, dataRow, "last");
            repExecute_table();
        })
    }

    //新增商品明细(退货)
    function addRowForOrderDetailPosByReturn($jqgrid, orderDetailPos) {
        $(orderDetailPos).each(function (index, item) {
            var ids = $jqgrid.jqGrid("getDataIDs");
            //console.log(ids);
            var $firstTrRole = $("#grid-table").find("tr").eq(1).attr("role");
            var rowid = $firstTrRole == "row" ? Math.max.apply(Math, ids) : 100;
            //console.log(rowid);
            var newrowid = parseInt(rowid) + 1;
            newrowids[newrowids.length] = newrowid;
            //定义row
            var dataRow = {
                productId: item.id,
                promotionType: item.promotionType,
                productCode: item.productCode,
                commonName : item.commonName,
                productName: item.productName,
                productSpecifications: item.productSpecifications,
                productUnit: item.productUnit,
                productManufacturer: item.productManufacturer,
                batchNo: item.batchNo,
                productNum: 0 - item.refundableNumber,   // item.productNum  ,退货数量为  退货可退数量
                productRetailPrice: item.productRetailPrice,
                productMemberPrice: item.productMemberPrice,
                discount: item.discount,
                costPrice: item.costPrice,
                prescriptionId: item.prescriptionId,
                actualUnitAmount: item.actualUnitAmount,
                receivableAmount: item.receivableAmount,
                actualAmount: item.actualAmount,
                discountAmount: item.discountAmount,
                productOriginAddress: item.productOriginAddress,
                prescriptionClassification: item.prescriptionClassification,
                prescriptionYn: item.prescriptionYn,
                bastYn: item.bastYn,
                stockNumber: item.stockNumber,
                productStockNumber: item.productStockNumber,
                promotionIntegralMultiple: item.promotionIntegralMultiple,
                RefundUpperlimit: item.refundableNumber || 1,
                orderItemId: item.orderItemId,
                expirationDate: item.expirationDate
            };
            //将新添加的行插入到最后一列
            $jqgrid.jqGrid("addRowData", newrowid, dataRow, "last");
            repExecute_table();
        })
    }

    //计算时间
    function DateDiff(d1, d2) {
        var day = 24 * 60 * 60 * 1000;
        try {
            var dateArr = d1.split("-");
            var checkDate = new Date();
            checkDate.setFullYear(dateArr[0], dateArr[1] - 1, dateArr[2]);
            var checkTime = checkDate.getTime();

            var dateArr2 = d2.split("-");
            var checkDate2 = new Date();
            checkDate2.setFullYear(dateArr2[0], dateArr2[1] - 1, dateArr2[2]);
            var checkTime2 = checkDate2.getTime();

            var cha = (checkTime - checkTime2) / day;
            return cha;
        } catch (e) {
            return false;
        }
    }

    $(function () {
        /*确认弹框 增加快捷键~键~键 start*/
        $("body").on("keyup",".ui-dialog-button>button",function (e) {
            var length_button = $(".ui-dialog-button>button").length;
            var index_button=$(this).index();
            if(e.keyCode == 39){
                if(index_button != length_button-1){
                    $(".ui-dialog-button>button").eq(index_button+1).focus();
                }
            }
            if(e.keyCode == 37){
                if(index_button != 0){
                    $(".ui-dialog-button>button").eq(index_button-1).focus();
                }
            }
            if(e.keyCode == 13){
                $(this).click();
                return false;
            }
        })
        /*确认弹框  增加快捷键~键~键 end*/

        if (sysConfig) {
            if (sysConfig.posShowPurpriceYn == 0) {
                $("#grid-table").setGridParam().hideCol("costPrice").trigger("reloadGrid");
            }
            if (sysConfig.updateOrderDiscountYn == 0) {
                $("#allDiscount").attr("readOnly", true);
            }
        }

        $.ajax({
            type: 'post',
            url: '/order/orderinfo/getLastOrderInfo',
            data: {},
            success: function (res) {
                //console.log(res.result)
                if (res.result) {
                    //实收
                    var actualAmount = res.result.actualAmount;
                    //应收
                    var receivableAmount = res.result.receivableAmount;
                    //优惠
                    var discountAmount = res.result.discountAmount;
                    //找零
                    var changeAmount = res.result.changeAmount;
                    $("#actualAmountOld").html(Number(actualAmount).toFixed(2));
                    $("#receivableAmountOld").html(Number(receivableAmount).toFixed(2));
                    $("#discountAmountOld").html(Number(discountAmount).toFixed(2));
                    $("#changeAmountOld").html(Number(changeAmount).toFixed(2));
                }
            },
            error: function (err) {

            }
        })

        //页面加载进来 商品信息  onfocus
        $('.inp_goodsInfoSearch').focus();

        //会员名称双击
        $('.inp_memberSerch').dblclick(function (e) {
            btn_memberSearch(e);
        })

        //会员名称单机
        $('#memberName').next('span.form-control-feedback').on('click', function (e) {
            btn_memberSearch(e);
        })

        //商品信息    双击
        $('.inp_goodsInfoSearch').dblclick(function () {
            btn_memberInfoSearch();
        })

        //商品信息    单机
        $('.inp_goodsInfoSearch').next('span.form-control-feedback').on('click', function () {
            btn_memberInfoSearch();
        })

        //整单扣率 监听 然后修改 表格里的数字
        $('#allDiscount').bind("input propertychange change", function (event) {
            repExecute_table(1);
        });

        $('#productName').on('keydown', function (e) {
            var $seleTr = $("#grid-table").find('tr[tabindex="0"]');
            if (e.keyCode == 38) {
                var prevTr = $seleTr.prev('tr').prev('tr[tabindex]')
                if (prevTr.length) {
                    $("#grid-table").jqGrid('setSelection', prevTr.prop('id'));
                }
            } else if (e.keyCode == 40) {
                var nextTr = $seleTr.prev('tr').next('tr[tabindex]')
                if (nextTr.length) {
                    $("#grid-table").jqGrid('setSelection', nextTr.prop('id'));
                }
            }
        })

        $('#allDiscount').keyup(function (event) {
            if (event.keyCode == 13) {
                //$('.btn_submit').click()
                $('.inp_goodsInfoSearch').focus();

                return;
            }
        });

        $('#innerBody').delegate('.ui-popup-modal', 'keyup', function (e) {
            var $this = $(this);

            if (e.keyCode == 27) {
                $this.find('button[i-id="cancel"],button[i-id=取消]').trigger('click');
                return false;
                e.preventDefault();
            } else if (e.keyCode == 13) {
                $this.find('button[i-id="ok"],button[i-id=确定]').trigger('click');
                return false;
                e.preventDefault();
            }


        })

        $('.btn-group').on('moseout', function () {
            $('.dropdown-menu').show();
        })

        //20180616增加中药销售模式
        window.SSmodel = 'routine'
        $('#SSModel').on('click', function (e) {
            if ($(this).text() == '切换到中药销售') {
                //中药销售模式
                window.SSmodel = 'Cmedicine'
                $(this).text('切换到常规销售');
                $('#ModelShow').text('中');
            } else {
                //常规销售模式
                window.SSmodel = 'routine'
                $(this).text('切换到中药销售');
                $('#ModelShow').text('常');
            }

        })
        //20180530 增加pos缓存，次/40s储存
        setInterval(function () {
            //如果stopLocal为真，则停止缓存
            if (!window.stopLocal) {
                var formData = $('#posForm').serializeArray();
                formData.push({name: 'memberPhone', value: $('#memberPhone').text()});
                formData.push({name: 'point', value: $('#point').text()});
                var storageBlock = [];
                $('#storageBlock input').each(function (i, v) {
                    storageBlock.push({name: v.name, value: v.value});
                })

                var pos_cache = {
                    gridData: $('#grid-table').jqGrid('getRowData'),
                    formData: formData,
                    storageBlock: storageBlock
                }
                //console.log('存储POS缓存');
                localStorage.setItem('pos_cache', JSON.stringify(pos_cache));
            }
        }, 40000);
    })

    //注入缓存 pos_cache
    function setCache() {
        var pos_cache = JSON.parse(localStorage.getItem('pos_cache'));
        if (pos_cache && pos_cache.gridData.length > 0) {
            $(pos_cache.gridData).each(function (i, v) {
                //console.log(v.productId, v);
                $('#grid-table').jqGrid('addRowData', 101 + i, v);
            })
        }
        if (pos_cache && pos_cache.formData.length > 0) {
            $(pos_cache.formData).each(function (i, v) {
                $('#posForm #' + v.name).val(v.value);

                if (v.name == 'point') $('#point').text(v.value);
                if (v.name == 'memberPhone') $('#memberPhone').text(v.value);

            })

        }
        if (pos_cache && pos_cache.storageBlock.length > 0) {
            $(pos_cache.storageBlock).each(function (i, v) {
                $('#storageBlock #' + v.name).val(v.value);
            })
        }
        //console.log('注入缓存！');
    }


    function toPercent(val) {
        return (Number(val) / 100).toFixed(2);
    }

    //会员名称  商品信息 回车  查询 modal
    var member_showBool = false, goodinfo_showBool = false;

    function btn_enterEvent(which) {
        member_showBool = false;
        goodinfo_showBool = false;
        var name = $(which).attr('name')
        $(which).unbind('keydown').keydown(function (e) {
            //console.log('事件触发：', e.keyCode, name);

            var $seleTr = $("#grid-table").find('tr[tabindex="0"]');
            if (e.keyCode == 38) {
                var prevTr = $seleTr.prev('tr[tabindex]')
                if (prevTr.length) {
                    $("#grid-table").jqGrid('setSelection', prevTr.prop('id'));
                } else {
                    $("#grid-table").jqGrid('setSelection', $("#grid-table").find('tr[tabindex]:last').prop('id'));

                }
            } else if (e.keyCode == 40) {
                var nextTr = $seleTr.next('tr[tabindex]')
                if (nextTr.length) {
                    $("#grid-table").jqGrid('setSelection', nextTr.prop('id'));
                } else {
                    $("#grid-table").jqGrid('setSelection', $("#grid-table").find('tr[tabindex]').eq(0).prop('id'));
                }
            }


            if (e.keyCode == 13) {
                switch (name) {
                    case 'memberName':
                        if (!member_showBool) {
                            btn_memberSearch()
                            member_showBool = true;
                            return false;
                        }
                        break;
                    case 'name_goodinfo':

                        //console.log("即将触发商品查询事件：", goodinfo_showBool);
                        if (!goodinfo_showBool) {
                            goodinfo_showBool = true;
                            btn_memberInfoSearch()

                        }
                        break;
                }

                e.preventDefault();
            }


        })
    }

    $('.inp_goodsInfoSearch').on('keydown', function (e) {
        fireKeyEvent(document.querySelector('#grid-table'), e.type, e.keyCode)
    })

    function fireKeyEvent(el, evtType, keyCode) {
        var doc = el.ownerDocument,
                win = doc.defaultView || doc.parentWindow,
                evtObj;
        if (doc.createEvent) {
            if (win.KeyEvent) {
                evtObj = doc.createEvent('KeyEvents');
                evtObj.initKeyEvent(evtType, true, true, win, false, false, false, false, keyCode, 0);
            }
            else {
                evtObj = doc.createEvent('UIEvents');
                Object.defineProperty(evtObj, 'keyCode', {
                    get: function () {
                        return this.keyCodeVal;
                    }
                });
                Object.defineProperty(evtObj, 'which', {
                    get: function () {
                        return this.keyCodeVal;
                    }
                });
                evtObj.initUIEvent(evtType, true, true, win, 1);
                evtObj.keyCodeVal = keyCode;
                if (evtObj.keyCode !== keyCode) {
                    //console.log("keyCode " + evtObj.keyCode + " 和 (" + evtObj.which + ") 不匹配");
                }
            }

            el.dispatchEvent(evtObj);
        }
        else if (doc.createEventObject) {
            evtObj = doc.createEventObject();
            evtObj.keyCode = keyCode;
            el.fireEvent('on' + evtType, evtObj);
        }
    }

    function keyController(e, fn) {
        //console.log('主窗体快捷键:', $(this));
        addLintenerKeyCode(e);
    }
    //document.onreadystatechange = completeLoading;
</script>

</html>
