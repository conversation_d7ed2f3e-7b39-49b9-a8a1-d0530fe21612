<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta charset="utf-8"/>
    <title>Dashboard - Ace Admin</title>

    <meta name="description" content="overview &amp; stats"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>
<#include "/common/top-common.ftl"/>
    <style>
        #queryBtn {
            border-radius: 4px;
        }
    </style>
</head>
<body id="innerBody">
<div class="row">
    <div class="col-sm-4 col-xs-5 marginTop5">
        <div class="input-group">
            <label class="input-group-addon">商品:</label>
            <input id="productName" name="productName" type="text" class="form-control">
        </div>
    </div>
    <div class="col-sm-3 col-xs-4 marginTop5 ">
        <div class="input-group">
            <button id="queryBtn" class="btn btn-primary btn-sm btn-query"><i
                    class="ace-icon fa fa-search nav-search-icon"></i>查询
            </button>
        </div>
    </div>
</div>
<div class="marginTop5" id="b_table">
    <table id="grid-table"></table>

    <div id="grid-pager" style=""></div>
</div>
<div class="text-right btn_sub_can_div" style="">
    <button type="button"  class="btn btn-redactbag" >确定</button>
    <button type="button"  class="btn btn-return" >取消</button>
</div>
</body>
<script type="text/javascript">
    var w_width = $(window).width() * 0.8;
    var w_height = $(window).height() * 0.75;
    var prescriptionYn_obj = "";
    var product_data_obj = {};
    $.jgrid.col = {caption: "筛选列", bSubmit: "确定", bCancel: "取消"};
    //当 datatype 为"local" 时需填写

    var finalVal = [];
    var grid_data = [];
    var grid_selector = "#grid-table";
    var pager_selector = "#grid-pager";
    var unit_type_code = {};
    var unit_type_name = {};
    var agent_id_type_code = {};
    var agent_id_type_name = {};
    var prescription_id_type_code = {};
    var prescription_id_type_name = {};
    var dataSource = {url: "/order/orderinfo/orderProductInfoQuery", datatype: "json"};
    var dialog = parent.dialog.get(window);
    window.console = window.console || {
        log: function () {
        }
    }
    $(function () {

        queryData = dialog.data; // 获取对话框传递过来的数据
        grid_data = queryData.gridData;
        var sysConfig = queryData.sysConfig;
        var productName = queryData.productName;
        var dataNumberArr = queryData.dataNumberArr;
        $("#productName").val(productName);
        prescriptionYn_obj = "";
        batch_no_queue_yn = "";
        if (sysConfig) {
            prescriptionYn_obj = sysConfig.prescriptionYn;
            batch_no_queue_yn = sysConfig.batchNoQueueYn;
        }

        $('.btn_submit').on('click', function () {
            if (prescriptionYn_obj == 1) {
                if (product_data_obj.prescriptionYn == 1) {
                    btn_alertDialog('提示', '该药品为处方药，请提取处方！');
                    //alert("该药品为处方药，请提取处方！");
                    return false;
                }
            }
            if (JSON.stringify(product_data_obj) != "{}") {
                finalVal = [];
                check_pihao_len();

                dialog({
                    url: '/order/orderinfo/toOrderChooseBatchNoView',
                    title: '选择批号',
                    width: w_width,
                    height: w_height,
                    data: product_data_obj, // 给modal 要传递的 的数据
                    onclose: function () {
                        if (this.returnValue) {
                            //$('#pos_date').html("这里是modal 返回的值  " + this.returnValue);
                            //console.log('选择批号 modal  返回的值  ：  ' + this.returnValue)
                            dialog.close(this.returnValue).remove()
                        }
                    },
                    oniframeload: function () {

                    }
                }).showModal();
                return false;
            }

        });

        //modal  关闭
        $('.btn_modalClose').click(function () {
            dialog.close().remove()
        })
        $('*').on('keydown',function (e) {
            if(e.keyCode == 27){
                dialog.close();
                dialog.remove();
            }
        })

        //必要代码，假操作真实现  键盘监听
        $('#productName').focus();
        setTimeout(function () {
            $('#productName').blur();
        }, 10)

    });

    //打开批号窗口时判断 是否 有信息
    function check_pihao_len() {
        var ary = [];
        $(queryData.gridData).each(function (i, v) {
            //将数量已满的商品的批号传入，
            if (v.productId == product_data_obj.id && v.productNum == v.stockNumber) {
                ary.push(v.batchNo);
            }
        })


        $.ajax({
            type: 'post',
            url: '/order/orderinfo/orderChooseBatchNoQuery',
            data: {
                "productId": product_data_obj.id,
                "batchQueryJson": ary.join(','),
                "batchNoQueueYn": batch_no_queue_yn
            },
            async: false,
            success: function (res) {
                if (res && res.result) {
                    finalVal.push(product_data_obj);
                    var batchNo_obj = res.result.list[0];
                    finalVal.push(batchNo_obj)
                    var dialog = parent.dialog.get(window);


                    if (prescriptionYn_obj == 1 && finalVal[0].prescriptionYn == 1) {
                        btn_alertDialog('提示', '该药品为处方药，请提取处方！');
                        setTimeout(function () {
                            $("#grid-table").focus();
                        }, 2100);
                        return false;
                    }
                    if (res.result.list.length == 1) {

                        if (formatDateym(new Date()) > res.result.list[0].expirationDate) {
                            btn_alertDialog('提示', ary.length ? '批号已选完' : '批号已过期');
                            setTimeout(function () {
                                dialog.close().remove()
                            }, 2000)
                        } else {
                            dialog.close(finalVal).remove()
                        }

                    } else if (res.result.list.length == 0) {
                        btn_alertDialog('提示', ary.length ? '批号已选完' : '批号已过期');
                        setTimeout(function () {
                            dialog.close().remove()
                        }, 2000)
                    } else {
                        var id = jQuery("#grid-table").jqGrid('getGridParam', 'selrow'); // selrow  string  只读属性，最后选择行的id
                        var rowData = $("#grid-table").jqGrid('getRowData', id);
                        open_pihao_dialog(rowData);
                    }

                }
            },
            error: function (err) {
            }
        })


    }

    $(document).keyup(function (event) {
        switch (event.keyCode) {
            case 27:
                event.keyCode = 0;
                event.cancelBubble = true;
                event.preventDefault();
                $('.btn_modalClose').click(); // ESC  取消按钮
                return false;
                break;
            case 13:
                event.keyCode = 0;
                event.cancelBubble = true;
                event.preventDefault();
                //$('.btn_submit').click(); //Enter 确定按钮
                var id = jQuery("#grid-table").jqGrid('getGridParam', 'selrow'); // selrow  string  只读属性，最后选择行的id
                var rowData = $("#grid-table").jqGrid('getRowData', id);
                finalVal = [];
                check_pihao_len();
                return false;
                break;
        }
    });

    $(document).ready(function () {
        $.ajax({
            type: 'post',
            url: '/system/dict/getSystemDictByType',
            data: {bussinessId: 10004},
            async: false,
            success: function (res) {
                if (res) {
                    if (res.result) {
                        unit_type_code = res.result.reduce(function (result, curr) {
                            result[curr.value] = curr.name;
                            return result;
                        }, {});
                        unit_type_name = res.result.reduce(function (result, curr) {
                            result[curr.name] = curr.value;
                            return result;
                        }, {});
                    }
                }
            },
            error: function (err) {

            }
        })
        $.ajax({
            type: 'post',
            url: '/system/dict/getSystemDictByType',
            data: {bussinessId: 10003},
            async: false,
            success: function (res) {
                if (res) {
                    if (res.result) {
                        agent_id_type_code = res.result.reduce(function (result, curr) {
                            result[curr.value] = curr.name;
                            return result;
                        }, {});
                        agent_id_type_name = res.result.reduce(function (result, curr) {
                            result[curr.name] = curr.value;
                            return result;
                        }, {});
                    }
                }
            },
            error: function (err) {

            }
        })
        $.ajax({
            type: 'post',
            url: '/system/dict/getSystemDictByType',
            data: {bussinessId: 20013},
            async: false,
            success: function (res) {
                if (res) {
                    if (res.result) {
                        prescription_id_type_code = res.result.reduce(function (result, curr) {
                            result[curr.value] = curr.name;
                            return result;
                        }, {});
                        prescription_id_type_name = res.result.reduce(function (result, curr) {
                            result[curr.name] = curr.value;
                            return result;
                        }, {});
                    }
                }
            },
            error: function (err) {

            }
        })


        var colNames = ['商品id', '商品编号', '通用名','商品名称', '规格', '单位', '生产厂家', '总库存', '零售价', '会员价', '剂型', '处方分类', '登记处方', '含特殊药品复方制剂', '产地', '成本价'];
        var colModel = [
            {
                name: 'id',
                index: 'id',//索引。其和后台交互的参数为sidx
                key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                //width: 100,
                hidden: true,
                editable: true,
                edittype: "input",
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            },
            {
                name: 'pref',
                index: 'pref',//索引。其和后台交互的参数为sidx
                //width: 100,
                editable: true,
                edittype: "input",
                editoptions: {
                    size: "20",
                    maxlength: "30"
                }
            },
            {
                name: 'commonName',
                index: 'commonName',
                //width: 200,//宽度
                edittype: "input",
            },
            {
                name: 'productName',
                index: 'productName',
                //width: 200,//宽度
                edittype: "input",
            }, {
                name: 'attributeSpecification',
                index: 'attributeSpecification',
                //width: 60,
                sorttype: "double",
                editable: true
            }, {
                name: 'unitId',
                index: 'unitId',
                //width: 60,
                sorttype: "double",
                editable: true,
                formatter: function (cellvalue) {
                    var str = '';
                    if (cellvalue != null && cellvalue != '' && cellvalue != -1) {
                        str = unit_type_code[cellvalue];
                    }
                    return str;
                },
                unformat: function (cellvalue, options, cell) {
                    var str = '';
                    if (cellvalue != null && cellvalue != '' && cellvalue != -1) {
                        str = unit_type_name[cellvalue];
                    }
                    return str;
                }
            }, {
                name: 'manufacturer',
                index: 'manufacturer',
                //width: 60,
                sorttype: "double",
                editable: true
            }, {
                name: 'stockNum',
                index: 'stockNum',
                //width: 60,
                sorttype: "double",
                editable: false,
                formatter: function (cellvalue,options,cell) {
                    /*展示 总库存等于总库存-前台选购数量*/
                     //console.log(options);
                    var counNum = Number(cellvalue);
                    if(queryData.dataNumberArr.length>0){
                            $.each(queryData.dataNumberArr,function (_i,_e) {
                                if(_e.productId == options.rowId){
                                    counNum -= Number(_e.productNum);
                                }
                            })
                    }
                    return counNum;
                    /*总库存等于总库存-前台选购数量*/
                },
                unformat: function (cellvalue, options, cell) {
                    /*保存数据时 还原实际总库存*/
                    //console.log(options);
                    var counNum = Number(cellvalue);
                    if(queryData.dataNumberArr.length>0){
                        $.each(queryData.dataNumberArr,function (_i,_e) {
                            if(_e.productId == options.rowId){
                                counNum += Number(_e.productNum);
                            }
                        })
                    }
                    return counNum;
                    /*保存数据时 还原实际总库存*/
                }
            }, {
                name: 'retailPrice',
                index: 'retailPrice',
                //width: 60,
                sorttype: "double",
                editable: true
            }, {
                name: 'vipPrice',
                index: 'vipPrice',
                //width: 60,
                sorttype: "double",
                editable: true
            }, {
                name: 'agentId',
                index: 'agentId',
                //width: 60,
                sorttype: "double",
                editable: true,
                formatter: function (cellvalue) {
                    var str = '';
                    if (cellvalue != null && cellvalue != '' && cellvalue != -1) {
                        str = agent_id_type_code[cellvalue];
                    }
                    return str;
                },
                unformat: function (cellvalue, options, cell) {
                    var str = '';
                    if (cellvalue != null && cellvalue != '' && cellvalue != -1) {
                        str = agent_id_type_name[cellvalue];
                    }
                    return str;
                }
            }, {
                name: 'prescriptionId',
                index: 'prescriptionId',
                //width: 60,
                sorttype: "double",
                editable: true,
                formatter: function (cellvalue) {
                    var str = '';
                    if (cellvalue != null && cellvalue != '' && cellvalue != -1) {
                        str = prescription_id_type_code[cellvalue];
                    }
                    return str;
                },
                unformat: function (cellvalue, options, cell) {
                    var str = '';
                    if (cellvalue != null && cellvalue != '' && cellvalue != -1) {
                        str = prescription_id_type_name[cellvalue];
                    }
                    return str;
                }
            }, {
                name: 'prescriptionYn',
                index: 'prescriptionYn',
                //width: 60,
                sorttype: "double",
                editable: true,
                formatter: function (cellvalue) {
                    var item = '';
                    if (cellvalue == 1) {
                        item = '是';
                    } else if (cellvalue == 0) {
                        item = "否";
                    }
                    return item;
                },
                unformat: function (cellvalue, options, cell) {
                    var item = '';
                    if (cellvalue == '是') {
                        item = 1;
                    } else if (cellvalue == "否") {
                        item = 0;
                    }
                    return item;
                }
            }, {
                name: 'containingHempYn',
                index: 'containingHempYn',
                //width: 60,
                sorttype: "double",
                editable: true,
                formatter: function (cellvalue) {
                    var item = '';
                    if (cellvalue == 1) {
                        item = '是';
                    } else if (cellvalue == 0) {
                        item = "否";
                    }
                    return item;
                },
                unformat: function (cellvalue, options, cell) {
                    var item = '';
                    if (cellvalue == '是') {
                        item = 1;
                    } else if (cellvalue == "否") {
                        item = 0;
                    }
                    return item;
                }
            }, {
                name: 'producingArea',
                index: 'producingArea',
                //width: 60,
                sorttype: "double",
                editable: true
            }, {
                name: 'costPrice',
                index: 'costPrice',
                //width: 60,
                hidden: true,
                sorttype: "double",
                editable: true
            }
        ];
        var localName = document.location.pathname + 'GridNames';
        var localModeol = document.location.pathname + 'GridModel';

        var colNames_local = localStorage.getItem(localName);
        var colNames_Model = localStorage.getItem(localModeol);

        if (colNames_local !== null && colNames_Model != null) {
            colNames = colNames_local.split(',');
            var bAry=[];
            colNames_Model=JSON.parse(colNames_Model);
            for(var i=0;i<colNames_Model.length;i++){
                for(var c=0;c<colModel.length;c++){
                    if(colModel[c].name==colNames_Model[i].name){
                        bAry.push($.extend(colNames_Model[i],colModel[c],colNames_Model[i]))
                    }
                }
            }
            colModel =bAry;
        }
        $(document).on("mouseup.jqGrid" + 'grid-table', function () {
            setTimeout(function () {
                var a = $('#grid-table').jqGrid('getGridParam', 'colNames');
                var b = $('#grid-table').jqGrid('getGridParam', 'colModel');
                var bAry=[];
                for(var i=0;i<b.length;i++){
                    bAry.push({name:b[i].name,width:b[i].width});
                }
                localStorage.setItem(localName, a);
                localStorage.setItem(localModeol, JSON.stringify(bAry));
            }, 0)
        });


        var lastsel;
        /**
         * 20180531:优化商品列表查询多次的问题
         * 1.在第一次翻页事件中手动处理翻页事件
         * 2.将grid初始对象提出等
         */
        var defaults = {
            sortable: true,
            jsonReader: {
                root: "result.list",
                page: "result.pageNum",
                total: "result.pages",
                records: "result.total"
            },
            postData: {"productName": $(productName).val()},
            height: 'auto',//高度，表格高度。可为数值、百分比或'auto'
            //mtype:"GET",//提交方式
            colNames: colNames,
            colModel: colModel,
            shrinkToFit: false,
            autoScroll: true,
            viewrecords: true,//是否在浏览导航栏显示记录总数
            rowNum: 50,//每页显示记录数
            rowList: [10, 20, 30, 40, 50],//用于改变显示行数的下拉列表框的元素数组。
            pager: pager_selector,//分页、按钮所在的浏览导航栏
            altRows: true,//设置为交替行表格,默认为false
            //toppager: true,//是否在上面显示浏览导航栏
            multiselect: false,//是否多选
            //multikey: "ctrlKey",//是否只能用Ctrl按键多选
            multiboxonly: false,//是否只能点击复选框多选
            // subGrid : true,
            //sortname:'id',//默认的排序列名
            sortorder: 'asc',//默认的排序方式（asc升序，desc降序）
            //caption: "采购退货单列表",//表名
            autowidth: true,//自动宽,
            scrollrows: true,//是否显示滚动条，是
            onSelectRow: function () { // 单击
                var id = jQuery("#grid-table").jqGrid('getGridParam', 'selrow');
                product_data_obj = $("#grid-table").jqGrid('getRowData', id);
            },
            ondblClickRow: function (id) {  //  双击行时触发  id:  当前行id
                var id = jQuery("#grid-table").jqGrid('getGridParam', 'selrow'); // selrow	string	只读属性，最后选择行的id
                var rowData = $("#grid-table").jqGrid('getRowData', id);
                finalVal = [];
                if (prescriptionYn_obj == 1) {
                    if (rowData.prescriptionYn == 1) {
                        btn_alertDialog('提示', '该药品为处方药，请提取处方！');
                        //alert("该药品为处方药，请提取处方！");
                        return false;
                    }
                }
                check_pihao_len();
            },
            loadComplete: function () {
                //$("#grid-table").closest(".ui-jqgrid-bdiv").css({ 'overflow-y' : 'scroll' });
                var allRowIds = $("#grid-table").jqGrid('getDataIDs'); // 表格默认第一行 选中状态
                $("#grid-table").setSelection(allRowIds[0]);

                var ta = $('#grid-table>tbody>tr')[1];
                //console.log($(ta))
                setTimeout(function () {
                    $(ta).trigger('focus')
                }, 10)
                var table = $(this);
                setTimeout(function () {
                    updatePagerIcons(table);
                }, 0);
            },
            onPaging: function (e) {
                /**
                 * 20180531:优化商品列表查询多次的问题
                 * 在第一次翻页事件中手动处理翻页事件
                 */
                if (grid_data) {
                    var defaults = $.extend(dataSource, {
                        postData: {
                            "productName": $("#productName").val()
                        }, page: 1
                    });
                    if (e == 'next') {
                        defaults = $.extend(defaults, {page: 2});
                    } else if (e == 'last') {
                        defaults = $.extend(defaults, {page: parseInt((grid_data.result.total + 20 - 1) / 20)});
                    }
                    setTimeout(function () {
                        $("#grid-table").jqGrid('setGridParam', defaults).trigger('reloadGrid');
                    }, 0)

                }
                grid_data = null;
            }
        }, localData = dataSource;
        if (grid_data && grid_data.result.list) {
            var a = grid_data.result.list;
            if (grid_data.result.total > 20) {
                setTimeout(function () {
                    $('#next_grid-pager').removeClass('ui-state-disabled');
                    $('#last_grid-pager').removeClass('ui-state-disabled');
                    $('.ui-paging-info').text('第1到第20条　共 ' + grid_data.result.total + ' 条');
                    var lastPage = parseInt((grid_data.result.total + 20 - 1) / 20);
                    $('#sp_1_grid-pager').text(lastPage);

                    //$("#grid-table").jqGrid('setGridParam', {records:315 }).trigger('reloadGrid');
                }, 0)
                //console.log('grid_data为真，手动解除页数事件限制');
            }
            localData = {data: a, datatype: "local"};
        }
        ;

        $("#grid-table").jqGrid($.extend(defaults, localData));


        $("#grid-table").jqGrid('bindKeys', {
            "onEnter": function (rowid) {
                //alert("你enter了一行， id为:"+rowid)
            }
        });


        $("#historyBtn").click(function () {
            $("#grid-table").jqGrid('setGridParam', {
                postData: {
                    "handover": $("#handover").val()
                }, page: 1
            }).trigger('reloadGrid');
        });

        $("#queryBtn").click(function () {
            getGoods();
            grid_data = null;
        });
        $('#productName').on('keyup', function (e) {
            if (e.keyCode == 13) {
                getGoods();
                return false;
            }
        })

        function getGoods() {
           // console.log(dataSource);

            $("#grid-table").jqGrid('setGridParam', $.extend(dataSource, {
                postData: {
                    "productName": $("#productName").val()
                }, page: 1
            })).trigger('reloadGrid');
        }
    });

    function open_pihao_dialog(_rowData) {
        // 双击当前行，拿到当前行的ID 值。关闭modal  打开下一个modal .并将ID 值传递过去
        //var dialog1 = parent.dialog.get(window);
        parent.dialog({
            url: '/order/orderinfo/toOrderChooseBatchNoView',
            title: '选择批号',
            width: w_width,
            height: w_height,
            data: {"product_obj":_rowData,"dataNumberArr": queryData.dataNumberArr}, // 给modal 要传递的 的数据
            onclose: function () {
                if (this.returnValue) {
                    //$('#pos_date').html("这里是modal 返回的值  " + this.returnValue);
                    //console.log('选择批号 modal  返回的值  ：  ' + this.returnValue)
                    dialog.close(this.returnValue).remove()
                }
            }
        }).showModal();
        return false;
    }
</script>
</html>
