<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta charset="utf-8" />
        <title>Dashboard - Ace Admin</title>

        <meta name="description" content="overview &amp; stats" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
        <#include "/common/top-common.ftl"/>
        <style type="text/css">
            .ui-jqgrid-view{overflow: auto; }/* max-height: 200px;  overflow-x: hidden !important; */
            #queryBtn{border-radius: 4px;}
            .input-group-addon:first-child {min-width: inherit;}
            .btn_sub_can_div{bottom: 5px !important;}
            @media (max-width: 768px) {
                #gbox_grid-table001{overflow: auto; overflow-y: hidden; }/*   max-height: 75px;overflow: auto; overflow-x: hidden;*/
            }
            .panel{box-shadow: none;}
            #b_table001{overflow-x: auto;overflow-y: hidden;}/*    overflow: auto;*/
            #b_table001 .ui-jqgrid-bdiv>div{    height: 50px;overflow: auto;overflow-x: hidden;}
            #grid-table001{ overflow-y: hidden;}
        </style>

    </head>
	<body id="innerBody" style="">
		<div style="">
		    <div class="row">
        			<div class="col-sm-4 col-xs-4 marginTop5">
        				<div class="input-group">
        					<label class="input-group-addon">起始日期:</label>
        					<input type="text" name="startDate" id="startDate" class="Wdate form-control"   onfocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',maxDate:'#F{$dp.$D(\'endDate\')}'})" style="height: inherit;"/>
        				</div>
        			</div>
        			<div class="col-sm-4 col-xs-4 marginTop5">
        				<div class="input-group">
        					<label class="input-group-addon">结束日期:</label>
        					<input type="text" name="endDate" id="endDate" class="Wdate form-control"  onfocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'#F{$dp.$D(\'startDate\')}'})" style="height: inherit;"/>
        				</div>
        			</div>
                    <div class="col-sm-4 col-xs-4 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon">小&nbsp;&nbsp;票&nbsp;&nbsp;号:</label>
                            <input type="text" id="ticketNo" name="ticketNo" class="form-control">
                        </div>
                    </div>
                    <div class="col-sm-4 col-xs-4 marginTop5">
                        <div class="input-group">
                            <label class="input-group-addon">商&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;品:</label>
                            <input type="text" id="productName" name="productName" class="form-control">
                        </div>
                    </div>
                    <div class="col-sm-3 col-xs-4 marginTop5 control-group">
                        <button id="queryBtn" class="btn btn-redactbag">  查询</button>
                    </div>
        		</div>

		</div>
        <div  class="panel panel-default publicPanel" id="b_table"  ><#--id="b_table"  style="height: 40%;"-->
            <table id="grid-table"></table>
            <div id="grid-pager" style=""></div>
            <#-- <table id="grid-table001"></table>-->
            <#--<div class="dataTables_wrapper form-inline no-footer saasTableWrap marginTop5" style="height: 120px; overflow: auto;">-->
            <#--<table id="grid-table001"></table>-->
            <#--</div>-->
        </div>
        <div class="panel panel-default publicPanel" id="b_table001" style="position: absolute; height: 100px; bottom: 6%; width: 100%;">
            <table id="grid-table001"></table>
        </div>
        <div class="text-right btn_sub_can_div " style=" ">
            <button type="button"  class="btn btn-redactbag" >确定</button>
            <button type="button"  class="btn btn-return"  >取消</button>
        </div>
	</body>
	<script type="text/javascript">
            $.jgrid.col = { caption: "筛选列", bSubmit: "确定", bCancel: "取消" };
                //当 datatype 为"local" 时需填写
            var grid_selector = "#grid-table";
            var pager_selector = "#grid-pager";
            var order_item_id_array = [];
            var sale_id_type_code = {};

            window.console = window.console || {
                        log: function() {}
                    }
            $(function() {
                var dialog = parent.dialog.get(window);
                var data = dialog.data; // 获取对话框传递过来的数据
//                $('#date_start_inp').val(data);

                $('.btn_submit').on('click', function() {

                    var gridTableRowId = $("#grid-table").jqGrid('getGridParam','selrow');
                    var gridTableRowData = $("#grid-table").jqGrid('getRowData',gridTableRowId);
                    var ids = jQuery("#grid-table001").jqGrid('getGridParam', 'selarrrow');
                    if(ids){
                        $(ids).each(function(index,item){
                            var rowData = $("#grid-table001").jqGrid('getRowData',item);
                            order_item_id_array.push(rowData);
                        });
                    }
                    var orderVo = {

                        "order_item_id_array":order_item_id_array,
                        "order_id":gridTableRowData

                    }
//                    var val = $('#date_start_inp').val();
                    if(order_item_id_array.length ){
                        dialog.close(orderVo);
                        dialog.remove();
                    }else{
                        btn_alertDialog('提示',' 没有选中数据');
                        return false;
                    }
                });

                $.ajax({
                    type:'post',
                    url:'/userrole/getSaleUser',
                    data:{},
                    async: false,
                    success:function(res){
                        if(res){
                            if(res.result){
                                if(res.result.length>0){
                                    sale_id_type_code = res.result.reduce(function(result,curr){
                                        result[curr.id]=curr.realName;
                                        return result;
                                    },{});
                                    sale_id_type_code = res.result.reduce(function(result,curr){
                                        result[curr.id]=curr.realName;
                                        return result;
                                    },{});
                                }
                            }
                        }
                    },
                    error:function(err){
                    }
                })

                //modal  关闭
                $('.btn_modalClose').click(function(){
                    dialog.close().remove()
                })
                $('.btn_modalClose').focus();
            });

            $(document).keyup(function(event){
                switch(event.keyCode)  {
                    case 27:
                        event.keyCode = 0;
                        event.cancelBubble = true;
                        event.preventDefault();
                        $('.btn_modalClose').click(); // ESC  取消按钮
                        return false;
                        break;
                    case 13:
                        event.keyCode = 0;
                        event.cancelBubble = true;
                        event.preventDefault();
                        $('.btn_submit').click(); //Enter 确定按钮
                        return false;
                        break;
                }
            });
             $(document).ready(function () {
             	var lastsel;
             	var startDate = $("#startDate").val();
             	var endDate = $("#endDate").val();
             	var productName = $("#productName").val();
             	var ticketNo = $("#ticketNo").val();
                $("#grid-table").jqGrid({
                		sortable: true,
                        url:"/order/orderinfo/getOrderReturnGoodsVoView",
                        jsonReader : {
                            root:"result.list",
                            page: "result.pageNum",
                            total: "result.pages",
                            records: "result.total"
                        },
//                      data: grid_data,//当 datatype 为"local" 时需填写
                        datatype: "json", //数据来源，本地数据（local，json,jsonp,xml等）
                        height: "auto",//高度，表格高度。可为数值、百分比或'auto'
                        //mtype:"GET",//提交方式
                        colNames: ['id','小票号', '日期', '时间','时段','销售员','应收金额','实收金额','优惠金额','找零金额','成本金额','毛利','会员名称','会员id','会员手机号','会员积分','会员折扣','会员价格策略'],
                        colModel: [{
                         name: 'id',
                         index: 'id',//索引。其和后台交互的参数为sidx
                         //width: 100,
                         editable: true,
                         hidden : true,
                         edittype:"input",
                         editoptions: {
                             size: "20",
                             maxlength: "30"
                         }
                     },{
                            name: 'ticketNo',
                            index: 'ticketNo',//索引。其和后台交互的参数为sidx
                            key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                            width: 100,
                            editable: true,
                            edittype:"input",
                            editoptions: {
                                size: "20",
                                maxlength: "30"
                            }
                        }, {
                            name: 'createTime',
                            index: 'createTime',
                            width: 100,//宽度
                            editable: true,//是否可编辑
                            edittype: "select", //可以编辑的类型。可选值：text, textarea, select, checkbox, password, button, image and file.s

                        }, {
                            name: 'createTime',
                            index: 'createTime',
                            width: 60,
                            sorttype: "double",
                            editable: true,
                            formatter:function(v){
                                var time=v;
                                if(time && time.split(' ').length>=1){
                                    time=time.split(' ')[1];
                                }else{
                                    time='00:00:00';
                                }
                                return time;
                            }
                        }, {
                            name: 'createTime',
                            index: 'createTime',
                            width: 60,
                            sorttype: "double",
                            editable: true,
                              formatter:function(v){
                                  var time= v;
                                  if(time && time.split(' ').length>=1){
                                      time=time.split(' ')[1].split(':')[0];
                                  }else{
                                      time='00:00:00';
                                  }
                                  time+=':00:00'
                                  return time;
                              }
                        }, {
                            name: 'sellerId',
                            index: 'sellerId',
                            width: 60,
                            sorttype: "double",
                            editable: true,
                            formatter: function (cellvalue) {
                                var str='';
                                if(cellvalue!=null&&cellvalue!='' && cellvalue != -1){
                                    str = sale_id_type_code[cellvalue];
                                    if(str == undefined){
                                        str = "";
                                    }
                                }
                                return str;
                            },
                            unformat: function (cellvalue, options, cell) {
                                var str='';
                                if(cellvalue!=null&&cellvalue!=''&& cellvalue != -1){
                                    str = sale_id_type_code[cellvalue];
                                    if(str == undefined){
                                        str = "";
                                    }
                                }
                                return str;
                            }
                        }, {
                            name: 'receivableAmount',
                            index: 'receivableAmount',
                            width: 60,
                            sorttype: "double",
                            editable: true
                        }, {
                            name: 'actualAmount',
                            index: 'actualAmount',
                            width: 60,
                            sorttype: "double",
                            editable: true
                        }, {
                            name: 'discountAmount',
                            index: 'discountAmount',
                            width: 60,
                            sorttype: "double",
                            editable: true
                        }, {
                            name: 'changeAmount',
                            index: 'changeAmount',
                            width: 60,
                            sorttype: "double",
                            editable: true
                        }, {
                            name: 'costAmount',
                            index: 'costAmount',
                            hidden:true,
                            sorttype: "double",
                            editable: true
                        }, {
                            name: 'grossProfitAmount',
                            index: 'grossProfitAmount',
                            hidden:true,
                            sorttype: "double",
                            editable: true
                        }, {
                            name: 'memberName',
                            index: 'memberName',
                            editable: true
                        }, {
                            name: 'memberId',
                            index: 'memberId',
                            hidden:true,
                            width: 60,
                            editable: true
                        }, {
                            name: 'phoneNumber',
                            index: 'phoneNumber',
                            hidden:true,
                            editable: true
                        }, {
                            name: 'point',
                            index: 'point',
                            hidden:true,
                            sorttype: "double",
                            editable: true
                        }, {
                            name: 'discount',
                            index: 'discount',
                            hidden:true,
                            sorttype: "double",
                            editable: true
                        }, {
                            name: 'priceStrategy',
                            index: 'priceStrategy',
                            hidden:true,
                            editable: true
                        }],
				        shrinkToFit:false,
						//autoScroll: true,
                        viewrecords: true,//是否在浏览导航栏显示记录总数
                        rowNum: 50,//每页显示记录数
                        rowList: [10, 20, 30,40,50],//用于改变显示行数的下拉列表框的元素数组。
                        pager: pager_selector,//分页、按钮所在的浏览导航栏
                        altRows: true,//设置为交替行表格,默认为false
                        //toppager: true,//是否在上面显示浏览导航栏
                        multiselect: false,//是否多选
                        rownumbers: true,
                        //multikey: "ctrlKey",//是否只能用Ctrl按键多选
                        multiboxonly: false,//是否只能点击复选框多选
                        // subGrid : true, 
                        //sortname:'id',//默认的排序列名
                        sortorder:'asc',//默认的排序方式（asc升序，desc降序）
                        //caption: "采购退货单列表",//表名
                        //autowidth: true,//自动宽
					    loadComplete: function() {
							var table = $(this);
							setTimeout(function(){
							updatePagerIcons(table);
							}, 0);
                            var allRowIds = $("#grid-table").jqGrid('getDataIDs'); // 表格默认第一行 选中状态
                            $("#grid-table").setSelection(allRowIds[0])
                            var ta = $('#grid-table>tbody>tr')[1];

                            setTimeout(function(){
                                $(ta).trigger('focus')
                            },10)
						},
                        onSelectRow:function(){ // 单击
                            var id = jQuery("#grid-table").jqGrid('getGridParam', 'selrow');
                            var order_obj = $("#grid-table").jqGrid('getRowData',id);
                            console.log('二级grid查询ID：',id,order_obj.id);
                            setTimeout(function(){
                                $("#grid-table001").jqGrid('setGridParam', {
                                    postData: {
                                        "orderId": order_obj.id
                                    },page:1
                                }).trigger('reloadGrid');
                                if (order_obj.priceStrategy == '2') {
                                    //==2  会员价
                                    $("#grid-table001").setGridParam().hideCol("productRetailPrice").trigger("reloadGrid");//隐藏零售价
                                    $("#grid-table001").setGridParam().showCol("productMemberPrice").trigger("reloadGrid");//展示会员价
                                } else {
                                    // or 零售价
                                    $("#grid-table001").setGridParam().showCol("productRetailPrice").trigger("reloadGrid");//展示零售价
                                    $("#grid-table001").setGridParam().hideCol("productMemberPrice").trigger("reloadGrid");//隐藏会员价
                                }
                            },500)

                        }
                });
                 $("#grid-table").jqGrid('bindKeys', {"onEnter":function( rowid ) {
                     //alert("你enter了一行， id为:"+rowid)
                 }} );
//			    var grid_data001 = [
//	                {goodId: "商品编码",goodName: "商品名称",goodFormat: "商品规格",
//	                factory:"生产厂家",produPlace:"产地",isRecipeCheck:'是否处方',
//	                isHemp:'含特殊药品复方制剂',stock:'批号',orgNumber:'原单数量',ketuiNum:'可退数量',
//	                kucunNum:'批号库存数量',retailPrice:'零售价',factPrice:'实际售价',discount:'扣率',
//	                theoryMoney:"应收金额",factMoney:"实收金额",costMoney:'成本金额',gross:'毛利'}
//	            ];
            

			    $("#grid-table001").jqGrid({
                        url:"/order/orderDetail/getOrderReturnGoodsItemQuery",
                        sortable: true,
                        jsonReader : {
                            root:"result",
                            page: "result.pageNum",
                            total: "result.pages",
                            records: "result.total"
                        },
//                        data: grid_data001,//当 datatype 为"local" 时需填写
                        datatype: "json", //数据来源，本地数据（local，json,jsonp,xml等）
                        height: "auto",//高度，表格高度。可为数值、百分比或'auto'
                        //mtype:"GET",//提交方式
                        colNames: ['id','状态','商品编码', '通用名','商品名称', '商品规格','生产厂家','产地','是否处方','含特殊药品复方制剂','批号','原单数量','可退数量','批号库存数量','成本价','零售价','会员价','实际售价','扣率','应收金额','实收金额','成本金额','毛利','总库存数量','单位','优惠金额','处方分类','明细id','过期时间'],
                        colModel: [
                        {
                            name: 'id',
                            index: 'id',//索引。其和后台交互的参数为sidx
//                            key: true,//当从服务器端返回的数据中没有id时，将此作为唯一rowid使用只有一个列可以做这项设置。如果设置多于一个，那么只选取第一个，其他被忽略
                            width: 100,
                            editable: true,
                            edittype:"input",
                            hidden:true,
                            editoptions: {
                                size: "20",
                                maxlength: "30"
                            }
                        },{
                            name: 'promotionType',
                            index: 'promotionType',//索引。其和后台交互的参数为sidx
                            width: 100,
                            editable: true,
                            edittype:"input",
                            hidden : true,
                            formatter: function (cellvalue) {
                                var item = '正常';
                                if (cellvalue == '0') {
                                    item = '正常';
                                } else if (cellvalue === '1') {
                                    item = "满折";
                                }
                                else if (cellvalue === '2') {
                                    item = "满送";
                                }
                                else if (cellvalue === '3') {
                                    item = "会员日";
                                }
                                else if (cellvalue === '4') {
                                    item = "限量特价";
                                }
                                return item;
                            },
                            unformat: function (cellvalue, options, cell) {
                                var item = '';
                                if (cellvalue === '正常') {
                                    item = 0;
                                } else if (cellvalue === "满折") {
                                    item = 1;
                                }else if (cellvalue === "满送") {
                                    item = 2;
                                }else if (cellvalue === "会员日") {
                                    item = 3;
                                }else if (cellvalue === "限量特价") {
                                    item = 4;
                                }
                                return item;
                            }
                        },{
                            name: 'productCode',
                            index: 'productCode',//索引。其和后台交互的参数为sidx
                            width: 100,
                            editable: true,
                            edittype:"input",
                            editoptions: {
                                size: "20",
                                maxlength: "30"
                            }
                        },{
                            name: 'commonName',
                            index: 'commonName',
                            width: 200,//宽度
                            editable: true,//是否可编辑
                            edittype: "select"
                        }, {
                            name: 'productName',
                            index: 'productName',
                            width: 200,//宽度
                            editable: true,//是否可编辑
                            edittype: "select"
                        }, {
                            name: 'productSpecifications',
                            index: 'productSpecifications',
                            width: 60,
                            sorttype: "double",
                            editable: true
                        }, {
                            name: 'productManufacturer',
                            index: 'productManufacturer',
                            width: 60,
                            sorttype: "double",
                            editable: true
                        }, {
                            name: 'productOriginAddress',
                            index: 'productOriginAddress',
                            width: 60,
                            sorttype: "double",
                            editable: true
                        }, {
                            name: 'prescriptionYn',
                            index: 'prescriptionYn',
                            width: 60,
                            sorttype: "double",
                            editable: true,
                            formatter:function(cellvalue){
                                var str='';
                                if(cellvalue){
                                    str='是';
                                }else {
                                    str='否';
                                }
                                return str;
                             },
                            unformat: function (cellvalue, options, cell) {//保存时转义回来
                                var item = '';
                                if (cellvalue == '否') {
                                    item = 0;
                                } else if (cellvalue == "是") {
                                    item = 1;
                                }
                                return item;
                            }
                        },
                        {
                            name: 'bastYn',
                            index: 'bastYn',
                            width: 60,
                            sorttype: "double",
                            editable: true,
                            formatter:function(cellvalue){
                                var str='';
                                if(cellvalue){
                                    str='是';
                                }else {
                                    str='否';
                                }
                                return str;
                             },
                            unformat: function (cellvalue, options, cell) {//保存时转义回来
                                var item = '';
                                if (cellvalue == '否') {
                                    item = 0;
                                } else if (cellvalue == "是") {
                                    item = 1;
                                }
                                return item;
                            }

                        },
                        {
                            name: 'batchNo',
                            index: 'batchNo',
                            width: 60,
                            sorttype: "double",
                            editable: true
                        },

                        {
                            name: 'productNum',
                            index: 'productNum',
                            width: 60,
                            sorttype: "double",
                            editable: true,
                            formatter: function (cellvalue) {
                                var item = '';
                                if (cellvalue != '') {
                                    item = 0 - cellvalue;
                                }
                                return item;
                            },
                            unformat: function (cellvalue, options, cell) {
                                var item = '';
                                if (cellvalue != '') {
                                    item = 0 - cellvalue;
                                }
                                return item;
                            }
                        },
                        {
                            name: 'refundableNumber',
                            index: 'refundableNumber',
                            width: 60,
                            sorttype: "double",
                            editable: true
                        },
                        {
                            name: 'stockNumber',
                            index: 'stockNumber',
                            width: 60,
                            sorttype: "double",
                            editable: true
                        },
                        {
                            name: 'costPrice',
                            index: 'costPrice',
                            width: 60,
                            sorttype: "double",
                            editable: true
                        },
                        {
                            name: 'productRetailPrice',
                            index: 'productRetailPrice',
                            width: 60,
                            sorttype: "double",
                            editable: true
                        },
                        {
                            name: 'productMemberPrice',
                            index: 'productMemberPrice',
                            width: 60,
                            hidden:true,
                            editable: true
                        },
                        {
                            name: 'actualUnitAmount',
                            index: 'actualUnitAmount',
                            width: 60,
                            sorttype: "double",
                            editable: true
                        },
                        {
                            name: 'discount',
                            index: 'discount',
                            width: 60,
                            sorttype: "double",
                            editable: true
                        },
                        {
                            name: 'receivableAmount',
                            index: 'receivableAmount',
                            width: 60,
                            sorttype: "double",
                            editable: true,
                            formatter: function (cellvalue) {
                                var item = '';
                                if (cellvalue != '') {
                                    item = 0 - cellvalue;
                                }
                                return item;
                            },
                            unformat: function (cellvalue, options, cell) {
                                var item = '';
                                if (cellvalue != '') {
                                    item = 0 - cellvalue;
                                }
                                return item;
                            }
                        }, {
                            name: 'actualAmount',
                            index: 'actualAmount',
                            width: 60,
                            sorttype: "double",
                            editable: true,
                            formatter: function (cellvalue) {
                                var item = '';
                                if (cellvalue != '') {
                                    item = 0 - cellvalue;
                                }
                                return item;
                            },
                            unformat: function (cellvalue, options, cell) {
                                var item = '';
                                if (cellvalue != '') {
                                    item = 0 - cellvalue;
                                }
                                return item;
                            }
                        }, {
                            name: 'productCostAmount',
                            index: 'productCostAmount',
                            //width: 60,
                            hidden:true,
                            sorttype: "double",
                            editable: true
                        }, {
                            name: 'grossProfitAmount',
                            index: 'grossProfitAmount',
                            //width: 60,
                            hidden:true,
                            sorttype: "double",
                            editable: true
                        },{
                            name: 'productStockNumber',
                            index: 'productStockNumber',
                            //width: 60,
                            hidden:true,
                            sorttype: "double",
                            editable: true
                        },{
                            name: 'productUnit',
                            index: 'productUnit',
                            //width: 60,
                            hidden:true,
                            editable: true
                        },{
                            name: 'discountAmount',
                            index: 'discountAmount',
                            //width: 60,
                            hidden:true,
                            sorttype: "double",
                            editable: true
                        },{
                            name: 'prescriptionClassification',
                            index: 'prescriptionClassification',
                            //width: 60,
                            hidden:true,
                            editable: true
                        },{
                            name: 'orderItemId',
                            index: 'orderItemId',
                                key: true,
                            //width: 60,
                            hidden:true,
                            editable: true
                        },{
                            name: 'expirationDate',
                            index: 'expirationDate',
                            //width: 60,
                            hidden:true,
                            editable: true
                        }],
                        shrinkToFit:false,
                        autoScroll: true,
                        viewrecords: true,//是否在浏览导航栏显示记录总数
                        rowNum: 200,//每页显示记录数
                        rowList: [10, 20, 30],//用于改变显示行数的下拉列表框的元素数组。
                        //pager: pager_selector,//分页、按钮所在的浏览导航栏
                        altRows: true,//设置为交替行表格,默认为false
                        //toppager: true,//是否在上面显示浏览导航栏
                        multiselect: true,//是否多选
                        //multikey: "ctrlKey",//是否只能用Ctrl按键多选
                        multiboxonly: true,//是否只能点击复选框多选
                        // subGrid : true,
                        //sortname:'id',//默认的排序列名
                        sortorder:'asc',//默认的排序方式（asc升序，desc降序）
                        //caption: "采购退货单列表",//表名
                        //autowidth: true,//自动宽
                        loadComplete: function() {
                            var table = $(this);
                            setTimeout(function(){
                            updatePagerIcons(table);
                            }, 0);
                        },
                        onSelectRow:function(){ // 单击
                            var ids = jQuery("#grid-table001").jqGrid('getGridParam', 'selarrrow');
                            if(ids){
                                $(ids).each(function(){

                                });
                            }
//
                        }
                });



                 function getData(){
                        $("#grid-table").jqGrid('setGridParam', {
                         postData: {
                             "startDate": $("#startDate").val(),
                             "endDate": $("#endDate").val(),
                             "productName": $("#productName").val(),
                             "ticketNo": $("#ticketNo").val()
                         },page:1
                     }).trigger('reloadGrid');

                     $("#grid-table001").jqGrid('setGridParam', {postData: {"orderId":""},page:1}).trigger('reloadGrid');
                     var id = jQuery("#grid-table").jqGrid('getGridParam', 'selrow');
                     var order_obj = $("#grid-table").jqGrid('getRowData',id);
                     //根据是否会员切换显示字段
                     if (order_obj.priceStrategy == '2') {
                         //==2  会员价
                         $("#grid-table001").setGridParam().hideCol("productRetailPrice").trigger("reloadGrid");//隐藏零售价
                         $("#grid-table001").setGridParam().showCol("productMemberPrice").trigger("reloadGrid");//展示会员价
                     } else {
                         // or 零售价
                         $("#grid-table001").setGridParam().showCol("productRetailPrice").trigger("reloadGrid");//展示零售价
                         $("#grid-table001").setGridParam().hideCol("productMemberPrice").trigger("reloadGrid");//隐藏会员价
                     }
                 }

                /* 查询按钮 */
                $("#queryBtn").click(function(){
                    getData();
                });

                $('#startDate').on('keyup',function(e){
                    if(e.keyCode==13){
                        getData();
                        return false;
                    }

                });
                $('#endDate').on('keyup',function(e){
                   if(e.keyCode==13){
                                           getData();
                                           return false;
                                       }
                });
                $('#ticketNo').on('keyup',function(e){
                    if(e.keyCode==13){
                                            getData();
                                            return false;
                                        }
                });
                $('#productName').on('keyup',function(e){
                    if(e.keyCode==13){
                                            getData();
                                            return false;
                                        }
                });
            });
        </script>
</html>
