<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta charset="utf-8"/>
    <link rel="icon" href="/static/assets/images/login/favicon.ico" type="image/x-icon"/>
    <title></title>
    <meta name="description" content="overview &amp; stats"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>
    <link rel="stylesheet" href="/static/assets/css/index.css?v=${jsVersion}"/>
    <#include "/common/top-common.ftl"/>
    <style>
        .dropdown {
            width: 100%;
        }

        .select-btn {
            text-align: left;
        }

        .select-single {
            text-align: center;
        }

        .btn-group {
            height: 36px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            position: relative;
        }

        .shownAllToggle {
            position: absolute;
            left: 0;
        }

        .btn {
            margin: 0 0 0 10px !important;
        }

        .togglg {
            display: none;
        }
    </style>
</head>
<body id="innerBody">

<div id="toolbox" class="toolbox newToolbox">
    <button id="downloadExcel" type="button" class="btn btn-redactbag">导出Excel</button>
</div>
<div id="searchbox" class="panel panel-default publicPanel newPanel">
    <form class="commonValidate row" id="searchForm">
        <input type="hidden" id="handover" name="handover" value="${orderRetailShiftHistorySum.handover!""}">
        <input type="hidden" id="dateStart" name="dateStart" value="${orderRetailShiftHistorySum.dateStart!""}">
        <input type="hidden" id="dateEnd" name="dateEnd" value="${orderRetailShiftHistorySum.dateEnd!""}">
        <input type="hidden" id="exportInputType" name="exportInputType" value="${orderRetailShiftHistorySum.exportInputType!""}">
        <input type="hidden" id="exportCnName" name="exportCnName" value="${orderRetailShiftHistorySum.exportCnName!""}">
        <input type="hidden" id="exportEnName" name="exportEnName" value="${orderRetailShiftHistorySum.exportEnName!""}">
        <input type="hidden" id="typeFields" name="typeFields" value="${orderRetailShiftHistorySum.typeFields!""}">
        <input type="hidden" id="collectFields" name="collectFields" value="${orderRetailShiftHistorySum.collectFields!""}">

        <div class="col-xs-4 marginTop5  pull-right" id="btnBoxer">
            <div class="input-group btn-group">
                <a href="javascript:0" class="shownAllToggle">展开全部</a>
                <button type="reset" class="btn btn-return" id="btnReset">重置</button>
                <button type="button" class="btn btn-redactbag" id="btnSearch">查询</button>
            </div>
        </div>
    </form>
</div>

<div class="panel panel-default publicPanel" id="b_table">
    <table id="grid-table"></table>
    <div id="grid-pager" style="margin-top: 20px !important;"></div>
</div>
</body>
<script src="/static/js/order/orderRetailShiftHistorySum.js?v=${jsVersion}"></script>
</html>