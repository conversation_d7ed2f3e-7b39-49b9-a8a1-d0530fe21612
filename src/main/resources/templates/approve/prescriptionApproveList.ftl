<!DOCTYPE html>
<html lang="en">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>Dashboard - Ace Admin</title>
		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
		<#include "/common/top-common.ftl"/>
	</head>

	<body id="innerBody">
		<!-- 工具栏 -->
		<div id="toolbox" class="toolbox newToolbox">
			<button type="button" class="btn btn-redactbag" id="passBth">审批通过</button>
			<button type="button" class="btn btn-removebag" id="rejectBth">驳回申请</button>
		</div>
		<div class="panel panel-default publicPanel newPanel">
			<form class="commonValidate">
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">开始日期</label>
						<input type="text" id="startDate" lit="seach" class="form-control  Wdate" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endDate\');}'})" style="height: inherit;">
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">结束日期</label>
						<input type="text" id="endDate" lit="seach" class="form-control  Wdate" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startDate\');}'})" style="height: inherit;">
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">单据编号</label>
						<input type="text" id="billNo" lit="seach" class="form-control">
					</div>
				</div>
				<div class="col-xs-3 marginTop5">
					<div class="input-group">
						<label class="input-group-addon">发起人</label>
						<input type="text" id="initiatorName" lit="seach" class="form-control">
					</div>
				</div>
				<div class="col-xs-12 marginTop5">
					<div class="input-group">
						<button id="queryBtn" type="button" class="btn btn-redactbag forbidRepeatSearch">  查询</button>
						<button id="cancel" type="button" class="btn btn-return forbidRepeatSearch">   重置</button>
                        <#--<button  class="btn btn-redactbag"  id="filtrkolumn">筛选列</button>-->
					</div>
				</div>
			</form>
		</div>
		<div class="panel panel-default publicPanel" id="b_table">
			<table id="grid-table"></table>
			<div id="grid-pager" style="margin-top: 20px !important;"></div>
		</div>
	</body>
    <script src="/static/js/approve/prescriptionApproveList.js?v=${jsVersion}"></script>

</html>

