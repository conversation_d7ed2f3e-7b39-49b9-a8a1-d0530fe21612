<!DOCTYPE html>
<html lang="en" style="overflow-y:scroll;">
	<head>
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
		<meta charset="utf-8" />
		<title>Dashboard - Ace Admin</title>

		<meta name="description" content="overview &amp; stats" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />

		<#include "/common/top-common.ftl"/>
		<style>
		 	#innerBody .newPanel .box_zll{
				padding:0 0 0 0;
			}
		</style>
	</head>
    <style>
        .panel.label280 .input-group .input-group-addon,
        .label280 .input-group .input-group-addon{
            width: 280px;
        }
    </style>
	<body id="innerBody">
		<!-- 工具栏 -->
		<div id="dictionary_tools" class="toolbox">
            <button id="goback"  class="btn btn-return">  返回</button>
		</div>

			<div class="panel panel-default publicPanel newPanel" style="overflow: hidden; padding:0 !important; margin-bottom: 0;">
				<div id="iframe_c" style="margin-top: -10px;background: #e4e6e9;"></div>
			</div>
		<div class="panel panel-default newPanel">
			<div class="box_zll">
				<form class="validate" action="save" method="post" style="display:none">
				<input type="hidden" value="${result.id?c}" id="proId">
					<div class="row lineBorder">
		        		<div class="col-sm-3 marginTop5">
		                    <div class="input-group">
		                        <label class="input-group-addon">单据编号</label>
		                        <input type="text" value="${fistproduct.pref!""}" disabled class="form-control">
		                    </div>
		                </div>
		                <div class="col-sm-3 marginTop5">
		                    <div class="input-group">
		                        <label class="input-group-addon">开票日期</label>
		                        <input type="text" value="${fistproduct.createTimeStr!""}" disabled class="form-control">
		                    </div>
		                </div>
		                <div class="col-sm-3 marginTop5">
		                    <div class="input-group">
		                        <label class="input-group-addon">开票员</label>
		                        <input type="text" value="${fistproduct.createUser!""}" disabled class="form-control">
		                    </div>
		                </div>
		        	</div>
					<div class="row">
                        <div class="col-sm-3 marginTop5" hidden>
                            <div class="input-group">
                                <label class="input-group-addon">商品ID</label>
								<input readonly type="text" name="id" value=${result.id?c} class="form-control">
                            </div>
                        </div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">商品编号</label>
								<input readonly type="text" name="pharmacyPref" value="${result.pharmacyPref!""}" class="form-control" id="pref">
                                <input readonly type="hidden" name="pref" value="${result.pref!""}" class="form-control">
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon"><span class="danger">*</span>通用名称</label>
								<input type="text" disabled class="form-control" name="commonName" value="${result.commonName!""}" required="required" data-msg-required='该字段为必填项'>
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">商品名称</label>
								<input type="text" disabled name="productName" value="${result.productName!""}" class="form-control">
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">标准库ID</label>
								<input readonly type="text" disabled name="standardLibraryId" value="${result.standardLibraryId!""}" class="form-control " data-title="标准库ID">
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon"><span class="danger">*</span>规格/型号</label>
								<input type="text" disabled name="attributeSpecification" value="${result.attributeSpecification!""}" class="form-control" required="required" data-msg-required='该字段为必填项'>
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon"><span class="danger">*</span>单位</label>
								<input type="text" disabled value="${fistproduct.unitName!""}" class="form-control">
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon"><span class="danger">*</span>剂型</label>
								<input type="text" disabled value="${fistproduct.dosageFormName!""}" class="form-control">
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">条形码</label>
								<input type="text" disabled name="barCode" value="${result.barCode!""}" class="form-control">
							</div>
						</div>
					</div>
					<div class="row lineBorder">
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon"><span class="danger">*</span>批准文号</label>
								<input type="text" disabled name="approvalNumber" value="${result.approvalNumber!""}" class="form-control" required="required" data-msg-required='该字段为必填项'>
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon"><span class="danger">*</span>生产厂家</label>
								<input type="text" disabled name="manufacturer" value="${result.manufacturer!""}" class="form-control" required="required" data-msg-required='该字段为必填项'>
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon"><span class="danger">*</span>上市许可持有人</label>
								<input type="text" disabled name="manufacturer" value="${result.drugPermissionPerson!""}" class="form-control" required="required" data-msg-required='该字段为必填项'>
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">产地</label>
								<input type="text" disabled name="producingArea" value="${result.producingArea!""}" class="form-control">
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">批准文号有效期</label>
								<input type="text" disabled name="approvalNumberValidity" value="${result.productExtMess.approvalNumberValidity!""}" class="form-control">
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">用法用量</label>
								<input type="text" disabled name="usageAndDosage" value="${result.usageAndDosage!""}" class="form-control">
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">用药提醒</label>
								<input type="text" disabled name="drugRemindId" value="${result.reminder!""}" class="form-control">
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">商品类别</label>
								<input type="text" disabled name="productType" value="${fistproduct.productTypeName!""}" class="form-control">
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">储存条件</label>
								<input type="text" disabled name="productType" value="${fistproduct.storeConditionName!""}" class="form-control">
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">ABC分类</label>
								<input type="text" disabled name="productType" value="${fistproduct.ABCDivdingName!""}" class="form-control">
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">架位</label>
								<input type="text" disabled name="shelfPosition" value="${fistproduct.shelfPosition!""}" class="form-control">
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon"><span class="danger">*</span>含特殊药品复方制剂</label>
								<select name="containingHempYn" disabled class="form-control">
									<option value="1" <#if result.containingHempYn == 1>selected="selected"</#if>>是</option>
									<option value="0" <#if result.containingHempYn == 0>selected="selected"</#if>>否</option>
								</select>
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">处方分类</label>
								<input type="text" disabled  value="${fistproduct.prescriptionClassification!""}" class="form-control">
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">登记处方</label>
								<select name="prescriptionYn" disabled class="form-control">
									<option value="1" <#if result.prescriptionYn == 1>selected="selected"</#if>>是</option>
									<option value="0" <#if result.prescriptionYn == 0>selected="selected"</#if>>否</option>
								</select>
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon"><span class="danger">*</span>经营范围</label>
								<input type="text" disabled  value="${fistproduct.businessScope!""}" class="form-control">
							</div>
						</div>
					</div>
					<div class="row lineBorder">
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">养护类型</label>
								<input type="text" disabled  value="${fistproduct.maintenanceType!""}" class="form-control">
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">商品功能分类</label>
								<input type="text" disabled  value="${fistproduct.productFunctionCatagory!""}" class="form-control">
							</div>
						</div>
                        <div class="col-sm-3 marginTop5">
                            <div class="input-group">
                                <a  class="btn btn-redactbag"  onclick="btn_alertFile()" id="number">暂无附件</a>
                                <input name="imgUrl" id="filepaths" value="${result.imgUrl!""}" type="hidden" >
                            </div>
                        </div>
					</div>
					<div class="row">
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon"><span class="danger">*</span>进项税率</label>
								<input type="text" disabled name="incomeTaxRate" value="${result.incomeTaxRate!""}" class="form-control" required="required" data-msg-required='该字段为必填项'>
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon"><span class="danger">*</span>销项税率</label>
								<input type="text" disabled name="ouputTaxRate" value="${result.ouputTaxRate!""}" class="form-control" required="required" data-msg-required='该字段为必填项'>
							</div>
						</div>
					</div>


                    <div class="row lineBorder">
                        <div class="col-sm-3 marginTop5">
                        </div>
                    </div>
                    <div class="row">
						<div class="col-sm-6 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">质量标准</label>
								<input value= "${result.productExtMess.qualityStandard!""}" name="productExtMess.qualityStandard" type="text" class="form-control" disabled="disabled">
							</div>
						</div>
						<div class="col-sm-6 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">质量标准效期</label>
								<input value= "${result.productExtMess.qualityStandardValidity!""}" name="productExtMess.qualityStandardValidity" type="text" class="form-control" disabled="disabled">
							</div>
						</div>
						<div class="col-sm-6 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">执行标准</label>
								<input value= "${result.productExtMess.executeStandard!""}" name="productExtMess.executeStandard" type="text" class="form-control" disabled="disabled">
							</div>
						</div>
						<div class="col-sm-6 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">执行标准效期</label>
								<input value= "${result.productExtMess.executeStandardValidity!""}" name="productExtMess.executeStandardValidity" type="text" class="form-control" disabled="disabled">
							</div>
						</div>
						<div class="col-sm-6 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">药品注册批件</label>
								<input value= "${result.productExtMess.drugRegisInstructions!""}" name="productExtMess.drugRegisInstructions" type="text" class="form-control" disabled="disabled">
							</div>
						</div>
						<div class="col-sm-6 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">药品注册批件效期</label>
								<input value= "${result.productExtMess.drugRegisInstructionsValidity!""}" name="productExtMess.drugRegisInstructionsValidity" type="text" class="form-control" disabled="disabled">
							</div>
						</div>
                        <div class="col-sm-6 marginTop5">
                            <div class="input-group">
                                <label class="input-group-addon">保健食品许可证 </label>
                                <input value= "${result.productExtMess.healthFoodLicence!""}" name="productExtMess.healthFoodLicence" type="text" class="form-control" disabled="disabled">
                            </div>
                        </div>
                        <div class="col-sm-6 marginTop5">
                            <div class="input-group">
                                <label class="input-group-addon">保健食品许可证效期  </label>
                                <input value= "${result.productExtMess.healthFoodLicenceValidity!""}" name="productExtMess.healthFoodLicenceValidity" class="form-control Wdate" onfocus="WdatePicker()" disabled="disabled">
                            </div>
                        </div>
                        <div class="col-sm-6 marginTop5">
                            <div class="input-group">
                                <label class="input-group-addon">消毒产品生产企业卫生许可证 </label>
                                <input value= "${result.productExtMess.disinfectProFirmSanitationCard!""}" name="productExtMess.disinfectProFirmSanitationCard" type="text" class="form-control" disabled="disabled">
                            </div>
                        </div>
                        <div class="col-sm-6 marginTop5">
                            <div class="input-group">
                                <label class="input-group-addon">消毒产品生产企业卫生许可证效期  </label>
                                <input value= "${result.productExtMess.disinfectProFirmSanitationCardValidity!""}" name="productExtMess.disinfectProFirmSanitationCardValidity" class="form-control Wdate" onfocus="WdatePicker()" disabled="disabled">
                            </div>
                        </div>
                    </div>
					<div class="row">
						<div class="col-sm-6 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">GMP证书 </label>
								<input value= "${result.productExtMess.GMPCertificate!""}" name="productExtMess.GMPCertificate" type="text" class="form-control" disabled="disabled">
							</div>
						</div>
						<div class="col-sm-6 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">GMP证书效期  </label>
								<input value= "${result.productExtMess.GMPCertificateValidity!""}" name="productExtMess.GMPCertificateValidity" class="form-control Wdate" onfocus="WdatePicker()" disabled="disabled">
							</div>
						</div>
						<div class="col-sm-6 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">GMP证书生产日期 </label>
								<input value= "${result.productExtMess.GMPRegisDate!""}" name="productExtMess.GMPRegisDate" class="form-control Wdate" onfocus="WdatePicker()" disabled="disabled">
							</div>
						</div>
					</div>
                    <div class="row">
                        <div class="col-sm-6 marginTop5">
                            <div class="input-group">
                                <label class="input-group-addon">国产特殊用途化妆品卫生许可批件 </label>
                                <input value= "${result.productExtMess.specialCosmeticsSanitationLicence!""}" name="productExtMess.specialCosmeticsSanitationLicence" type="text" class="form-control" disabled="disabled">
                            </div>
                        </div>
                        <div class="col-sm-6 marginTop5">
                            <div class="input-group">
                                <label class="input-group-addon">国产特殊用途化妆品卫生许可批件效期  </label>
                                <input value= "${result.productExtMess.specialCosmeticsSanitationLicenceValidity!""}" name="productExtMess.specialCosmeticsSanitationLicenceValidity" class="form-control Wdate" onfocus="WdatePicker()" disabled="disabled">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6 marginTop5">
                            <div class="input-group">
                                <label class="input-group-addon">国产非特殊化妆品备案登记凭证  </label>
                                <input value= "${result.productExtMess.cosmeticsRegisCard!""}" name="productExtMess.cosmeticsRegisCard" type="text" class="form-control" disabled="disabled">
                            </div>
                        </div>
                        <div class="col-sm-6 marginTop5">
                            <div class="input-group">
                                <label class="input-group-addon">国产非特殊化妆品备案登记凭证效期 </label>
                                <input value= "${result.productExtMess.cosmeticsRegisCardValidity!""}" name="productExtMess.cosmeticsRegisCardValidity" class="form-control Wdate" onfocus="WdatePicker()" disabled="disabled">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6 marginTop5">
                            <div class="input-group">
                                <label class="input-group-addon">进口特殊用途化妆品卫生许可批件 </label>
                                <input value= "${result.productExtMess.importSpecialCosmeticsSanitationLicence!""}" name="productExtMess.importSpecialCosmeticsSanitationLicence" type="text" class="form-control" disabled="disabled">
                            </div>
                        </div>
                        <div class="col-sm-6 marginTop5">
                            <div class="input-group">
                                <label class="input-group-addon">进口特殊用途化妆品卫生许可批件效期  </label>
                                <input value= "${result.productExtMess.importSpecialCosmeticsSanitationLicenceValidity!""}" name="productExtMess.importSpecialCosmeticsSanitationLicenceValidity" class="form-control Wdate" onfocus="WdatePicker()" disabled="disabled">
                            </div>
                        </div>
                    </div>
                    <div class="row  lineBorder">
                        <div class="col-sm-6 marginTop5">
                            <div class="input-group">
                                <label class="input-group-addon">进口非特殊用途化妆品备案登记凭证  </label>
                                <input value= "${result.productExtMess.importCosmeticsRegisCard!""}"  name="productExtMess.importCosmeticsRegisCard" type="text" class="form-control" disabled="disabled">
                            </div>
                        </div>
                        <div class="col-sm-6 marginTop5">
                            <div class="input-group">
                                <label class="input-group-addon">进口非特殊用途化妆品备案登记凭证效期 </label>
                                <input value= "${result.productExtMess.importCosmeticsRegisCardValidity!""}" name="productExtMess.importCosmeticsRegisCardValidity" class="form-control Wdate" onfocus="WdatePicker()" disabled="disabled">
                            </div>
                        </div>
                    </div>


					<div class="row" style="display:none">
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon"><span class="danger">*</span>零售价</label>
								<input type="text" disabled name="retailPrice" value="${result.retailPrice!""}" class="form-control" required="required" data-msg-required='该字段为必填项'>
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">会员价</label>
								<input type="text" disabled name="vipPrice" value="${result.vipPrice!""}" class="form-control">
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">积分商品</label>
								<input type="text" disabled  value="${fistproduct.scoreProductYn!""}" class="form-control">
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">积分倍数</label>
								<input type="text" disabled name="scoreRate" value="${result.scoreRate!""}" class="form-control">
							</div>
						</div>
					</div>
					<div class="row lineBorder" style="display:none">
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">特价商品</label>
								<select disabled class="form-control">
									<option value="1">是</option>
									<option value="0" selected>否</option>
								</select>
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">库存上限</label>
								<input type="text" disabled name="storeMaxLimit" value="${result.storeMaxLimit!""}" class="form-control">
							</div>
						</div>
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">库存下限</label>
								<input type="text" disabled name="storeMinLimit" value="${result.storeMinLimit!""}" class="form-control">
							</div>
						</div>
					</div>
					<div class="row" style="display:none">
						<div class="col-sm-3 marginTop5">
							<div class="input-group">
								<label class="input-group-addon">是否启用</label>
								<select name="status" disabled class="form-control">
									<option value="1" selected=${result.status!-1} == 1>是</option>
									<option value="0" selected=${result.status!-1} !=1>否</option>
								</select>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-3 marginTop5">
						</div>
					</div>
					<button class="hideSubmit" type="submit">提交</button>
				</form>
				<input type="hidden" id="businessNo" value="${result.pref!""}">
                <input type="hidden" id="approvePage" value="${approvePage!""}">
				<div class="panel panel-default publicPanel" style="padding:0">
					<table id="grid-table"></table>
					<div id="grid-pager" style="margin-top: 20px !important;"></div>
				</div>
			</div>
		</div>
	</body>
<script src="/static/js/approve/productInfo.js?v=${jsVersion}"></script>
<script type="text/javascript">
	$(function(){
		var temp =  "${result.imgUrl!""}";
		if('' == temp){
			return;
		}
		var array = temp.split(",");
		$("#number").text("查看附件")
	})
</script>


</html>
