<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.cloud.page.dao.SaasIntentionCustomerClueMapper">

    <resultMap type="com.xyy.saas.cloud.page.po.SaasIntentionCustomerCluePo" id="SaasIntentionCustomerClueMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="customerPref" column="customer_pref" jdbcType="VARCHAR"/>
        <result property="submitTime" column="submit_time" jdbcType="TIMESTAMP"/>
        <result property="expectedDate" column="expected_date" jdbcType="TIMESTAMP"/>
        <result property="expectedPeriod" column="expected_period" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="source" column="source" jdbcType="INTEGER"/>
        <result property="salesName" column="sales_name" jdbcType="VARCHAR"/>
        <result property="salesPhone" column="sales_phone" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>


    <sql id="Base_Column_List">
id,customer_pref,submit_time,expected_date,expected_period,remark,source,sales_name,sales_phone,create_time,update_time    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="SaasIntentionCustomerClueMap">
        select
            <include refid="Base_Column_List"/>
        from saas_intention_customer_clue
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryByCondition" resultMap="SaasIntentionCustomerClueMap">
        select
            <include refid="Base_Column_List"/>
        from saas_intention_customer_clue
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="customerPref != null and customerPref != ''">
                and customer_pref = #{customerPref}
            </if>
            <if test="submitTime != null">
                and submit_time = #{submitTime}
            </if>
            <if test="expectedDate != null">
                and expected_date = #{expectedDate}
            </if>
            <if test="expectedPeriod != null">
                and expected_period = #{expectedPeriod}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="source != null">
                and source = #{source}
            </if>
            <if test="salesName != null and salesName != ''">
                and sales_name = #{salesName}
            </if>
            <if test="salesPhone != null and salesPhone != ''">
                and sales_phone = #{salesPhone}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true">
        insert into saas_intention_customer_clue
			<trim prefix="(" suffix=")" suffixOverrides=",">
   <if test="id != null">
                 id,
            	</if>
   <if test="customerPref != null">
                 customer_pref,
            	</if>
   <if test="submitTime != null">
                 submit_time,
            	</if>
   <if test="expectedDate != null">
                 expected_date,
            	</if>
   <if test="expectedPeriod != null">
                 expected_period,
            	</if>
   <if test="remark != null">
                 remark,
            	</if>
   <if test="source != null">
                 source,
            	</if>
   <if test="salesName != null">
                 sales_name,
            	</if>
   <if test="salesPhone != null">
                 sales_phone,
            	</if>
   <if test="createTime != null">
                 create_time,
            	</if>
   <if test="updateTime != null">
                 update_time,
            	</if>
   			</trim>
   			<trim prefix="values (" suffix=")" suffixOverrides=",">
   <if test="id != null">
                 #{id},
            	</if>
   <if test="customerPref != null">
                 #{customerPref},
            	</if>
   <if test="submitTime != null">
                 #{submitTime},
            	</if>
   <if test="expectedDate != null">
                 #{expectedDate},
            	</if>
   <if test="expectedPeriod != null">
                 #{expectedPeriod},
            	</if>
   <if test="remark != null">
                 #{remark},
            	</if>
   <if test="source != null">
                 #{source},
            	</if>
   <if test="salesName != null">
                 #{salesName},
            	</if>
   <if test="salesPhone != null">
                 #{salesPhone},
            	</if>
   <if test="createTime != null">
                 #{createTime},
            	</if>
   <if test="updateTime != null">
                 #{updateTime},
            	</if>
    		</trim>
    </insert>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from saas_intention_customer_clue
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="customerPref != null and customerPref != ''">
                and customer_pref = #{customerPref}
            </if>
            <if test="submitTime != null">
                and submit_time = #{submitTime}
            </if>
            <if test="expectedDate != null">
                and expected_date = #{expectedDate}
            </if>
            <if test="expectedPeriod != null">
                and expected_period = #{expectedPeriod}
            </if>
            <if test="remark != null and remark != ''">
                and remark = #{remark}
            </if>
            <if test="source != null">
                and source = #{source}
            </if>
            <if test="salesName != null and salesName != ''">
                and sales_name = #{salesName}
            </if>
            <if test="salesPhone != null and salesPhone != ''">
                and sales_phone = #{salesPhone}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>


    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into saas_intention_customer_clue(customer_pref,submit_time,expected_date,expected_period,remark,source,sales_name,sales_phone,create_time,update_time)
        values
        <foreach collection="list" item="entity" separator=",">
        (#{entity.customerPref},#{entity.submitTime},#{entity.expectedDate},#{entity.expectedPeriod},#{entity.remark},#{entity.source},#{entity.salesName},#{entity.salesPhone},#{entity.createTime},#{entity.updateTime})
        </foreach>
    </insert>


    <!--通过主键修改数据-->
    <update id="updateSelective">
        update saas_intention_customer_clue
        <set>
            <if test="customerPref != null and customerPref != ''">
                customer_pref = #{customerPref},
            </if>
            <if test="submitTime != null">
                submit_time = #{submitTime},
            </if>
            <if test="expectedDate != null">
                expected_date = #{expectedDate},
            </if>
            <if test="expectedPeriod != null">
                expected_period = #{expectedPeriod},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="source != null">
                source = #{source},
            </if>
            <if test="salesName != null and salesName != ''">
                sales_name = #{salesName},
            </if>
            <if test="salesPhone != null and salesPhone != ''">
                sales_phone = #{salesPhone},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from saas_intention_customer_clue where id = #{id}
    </delete>

</mapper>

