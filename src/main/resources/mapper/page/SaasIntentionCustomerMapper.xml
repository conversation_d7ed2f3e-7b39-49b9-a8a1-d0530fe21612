<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.cloud.page.dao.SaasIntentionCustomerMapper">

    <resultMap type="com.xyy.saas.cloud.page.po.SaasIntentionCustomerPo" id="SaasIntentionCustomerMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="customerPref" column="customer_pref" jdbcType="VARCHAR"/>
        <result property="customerDrugstoreName" column="customer_drugstore_name" jdbcType="VARCHAR"/>
        <result property="customerPhone" column="customer_phone" jdbcType="VARCHAR"/>
        <result property="province" column="province" jdbcType="VARCHAR"/>
        <result property="city" column="city" jdbcType="VARCHAR"/>
        <result property="area" column="area" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>


    <sql id="Base_Column_List">
id,customer_pref,customer_drugstore_name,customer_phone,province,city,area,create_time,update_time    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="SaasIntentionCustomerMap">
        select
            <include refid="Base_Column_List"/>
        from saas_intention_customer
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryByCondition" resultMap="SaasIntentionCustomerMap">
        select
            <include refid="Base_Column_List"/>
        from saas_intention_customer
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="customerPref != null and customerPref != ''">
                and customer_pref = #{customerPref}
            </if>
            <if test="customerDrugstoreName != null and customerDrugstoreName != ''">
                and customer_drugstore_name = #{customerDrugstoreName}
            </if>
            <if test="customerPhone != null and customerPhone != ''">
                and customer_phone = #{customerPhone}
            </if>
            <if test="province != null and province != ''">
                and province = #{province}
            </if>
            <if test="city != null and city != ''">
                and city = #{city}
            </if>
            <if test="area != null and area != ''">
                and area = #{area}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>

    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true">
        insert into saas_intention_customer
			<trim prefix="(" suffix=")" suffixOverrides=",">
   <if test="id != null">
                 id,
            	</if>
   <if test="customerPref != null">
                 customer_pref,
            	</if>
   <if test="customerDrugstoreName != null">
                 customer_drugstore_name,
            	</if>
   <if test="customerPhone != null">
                 customer_phone,
            	</if>
   <if test="province != null">
                 province,
            	</if>
   <if test="city != null">
                 city,
            	</if>
   <if test="area != null">
                 area,
            	</if>
   <if test="createTime != null">
                 create_time,
            	</if>
   <if test="updateTime != null">
                 update_time,
            	</if>
   			</trim>
   			<trim prefix="values (" suffix=")" suffixOverrides=",">
   <if test="id != null">
                 #{id},
            	</if>
   <if test="customerPref != null">
                 #{customerPref},
            	</if>
   <if test="customerDrugstoreName != null">
                 #{customerDrugstoreName},
            	</if>
   <if test="customerPhone != null">
                 #{customerPhone},
            	</if>
   <if test="province != null">
                 #{province},
            	</if>
   <if test="city != null">
                 #{city},
            	</if>
   <if test="area != null">
                 #{area},
            	</if>
   <if test="createTime != null">
                 #{createTime},
            	</if>
   <if test="updateTime != null">
                 #{updateTime},
            	</if>
    		</trim>
    </insert>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from saas_intention_customer
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="customerPref != null and customerPref != ''">
                and customer_pref = #{customerPref}
            </if>
            <if test="customerDrugstoreName != null and customerDrugstoreName != ''">
                and customer_drugstore_name = #{customerDrugstoreName}
            </if>
            <if test="customerPhone != null and customerPhone != ''">
                and customer_phone = #{customerPhone}
            </if>
            <if test="province != null and province != ''">
                and province = #{province}
            </if>
            <if test="city != null and city != ''">
                and city = #{city}
            </if>
            <if test="area != null and area != ''">
                and area = #{area}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>
    <select id="queryByNameAndPhone" resultMap="SaasIntentionCustomerMap">
        select
        <include refid="Base_Column_List"/>
        from saas_intention_customer
        where customer_phone = #{customerPhone} and customer_drugstore_name = #{customerDrugstoreName} limit 1
    </select>


    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into saas_intention_customer(customer_pref,customer_drugstore_name,customer_phone,province,city,area,create_time,update_time)
        values
        <foreach collection="list" item="entity" separator=",">
        (#{entity.customerPref},#{entity.customerDrugstoreName},#{entity.customerPhone},#{entity.province},#{entity.city},#{entity.area},#{entity.createTime},#{entity.updateTime})
        </foreach>
    </insert>


    <!--通过主键修改数据-->
    <update id="updateSelective">
        update saas_intention_customer
        <set>
            <if test="customerPref != null and customerPref != ''">
                customer_pref = #{customerPref},
            </if>
            <if test="customerDrugstoreName != null and customerDrugstoreName != ''">
                customer_drugstore_name = #{customerDrugstoreName},
            </if>
            <if test="customerPhone != null and customerPhone != ''">
                customer_phone = #{customerPhone},
            </if>
            <if test="province != null and province != ''">
                province = #{province},
            </if>
            <if test="city != null and city != ''">
                city = #{city},
            </if>
            <if test="area != null and area != ''">
                area = #{area},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from saas_intention_customer where id = #{id}
    </delete>

</mapper>

