server:
  port: 9907
##logging
logging:
  config: classpath:logback-spring.xml
  file: heye_member_ms

##mybatis
mybatis:
  mapper-locations: classpath:/mapper/*.xml,classpath:/mapper/**/*.xml
  configuration:
    mapUnderscoreToCamelCase: true
    log-prefix: dao.
pagehelper:
  autoDialect: true
  closeConn: true
  reasonable: true
app:
  id: xyy-heye-member-all

---
##开发环境
spring:
  profiles:
    active: dev
  datasource:
    url: jdbc:mysql://************:3307/xyy_heye_member?createDatabaseIfNotExist=true&autoReconnect=true&allowMultiQueries=true&rewriteBatchedStatements=true
    username: superxyy
    #password: saasxyy123
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.jdbc.Driver
    druid:
      connection-init-sqls: set names utf8mb4
      max-active: 50
      initial-size: 5
      max-wait: 60000
      min-idle: 5
      time-between-eviction-runs-millis: 2000
      min-evictable-idle-time-millis: 300000
      validation-query: select 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      keep-alive: true
      filters: config
      connection-properties: config.decrypt=true;config.decrypt.key=${spring.datasource.key}

dubbo:
  application:
    name: heye_member_9907
    qos-port: 22244
    version: 1.0.0
  protocol:
    id: dubbo
    name: dubbo
    port: 22994
    threadpool: fixed
    threads: 500
  registry:
    check: false
    protocol: zookeeper
    address: zk1-dev.zookeeper.ybm100.top:2181,zk2-dev.zookeeper.ybm100.top:2181,zk3-dev.zookeeper.ybm100.top:2181 # 集团侧zk地址
    timeout: 6000000
  consumer:
    check: false
    timeout: 6000000
  provider:
    timeout: 6000000
    retries: -1

management:
  security:
    enabled: false
  health:
    dubbo:
      status:
        defaults: memory
        extras: load,threadpool
###rocketmq参数配置
rocketmq:
  name-server: mq1-dev.rocketmq.ybm100.top:9876;mq2-dev.rocketmq.ybm100.top:9876;mq3-dev.rocketmq.ybm100.top:9876;mq4-dev.rocketmq.ybm100.top:9876
  registerMember:
    topic: heye_register_member_dev_topic
    group: heye_pc_homepage_member_dev_group
    activityGroup: heye_register_member_dev_group
  handleCoupon:
    topic: heye_add_verify_dev_topic
    group: heye_add_verify_dev_group
  sendActivitySms:
      topic: heye_send_activity_sms_dev_topic
      group: heye_send_activity_sms_dev_topic
  sendSms:
      topic: heye_send_sms_dev_topic
      group: heye_send_sms_dev_group
  addPrecisionActivity:
    topic: heye_add_precision_activity_dev_topic
    group: heye_add_precision_activity_dev_group
  orderImport:
    topic: heye_import_order_dev_topic
    group: heye_import_order_dev_group
    orderCountGroup: heye_import_order_dev_order_count_group
  producer:
    group: PID_heye_member_ms
  unBindServiceUser:
    topic: dev_unbind_employee_topic
    group: dev_unbind_employee_group
  addMemberRecord:
    topic: heye_add_member_record_dev_topic
    group: heye_add_member_record_dev_group

apollo:
  bootstrap:
    enabled: true
    eagerLoad:
      enabled: true
  meta: http://node01-dev.appolo.ybm100.top:8080,http://node02-dev.appolo.ybm100.top:8080,http://node03-dev.appolo.ybm100.top:8080

xyy:
  elastic:
    job:
      zk:
        serverLists: zk1-dev.zookeeper.ybm100.top:2181,zk2-dev.zookeeper.ybm100.top:2181,zk3-dev.zookeeper.ybm100.top:2181
        namespace: heye-member-ElasticJob-Register
  redis:
    host: db1-saas-dev.redis.ybm100.top
    password: C2NJd7e6ffpyqe52
    port: 50001
    readTimeout: 2000
    connectTimeout: 2000
    pool:
      max-active: 8
      max-idle: 8
      min-idle: 0
      max-wait: 2000
---

##测试环境
spring:
  profiles: test
  datasource:
    url: ******************************************************************************************************************************************************************************
    username: superxyy
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.jdbc.Driver
    druid:
      connection-init-sqls: set names utf8mb4
      max-active: 50
      initial-size: 5
      max-wait: 60000
      min-idle: 5
      time-between-eviction-runs-millis: 2000
      min-evictable-idle-time-millis: 300000
      validation-query: select 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      keep-alive: true
      filters: config
      connection-properties: config.decrypt=true;config.decrypt.key=${spring.datasource.key}
dubbo:
  application:
    name: heye_member_9907
    qos-port: 22242
    version: 1.0.0
  protocol:
    id: dubbo
    name: dubbo
    port: 22994
    threadpool: fixed
    threads: 500
  registry:
    check: false
    protocol: zookeeper
    address: zk1-test.zookeeper.ybm100.top:2181,zk2-test.zookeeper.ybm100.top:2181,zk3-test.zookeeper.ybm100.top:2181
  consumer:
    check: false
    timeout: 6000000
  provider:
    retries: -1
    timeout: 6000000

management:
  security:
    enabled: false
  health:
    dubbo:
      status:
        defaults: memory
        extras: load,threadpool
###rocketmq参数配置
rocketmq:
  name-server: mq1-test.rocketmq.ybm100.top:9876;mq2-test.rocketmq.ybm100.top:9876;mq3-test.rocketmq.ybm100.top:9876;mq4-test.rocketmq.ybm100.top:9876
  registerMember:
    topic: heye_register_member_test_topic
    group: heye_pc_homepage_member_test_group
    activityGroup: heye_register_member_test_group
  handleCoupon:
    topic: heye_add_verify_test_topic
    group: heye_add_verify_test_group
  sendActivitySms:
        topic: heye_send_activity_sms_test_topic
        group: heye_send_activity_sms_test_topic
  sendSms:
    topic: heye_send_sms_test_topic
    group: heye_send_sms_test_group
  addPrecisionActivity:
    topic: heye_add_precision_activity_test_topic
    group: heye_add_precision_activity_test_group
  orderImport:
      topic: heye_import_order_test_topic
      group: heye_import_order_test_group
      orderCountGroup: heye_import_order_test_order_count_group
  producer:
    group: PID_heye_member_ms
  unBindServiceUser:
    topic: test_unbind_employee_topic
    group: test_unbind_employee_group
  addMemberRecord:
    topic: heye_add_member_record_test_topic
    group: heye_add_member_record_test_group


apollo:
  bootstrap:
    enabled: true
    eagerLoad:
      enabled: true
  meta: http://node01-test.appolo.ybm100.top:8080,http://node02-test.appolo.ybm100.top:8080,http://node03-test.appolo.ybm100.top:8080

xyy:
  elastic:
    job:
      zk:
        serverLists: zk1-test.zookeeper.ybm100.top:2181,zk2-test.zookeeper.ybm100.top:2181,zk3-test.zookeeper.ybm100.top:2181
        namespace: heye-member-ElasticJob-Register
  redis:
    host: db1-saas-test.redis.ybm100.top
    password: I2yUjDtmpC49DovW
    port: 51001
    readTimeout: 2000
    connectTimeout: 2000
    pool:
      max-active: -1
      max-idle: -1
      min-idle: 0
      max-wait: -1
---

##test3测试环境
spring:
  profiles: test3
  datasource:
    url: jdbc:mysql://************:3307/xyy_heye_member?createDatabaseIfNotExist=true&autoReconnect=true&allowMultiQueries=true&rewriteBatchedStatements=true
    username: superxyy
    #password: superxyy123
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.jdbc.Driver
    druid:
      connection-init-sqls: set names utf8mb4
      max-active: 50
      initial-size: 5
      max-wait: 60000
      min-idle: 5
      time-between-eviction-runs-millis: 2000
      min-evictable-idle-time-millis: 300000
      validation-query: select 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      keep-alive: true
      filters: config
      connection-properties: config.decrypt=true;config.decrypt.key=${spring.datasource.key}
dubbo:
  application:
    name: heye_member_9907
    qos-port: 22242
    version: 1.0.0
  config:
    multiple: true
  registries:
    common:
      id: common
      protocol: zookeeper
      address: zk1-test.zookeeper.ybm100.top:2181,zk2-test.zookeeper.ybm100.top:2181,zk3-test.zookeeper.ybm100.top:2181
      check: false
      default: false
  protocol:
    id: dubbo
    name: dubbo
    port: 22994
    threadpool: fixed
    threads: 500
  registry:
    check: false
    protocol: zookeeper
    address: zk301-base-test-bj2.ybm100.top:2181,zk302-base-test-bj2.ybm100.top:2181,zk303-base-test-bj2.ybm100.top:2181
  consumer:
    check: false
    timeout: 6000000
  provider:
    retries: -1
    timeout: 6000000

management:
  security:
    enabled: false
  health:
    dubbo:
      status:
        defaults: memory
        extras: load,threadpool
###rocketmq参数配置
rocketmq:
  name-server: mq1-test.rocketmq.ybm100.top:9876;mq2-test.rocketmq.ybm100.top:9876;mq3-test.rocketmq.ybm100.top:9876;mq4-test.rocketmq.ybm100.top:9876
  registerMember:
    topic: heye_register_member_test3_topic
    group: heye_pc_homepage_member_test3_group
    activityGroup: heye_register_member_test3_group
  handleCoupon:
    topic: heye_add_verify_test3_topic
    group: heye_add_verify_test3_group
  sendActivitySms:
        topic: heye_send_activity_sms_test3_topic
        group: heye_send_activity_sms_test3_topic
  sendSms:
    topic: heye_send_sms_test3_topic
    group: heye_send_sms_test3_group
  addPrecisionActivity:
    topic: heye_add_precision_activity_test3_topic
    group: heye_add_precision_activity_test3_group
  orderImport:
    topic: heye_import_order_test3_topic
    group: heye_import_order_test3_group
    orderCountGroup: heye_import_order_test3_order_count_group
  producer:
    group: PID_heye_member_ms
  unBindServiceUser:
    topic: test3_unbind_employee_topic
    group: test3_unbind_employee_group
  addMemberRecord:
    topic: heye_add_member_record_test3_topic
    group: heye_add_member_record_test3_group

apollo:
  cluster: test3
  bootstrap:
    enabled: true
    eagerLoad:
      enabled: true
  meta: http://node01-test.appolo.ybm100.top:8080,http://node02-test.appolo.ybm100.top:8080,http://node03-test.appolo.ybm100.top:8080

xyy:
  elastic:
    job:
      zk:
        serverLists: zk301-base-test-bj2.ybm100.top:2181,zk302-base-test-bj2.ybm100.top:2181,zk303-base-test-bj2.ybm100.top:2181
        namespace: heye-member-ElasticJob-Register
  redis:
    host: db1-saas-test.redis.ybm100.top
    password: I2yUjDtmpC49DovW
    port: 51001
    readTimeout: 100000
    connectTimeout: 2000
    pool:
      max-active: -1
      max-idle: -1
      min-idle: 0
      max-wait: -1
---

##生产环境
spring:
  profiles: prod
  datasource:
    url: *************************************************************************************************************************************************************************
    username: ${spring.datasource.username}
    password: ${spring.datasource.password}
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.jdbc.Driver
    druid:
      connection-init-sqls: set names utf8mb4
      max-active: 50
      initial-size: 5
      max-wait: 60000
      min-idle: 5
      time-between-eviction-runs-millis: 2000
      min-evictable-idle-time-millis: 300000
      validation-query: select 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      keep-alive: true
      filters: config
      connection-properties: config.decrypt=false;

dubbo:
  application:
    name: heye_member_9907
    qos-port: 22240
    version: 1.0.0
  protocol:
    id: dubbo
    name: dubbo
    port: 22994
    threadpool: fixed
    threads: 500
  registry:
    check: false
    protocol: zookeeper
    address: zk01-ec-prod.zookeeper.ybm100.top:2181,zk02-ec-prod.zookeeper.ybm100.top:2181,zk03-ec-prod.zookeeper.ybm100.top:2181,zk04-ec-prod.zookeeper.ybm100.top:2181,zk05-ec-prod.zookeeper.ybm100.top:2181
    timeout: 6000000
  consumer:
    check: false
    timeout: 6000000
  provider:
    timeout: 6000000
    retries: -1

management:
  security:
    enabled: false
  health:
    dubbo:
      status:
        defaults: memory
        extras: load,threadpool
###rocketmq参数配置
rocketmq:
  name-server: mq01-saas-prod.rocketmq.ybm100.top:9876;mq02-saas-prod.rocketmq.ybm100.top:9876;mq03-saas-prod.rocketmq.ybm100.top:9876;mq04-saas-prod.rocketmq.ybm100.top:9876
  registerMember:
    topic: heye_register_member_prod_topic
    group: heye_pc_homepage_member_prod_group
    activityGroup: heye_register_member_prod_group
  handleCoupon:
    topic: heye_add_verify_prod_topic
    group: heye_add_verify_prod_group
  sendActivitySms:
        topic: heye_send_activity_sms_prod_topic
        group: heye_send_activity_sms_prod_topic
  sendSms:
    topic: heye_send_sms_prod_topic
    group: heye_send_sms_prod_group
  addPrecisionActivity:
    topic: heye_add_precision_activity_prod_topic
    group: heye_add_precision_activity_prod_group
  orderImport:
    topic: heye_import_order_prod_topic
    group: heye_import_order_prod_group
    orderCountGroup: heye_import_order_prod_order_count_group
  producer:
    group: PID_heye_member_ms
  unBindServiceUser:
    topic: prod_unbind_employee_topic
    group: prod_unbind_employee_group
  addMemberRecord:
    topic: heye_add_member_record_prod_topic
    group: heye_add_member_record_prod_group

###rocketmq参数配置
apollo:
  bootstrap:
    enabled: true
    eagerLoad:
      enabled: true
  meta: http://node01-prod.appolo.ybm100.top:8080,http://node02-prod.appolo.ybm100.top:8080,http://node03-prod.appolo.ybm100.top:8080

xyy:
  elastic:
    job:
      zk:
        serverLists: zk01-ec-prod.zookeeper.ybm100.top:2181,zk02-ec-prod.zookeeper.ybm100.top:2181,zk03-ec-prod.zookeeper.ybm100.top:2181,zk04-ec-prod.zookeeper.ybm100.top:2181,zk05-ec-prod.zookeeper.ybm100.top:2181
        namespace: heye-member-ElasticJob-Register
  redis:
    host: db21-saas-prod.redis.ybm100.top
    password: vTqWVtL69TkKqZlJ
    port: 6379
    readTimeout: 100000
    connectTimeout: 100000
    pool:
      max-active: -1
      max-idle: -1
      min-idle: 0
      max-wait: -1
---


##生产环境
spring:
  profiles: prod2
  datasource:
    url: *************************************************************************************************************************************************************************
    username: ${spring.datasource.username}
    password: ${spring.datasource.password}
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.jdbc.Driver
    druid:
      connection-init-sqls: set names utf8mb4
      max-active: 50
      initial-size: 5
      max-wait: 60000
      min-idle: 5
      time-between-eviction-runs-millis: 2000
      min-evictable-idle-time-millis: 300000
      validation-query: select 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      keep-alive: true
      filters: config
      connection-properties: config.decrypt=false;

dubbo:
  application:
    name: heye_member_9907
    qos-port: 22240
    version: 1.0.0
  config:
    multiple: true
  registries:
    common:
      id: common
      protocol: zookeeper
      address: zk01-ec-prod.zookeeper.ybm100.top:2181,zk02-ec-prod.zookeeper.ybm100.top:2181,zk03-ec-prod.zookeeper.ybm100.top:2181,zk04-ec-prod.zookeeper.ybm100.top:2181,zk05-ec-prod.zookeeper.ybm100.top:2181
      check: false
      default: false
  protocol:
    id: dubbo
    name: dubbo
    port: 22994
    threadpool: fixed
    threads: 500
  registry:
    check: false
    protocol: zookeeper
    address: zk201-saas-prod.zookeeper.ybm100.top:2181,zk202-saas-prod.zookeeper.ybm100.top:2181,zk203-saas-prod.zookeeper.ybm100.top:2181,zk204-saas-prod.zookeeper.ybm100.top:2181,zk205-saas-prod.zookeeper.ybm100.top:2181
    timeout: 6000000
  consumer:
    check: false
    timeout: 6000000
  provider:
    timeout: 6000000
    retries: -1

management:
  security:
    enabled: false
  health:
    dubbo:
      status:
        defaults: memory
        extras: load,threadpool
###rocketmq参数配置
rocketmq:
  name-server: mq01-saas-prod.rocketmq.ybm100.top:9876;mq02-saas-prod.rocketmq.ybm100.top:9876;mq03-saas-prod.rocketmq.ybm100.top:9876;mq04-saas-prod.rocketmq.ybm100.top:9876
  registerMember:
    topic: heye_register_member_prod2_topic
    group: heye_pc_homepage_member_prod2_group
    activityGroup: heye_register_member_prod2_group
  handleCoupon:
    topic: heye_add_verify_prod2_topic
    group: heye_add_verify_prod2_group
  sendActivitySms:
        topic: heye_send_activity_sms_prod2_topic
        group: heye_send_activity_sms_prod2_topic
  sendSms:
    topic: heye_send_sms_prod2_topic
    group: heye_send_sms_prod2_group
  addPrecisionActivity:
    topic: heye_add_precision_activity_prod2_topic
    group: heye_add_precision_activity_prod2_group
  orderImport:
      topic: heye_import_order_prod2_topic
      group: heye_import_order_prod2_group
      orderCountGroup: heye_import_order_prod2_order_count_group
  producer:
    group: PID_heye_member_ms
  unBindServiceUser:
    topic: prod2_unbind_employee_topic
    group: prod2_unbind_employee_group
  addMemberRecord:
    topic: heye_add_member_record_prod2_topic
    group: heye_add_member_record_prod2_group

apollo:
  bootstrap:
    enabled: true
    eagerLoad:
      enabled: true
  meta: http://node01-prod.appolo.ybm100.top:8080,http://node02-prod.appolo.ybm100.top:8080,http://node03-prod.appolo.ybm100.top:8080

xyy:
  elastic:
    job:
      zk:
        serverLists: zk201-saas-prod.zookeeper.ybm100.top:2181,zk202-saas-prod.zookeeper.ybm100.top:2181,zk203-saas-prod.zookeeper.ybm100.top:2181,zk204-saas-prod.zookeeper.ybm100.top:2181,zk205-saas-prod.zookeeper.ybm100.top:2181
        namespace: heye-member-ElasticJob-Register
  redis:
    host: db21-saas-prod.redis.ybm100.top
    password: vTqWVtL69TkKqZlJ
    port: 6379
    readTimeout: 100000
    connectTimeout: 100000
    pool:
      max-active: -1
      max-idle: -1
      min-idle: 0
      max-wait: -1