package com.xyy.heye;

import com.alibaba.dubbo.config.spring.context.annotation.EnableDubbo;
import com.alibaba.fastjson.parser.ParserConfig;
import com.xyy.elasticjob.client.annotation.EnableElasticJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@EnableDubbo
@EnableElasticJob
@ComponentScan(basePackages = {"com.xyy.*"})
public class XyyHeYeMemberApplication {
    private static final Logger logger = LoggerFactory.getLogger(XyyHeYeMemberApplication.class);

    public static void main(String[] args) {
        try {
            //spring容器关闭时，如果没有检测到dubbo销毁线程启动，就等待一段时间
            System.setProperty("dubbo.start.notiry.waiting.millis","5000"); //默认3秒，启动时等待通知客服端时间
            //dubbo销毁线程结束后，等待一段时间，再继续执行spring容器的销毁
            System.setProperty("dubbo.shutownhook.notiry.waiting.millis","5000"); //默认1秒，停用时等待通知客服端时间
            System.setProperty("dubbo.service.shutdown.wait","15000"); //默认10秒，dubbo优雅停机最大停机时间，设置优雅停机超时时间，如果超时则强制关闭。
            System.setProperty("cat.appName","xyy_heye_member_provider");
            SpringApplication app=new SpringApplication(XyyHeYeMemberApplication.class);
            app.run(args);
            ParserConfig.getGlobalInstance().setAutoTypeSupport(true);
        }catch (Exception e){
            logger.info("错误信息为：{}",e);
        }

    }
}
