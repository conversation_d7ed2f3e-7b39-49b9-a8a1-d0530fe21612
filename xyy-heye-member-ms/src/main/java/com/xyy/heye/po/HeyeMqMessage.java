package com.xyy.heye.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * mq 异常消息记录表
 * <AUTHOR>
 */
@Data
public class HeyeMqMessage implements Serializable {

    /**
     * 主键
     */
    private Long id;
    /**
     * 业务标识
     */
    private String businessId;
    /**
     * 消息类型 1 同步消息
     */
    private Integer type;
    /**
     * 原始消息
     */
    private String message;
    /**
     * 异常消息
     */
    private String errorMsg;
    /**
     * 重试次数
     */
    private Integer retryTimes;
    /**
     * -1 失败   1 成功
     */
    private Integer msgStatus;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 逻辑删除 1 有效 0 删除
     */
    private Integer yn;

    /**
     * 消息状态
     */
    public enum MsgStatus{
        SUCC(1, "成功"),
        ERROR(2, "失败"),
        ;
        private int code;
        private String msg;

        MsgStatus(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public static String getNameByCode(Integer code){
            for (MsgStatus msgStatus : MsgStatus.values()){
                if (code != null && code == msgStatus.getCode()){
                    return msgStatus.getMsg();
                }
            }
            return "";
        }

        public Integer getCode() {
            return code;
        }

        public String getMsg() {
            return msg;
        }
    }

    /**
     * 消息状态
     */
    public enum MsgType{
        DATA_SYNC_MSG(1, "数据同步消息"),
        ;
        private int code;
        private String msg;

        MsgType(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public static String getNameByCode(Integer code){
            for (MsgType msgType : MsgType.values()){
                if (code != null && code == msgType.getCode()){
                    return msgType.getMsg();
                }
            }
            return "";
        }

        public Integer getCode() {
            return code;
        }

        public String getMsg() {
            return msg;
        }
    }
}
