package com.xyy.heye.po;

import java.io.Serializable;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class HeyeCoupon implements Serializable {

    /**
     *
     */
    private Long id;
    /**
     * 券编号
     */
    private String couponNo;
    /**
     *  会员ID
     */
    private Long userId;
    /**
     *  优惠券模板id
     */
    private Long couponTemplateId;
    /**
     *  营销活动编号
     */
    private Long activityId;
    /**
     *  券类型: 1代金券, 2折扣券
     */
    private Integer couponType;
    /**
     *  折扣，1-100
     */
    private BigDecimal discountRate;
    /**
     *  减免金额
     */
    private BigDecimal remissionAmount;
    /**
     *  使用条件：最低消费金额
     */
    private BigDecimal minMoney;
    /**
     *  有效期起始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date effectStartDate;
    /**
     *  有效期截止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date effectEndDate;
    /**
     *  是否可以叠加(0-否 1-是)
     */
    private Integer isCumulative;
    /**
     *  是否与其它优惠同享(0-否 1-是)
     */
    private Integer isShare;
    /**
     *  药店唯一标识
     */
    private String organSign;
    /**
     *  总部机构号
     */
    private String headquartersOrganSign;
    /**
     *  发券方：1: 门店，2:总部
     */
    private Integer createType;
    /**
     *  状态:（1未使用   3已使用  5 已作废）
     */
    private Integer couponStatus;
    /**
     *  版本号   乐观锁时使用
     */
    private Integer baseVersion;
    /**
     *  创建人
     */
    private String createUser;
    /**
     *  创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;
    /**
     *  修改人
     */
    private String updateUser;
    /**
     *  修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;
    /**
     *  逻辑删除 1 有效 0 删除
     */
    private Integer yn;


    /**
     * 优惠券类型
     */
    public enum CouponType{
        VOUCHER(1, "代金券"),
        DISCOUNT_COUPON(2, "折扣券")
        ;
        private int code;
        private String msg;

        CouponType(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public static String getNameByCode(Integer code){
            for (CouponType couponType : CouponType.values()){
                if (code != null && code == couponType.getCode()){
                    return couponType.getMsg();
                }
            }
            return "未知状态" + code;
        }

        public int getCode() {
            return code;
        }

        public String getMsg() {
            return msg;
        }
    }

    /**
     * 优惠券状态类型
     */
    public enum CouponStatus{
        UNUSE(1, "未使用"),
        USED(3, "已使用"),
        INVALID(5, "已作废")
        ;
        private int code;
        private String msg;

        CouponStatus(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public static String getNameByCode(Integer code){
            for (CouponStatus couponStatus : CouponStatus.values()){
                if (code != null && code == couponStatus.getCode()){
                    return couponStatus.getMsg();
                }
            }
            return "未知状态" + code;
        }

        public int getCode() {
            return code;
        }

        public String getMsg() {
            return msg;
        }
    }

}
