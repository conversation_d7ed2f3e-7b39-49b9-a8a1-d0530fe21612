package com.xyy.heye.po;
import java.io.Serializable;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class HeyeDict implements Serializable{

    /**
      *  
      */
    private Integer id;
    /**
      *  业务唯一标识
      */
    private String name;
    /**
      *  名称
      */
    private String showName;
    /**
      *  字典值
      */
    private Integer value;
    /**
      *  扩展字段，根据业务需要设置值
      */
    private String ext;
    /**
      *  字典数据描述
      */
    private String description;
    /**
      *  业务值排序字段
      */
    private Integer sort;
    /**
      *  是有启用 1 是 0 否
      */
    private Integer isActive;
    /**
      *  创建人
      */
    private String createUser;
    /**
      *  创建时间
      */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;
    /**
      *  更新人
      */
    private String updateUser;
    /**
      *  更新时间
      */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;
    /**
      *  是否删除 1 有效、0 删除
      */
    private Integer yn;

}
