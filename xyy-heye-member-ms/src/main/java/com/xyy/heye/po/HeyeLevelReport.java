package com.xyy.heye.po;

import java.io.Serializable;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

@Data
public class HeyeLevelReport implements Serializable {

    /**
     *
     */
    private Long id;
    /**
     * 总部机构号
     */
    private String headquartersOrganSign;
    /**
     * 机构号
     */
    private String organSign;
    /**
     * 机构编码
     */
    private String drugstoreCode;
    /**
     * 核心会员数
     */
    private Integer coreMemberNum;
    /**
     * 普通会员数
     */
    private Integer usualMemberNum;
    /**
     * 新增会员数
     */
    private Integer memberNum;
    /**
     * 轻度睡眠会员数
     */
    private Integer mildSleepMemberNum;
    /**
     * 中度睡眠会员数
     */
    private Integer mediumSleepMemberNum;
    /**
     * 重度睡眠会员数
     */
    private Integer seriousSleepMemberNum;
    /**
     * 未分级会员数
     */
    private Integer unratedMemberNum;
    /**
     * 总会员数
     */
    private Integer totalMemberNum;
    /**
     * 至尊会员新增数
     */
    private Integer superMemberAddNum;
    /**
     * 钻石会员新增数
     */
    private Integer diamondMemberAddNum;
    /**
     * 黄金会员新增数
     */
    private Integer goldMemberAddNum;
    /**
     * 白银会员新增数
     */
    private Integer silverMemberAddNum;
    /**
     * 青铜会员新增数
     */
    private Integer copperMemberAddNum;
    /**
     * 未消费会员新增数
     */
    private Integer notConsumeMemberAddNum;
    /**
     * 至尊会员数
     */
    private Integer superMemberNum;
    /**
     * 钻石会员数
     */
    private Integer diamondMemberNum;
    /**
     * 黄金会员数
     */
    private Integer goldMemberNum;
    /**
     * 白银会员数
     */
    private Integer silverMemberNum;
    /**
     * 青铜会员数
     */
    private Integer copperMemberNum;
    /**
     * 未消费会员数
     */
    private Integer notConsumeMemberNum;
    /**
     * 统计时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date statisticalTime;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 1:活跃度 2贡献度
     */
    private Integer type;


}
