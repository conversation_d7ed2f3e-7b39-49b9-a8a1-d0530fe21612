package com.xyy.heye.po;
import java.io.Serializable;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

@Data
public class HeyeProduct implements Serializable{

    /**
      *  
      */
    private Long id;
    /**
      *  商品编号
      */
    private String productCode;
    /**
      *  通用名称
      */
    private String commonName;
    /**
      *  商品名称
      */
    private String productName;
    /**
      *  规格/型号
      */
    private String attributeSpecification;
    /**
      *  生产厂家
      */
    private String manufacturer;
    /**
      *  条形码
      */
    private String barCode;
    /**
      *  重点商品（1-是/0-否）
      */
    private Integer important;
    /**
      *  单位名称
      */
    private String unit;

    /**
      *  批准文号
      */
    private String approvalNumber;
    /**
      *  剂型名称
      */
    private String dosageForm;
    /**
      *  药店唯一标识
      */
    private String organSign;
    /**
      *  创建人
      */
    private String createUser;

    private String createUserName;
    /**
      *  创建时间
      */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;
    /**
      *  修改人
      */
    private String updateUser;

    private String updateUserName;
    /**
      *  修改时间
      */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    private Integer baseVersion;
    /**
      *  逻辑删除 1 有效 0 删除
      */
    private Integer yn;

    /**
     * 提交状态
     */
    public enum Important{
        IMPORTANT(1, "是"),
        UNIMPORTANT(0, "否")
        ;
        private int code;
        private String msg;

        Important(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public static String getNameByCode(Integer code){
            for (Important important : Important.values()){
                if (code != null && code == important.getCode()){
                    return important.getMsg();
                }
            }
            return "未知状态" + code;
        }

        public Integer getCode() {
            return code;
        }

        public String getMsg() {
            return msg;
        }
    }
}
