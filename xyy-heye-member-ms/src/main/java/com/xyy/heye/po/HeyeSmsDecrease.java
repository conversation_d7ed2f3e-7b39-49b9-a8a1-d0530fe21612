package com.xyy.heye.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;


@Data
public class HeyeSmsDecrease implements Serializable {

    private static final long serialVersionUID = -3090619390430647291L;
    /**
     *
     */
    private Long id;
    /**
     *
     */
    private Long smsId;

    private Integer costCount;

    private Integer originCount;

    private String organSign;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 逻辑删除 1 有效 0 删除
     */
    private Integer yn;



}
