package com.xyy.heye.po;

import java.io.Serializable;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class HeyeOrder implements Serializable {

    /**
     *
     */
    private Long id;
    /**
     * 订单编号
     */
    private String billNo;
    /**
     * 药店唯一标识
     */
    private String organSign;

    /**
     * 总部机构号
     */
    private String headquartersOrganSign;

    /**
     * 会员所属机构
     */
    private String memberOrganSign;

    /**
     * 订单金额
     */
    private BigDecimal billAmount;

    /**
     * 订单成本金额
     */
    private BigDecimal billCostAmount;

    /**
     * 订单商品数量
     */
    private BigDecimal billNum;
    /**
     * 销售时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date sellTime;
    /**
     * 收银员用户ID
     */
    private Integer cashierUserId;
    /**
     * 收银员姓名
     */
    private String cashierName;
    /**
     * 销售员用户ID
     */
    private Integer sellerUserId;
    /**
     * 销售员姓名
     */
    private String sellerName;
    /**
     * 会员ID
     */
    private Long memberId;
    /**
     * 会员卡号
     */
    private String cardNo;
    /**
     * 订单来源
     */
    private Integer billSource;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 修改人
     */
    private String updateUser;
    /**
     * 修改时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 逻辑删除 1 有效 0 删除
     */
    private Integer yn;

    public boolean equals(HeyeOrder order) {
        return (this == order);
    }

    public int hashCode() {
        return this.organSign.hashCode() + this.billNo.hashCode();
    }
}
