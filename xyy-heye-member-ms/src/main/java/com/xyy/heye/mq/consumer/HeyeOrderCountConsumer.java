package com.xyy.heye.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xyy.framework.redis.core.IRedis;
import com.xyy.heye.common.RedisConstant;
import com.xyy.heye.excel.dto.OrderMessageDTO;
import com.xyy.heye.po.HeyeOrder;
import com.xyy.heye.po.HeyeOrderDetail;
import com.xyy.heye.service.HeyeReportService;
import com.xyy.saas.common.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RocketMQMessageListener(topic = "${rocketmq.orderImport.topic}", consumerGroup = "${rocketmq.orderImport.orderCountGroup}")
public class HeyeOrderCountConsumer implements RocketMQListener<String> {

    @Resource
    private HeyeReportService reportService;
    
    @Autowired
    private IRedis redis;
    
    @Override
    public void onMessage(String s) {
        log.info("===>HeyeOrderCountConsumer.onMessage:{}", s);
        String secret = MD5Util.getMD5(s);
        OrderMessageDTO orderMessageDTO = JSONObject.parseObject(s, new TypeReference<OrderMessageDTO>() {});
        if(null == orderMessageDTO) {
            return;
        }
        Map<HeyeOrder, List<HeyeOrderDetail>> orderListMap = orderMessageDTO.getOrderMap();
        
        if(null == orderListMap) { return ; }
        if(!"HEYE_ORDER_COUNT_VALUE".equals(redis.get(RedisConstant.ORDER_IMPORT,secret))){
            redis.set(RedisConstant.ORDER_IMPORT, secret, "HEYE_ORDER_COUNT_VALUE", 60*60L);
            // 触发数据统计
            for(HeyeOrder order : orderListMap.keySet()) {
                try {
                    reportService.countHeyeMemberConsumeNumAmountAndSKU(order, orderListMap.get(order));
                } catch (Exception e) {
                    log.info("订单数据统计出错：{} 数据：{} {}", e.getMessage(), JSONObject.toJSONString(order), JSONObject.toJSON(orderListMap.get(order)));
                }
            }
        }else{
            log.info("订单统计重复消费: {}", s);
        }
    }

}
