package com.xyy.heye.mq.producer;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class HeyeMQProducer {

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    public SendResult syncSend(String destination, Object payload) {
        log.info("====>>>>RocketMqProducer:nameServer:{},{}", rocketMQTemplate.getProducer().getNamesrvAddr(), rocketMQTemplate.getProducer().getProducerGroup());
        log.info("====>>>>RocketMqProducer:topic and tags:{}", destination);
        SendResult sendResult = rocketMQTemplate.syncSend(destination, payload);
        log.info("====>>>>RocketMqProducer syncSend sendResult: {}", JSONObject.toJSONString(sendResult));
        return sendResult;
    }

    public void asyncSend(String destination, Object payload, SendCallback sendCallback) {
        log.info("====>>>>RocketMqProducer:nameServer:{},{}", rocketMQTemplate.getProducer().getNamesrvAddr(), rocketMQTemplate.getProducer().getProducerGroup());
        log.info("====>>>>RocketMqProducer:topic and tags:{}", destination);
        rocketMQTemplate.asyncSend(destination, payload, sendCallback);
        log.info("====>>>>RocketMqProducer asyncSend finished");
    }
}
