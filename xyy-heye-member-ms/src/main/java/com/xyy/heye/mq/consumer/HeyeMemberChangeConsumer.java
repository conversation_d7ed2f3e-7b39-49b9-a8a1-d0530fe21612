package com.xyy.heye.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.xyy.heye.dto.JudgeMemberServiceUserDTO;
import com.xyy.heye.dto.record.HeyeMemberRecordMsgDTO;
import com.xyy.heye.po.HeyeMember;
import com.xyy.heye.service.HeyeMemberRecordMsgService;
import com.xyy.heye.service.HeyeMemberService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Auther Wind
 * @Date 2021/4/21
 * @Description: 店员调店/离店，修改会员对应服务专员mq
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "${rocketmq.unBindServiceUser.topic}", consumerGroup = "${rocketmq.unBindServiceUser.group}")
public class HeyeMemberChangeConsumer implements RocketMQListener<String> {

    @Autowired
    private HeyeMemberService heyeMemberService;

    @Autowired
    private HeyeMemberRecordMsgService heyeMemberRecordMsgService;

    @Override
    public void onMessage(String s) {
        log.info("店员调店/离店，修改会员对应服务专员mq消息：{}", s);
        try{
            JudgeMemberServiceUserDTO judgeMemberServiceUserDTO = JSONObject.parseObject(s, JudgeMemberServiceUserDTO.class);
            boolean flag = heyeMemberService.judgeMemberServiceUser(judgeMemberServiceUserDTO);
            // 校验：如果不存在则不需要解除绑定
            if (!flag){
                return;
            }
            // 获取待修改会员列表
            List<HeyeMember> memberList = heyeMemberService.queryUnbindServiceUserMember(judgeMemberServiceUserDTO);
            // 解除绑定
            heyeMemberService.unBindServiceUser(judgeMemberServiceUserDTO);
            // 解除绑定完成后发送会员记录消息
            heyeMemberRecordMsgService.sendForChangeServiceUser(memberList, judgeMemberServiceUserDTO.getOperator(), HeyeMemberRecordMsgDTO.oprTypeEnum.REMOVE_SERVICE_USER.getCode());
        }catch (Exception e){
            log.error("店员调店/离店，修改服务专员异常，mq消息:{},异常信息:{}", s, e);
        }
    }
}
