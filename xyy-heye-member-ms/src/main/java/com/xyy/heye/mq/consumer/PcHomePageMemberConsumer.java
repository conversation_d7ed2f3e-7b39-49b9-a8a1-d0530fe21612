package com.xyy.heye.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xyy.framework.redis.core.IRedis;
import com.xyy.heye.common.HeYeMemberCatConstants;
import com.xyy.heye.common.RedisConstant;
import com.xyy.heye.dto.report.MemberStatMqDTO;
import com.xyy.heye.po.HeyeMemberDayDetailReport;
import com.xyy.heye.po.HeyeMemberDayReport;
import com.xyy.heye.service.HeyeReportService;
import com.xyy.heye.util.CatHeYeMemberUtil;
import com.xyy.heye.util.DateUtil;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQPushConsumerLifecycleListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@RocketMQMessageListener(topic = "${rocketmq.registerMember.topic}", consumerGroup = "${rocketmq.registerMember.group}")
public class PcHomePageMemberConsumer implements RocketMQListener<String> {

    private static final Logger logger = LoggerFactory.getLogger(PcHomePageMemberConsumer.class);

    @Autowired
    HeyeReportService heyeReportService;

    @Autowired
    private IRedis redis;

    @Override
    public void onMessage(String message) {
        logger.info("pc首页报表：新增会员数据信息：{}",message);
        List<MemberStatMqDTO> memberStatMqDTOList  = JSON.parseObject(message, new TypeReference<List<MemberStatMqDTO>>(){});

        Iterator<MemberStatMqDTO> memberStatMqDTOIterable = memberStatMqDTOList.iterator();
        while(memberStatMqDTOIterable.hasNext()){
            MemberStatMqDTO memberStatMqDTO = memberStatMqDTOIterable.next();
            //不统计没有会员卡号的
            if(memberStatMqDTO.getSendCardTime() == null){
                memberStatMqDTOIterable.remove();
                continue;
            }
            if(memberStatMqDTO.getCardNo() !=null){
                //重复会员卡号不统计，主要防止MQ重试
                if("1".equals(redis.get(RedisConstant.HEYE_MEMBER_CATEGORY,
                        RedisConstant.HEYE_MEMBER_CARD_NO_FOR_PC_STAT+"_"+memberStatMqDTO.getOrganSign()+"_"+memberStatMqDTO.getCardNo()))){
                    memberStatMqDTOIterable.remove();
                    logger.info("pc首页报表,药店：{},会员卡号：{}，重复消费",memberStatMqDTO.getOrganSign(),memberStatMqDTO.getCardNo());
                    continue;
                }else{
                    redis.set(RedisConstant.HEYE_MEMBER_CATEGORY,
                            RedisConstant.HEYE_MEMBER_CARD_NO_FOR_PC_STAT+"_"+memberStatMqDTO.getOrganSign()+"_"+memberStatMqDTO.getCardNo(),"1",60*60*24L);
                }
            }
            memberStatMqDTO.setSendCardTime(DateUtil.formatHhMmSsOfDate(memberStatMqDTO.getSendCardTime()));
        }

        if(memberStatMqDTOList.isEmpty()){
            return;
        }

        try {
            //按药店机构号进行分类
            Map<String,List<MemberStatMqDTO>> mapGroupByOrganSign = memberStatMqDTOList.stream().collect(Collectors.groupingBy(MemberStatMqDTO::getOrganSign));
            for(Map.Entry<String,List<MemberStatMqDTO>> groupByOrganSignEntry : mapGroupByOrganSign.entrySet()){
                String organSign = groupByOrganSignEntry.getKey();
                String headquartersOrganSign = groupByOrganSignEntry.getValue().get(0).getHeadquartersOrganSign();

                //根据开卡时间分类统计
                Map<Date,List<MemberStatMqDTO>> mapGroupBySendCardTime = groupByOrganSignEntry.getValue().stream().collect(Collectors.groupingBy(MemberStatMqDTO::getSendCardTime));
                for(Map.Entry<Date,List<MemberStatMqDTO>> groupBySendCardTimeEntry : mapGroupBySendCardTime.entrySet()) {
                    Date sendCardTime = groupBySendCardTimeEntry.getKey();
                    List<MemberStatMqDTO> groupBySendCardTimeDTO = groupBySendCardTimeEntry.getValue();
                    try{
                        doCalculate(organSign, headquartersOrganSign, sendCardTime, groupBySendCardTimeDTO);
                    }catch (Exception e){
                        logger.error("pc首页会员统计，更新或插入出现异常：药店机构号：{},总店:{},开卡时间：{},会员明细：{}，异常：",organSign,headquartersOrganSign,sendCardTime, JSONObject.toJSON(groupBySendCardTimeDTO),e);
                        CatHeYeMemberUtil.logMetricForCount(HeYeMemberCatConstants.HEYE_MEMBER_PC_HOMEPAGE_MEMBER_ERROR);
                    }
                }
            }
        }catch (Exception e){
            logger.error("pc首页报表：新增会员数据信息,出现异常：",e);
        }

    }

    private void doCalculate(String organSign, String headquartersOrganSign, Date sendCardTime, List<MemberStatMqDTO> groupBySendCardTimeDTO){
        if(groupBySendCardTimeDTO.isEmpty()){
            return;
        }
        String yearTime = String.valueOf(DateUtil.getYear(sendCardTime));
        String monthTime = String.valueOf(DateUtil.getMonth(sendCardTime));
        String dayTime = String.valueOf(DateUtil.getDay(sendCardTime));

        HeyeMemberDayReport heyeMemberDayReport = new HeyeMemberDayReport();
        heyeMemberDayReport.setOrganSign(organSign);
        heyeMemberDayReport.setHeadquartersOrganSign(headquartersOrganSign);
        heyeMemberDayReport.setYearTime(yearTime);
        heyeMemberDayReport.setMonthTime(monthTime);
        heyeMemberDayReport.setDayTime(dayTime);
        List<HeyeMemberDayDetailReport> heyeMemberDayDetailReportList = new ArrayList<>();
        Long increasedMemberNum = 0L;
        Long increasedRecommendMember = 0L;
        for(MemberStatMqDTO memberStatMqDTO:groupBySendCardTimeDTO){
            increasedMemberNum++;
            if(memberStatMqDTO.getRecommendUserId()!=null){
                increasedRecommendMember++;
            }
            HeyeMemberDayDetailReport heyeMemberDayDetailReport = new HeyeMemberDayDetailReport();
            heyeMemberDayDetailReport.setOrganSign(memberStatMqDTO.getOrganSign());
            heyeMemberDayDetailReport.setHeadquartersOrganSign(memberStatMqDTO.getHeadquartersOrganSign());
            heyeMemberDayDetailReport.setCardNo(memberStatMqDTO.getCardNo());
            heyeMemberDayDetailReport.setProductCode("");
            heyeMemberDayDetailReport.setType(HeyeMemberDayDetailReport.TypeEnum.INCREASE_CARD_NO.getCode());
            heyeMemberDayDetailReport.setYearTime(yearTime);
            heyeMemberDayDetailReport.setMonthTime(monthTime);
            heyeMemberDayDetailReport.setDayTime(dayTime);
            heyeMemberDayDetailReportList.add(heyeMemberDayDetailReport);
        }
        heyeMemberDayReport.setMemberNum(increasedMemberNum);
        heyeMemberDayReport.setRecommendMemberNum(increasedRecommendMember);
        heyeReportService.updatePcHomePageMember(heyeMemberDayReport,heyeMemberDayDetailReportList);
    }
}
