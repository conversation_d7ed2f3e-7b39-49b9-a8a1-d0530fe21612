package com.xyy.heye.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.xyy.heye.dto.AddHeyeMemberRecordReqDTO;
import com.xyy.heye.po.HeyeMember;
import com.xyy.heye.po.HeyeMemberRecord;
import com.xyy.heye.service.HeyeMemberRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Auther Wind
 * @Date 2021/4/21
 * @Description: 荷叶会员记录消费mq
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "${rocketmq.addMemberRecord.topic}", consumerGroup = "${rocketmq.addMemberRecord.group}")
public class HeyeMemberRecordConsumer implements RocketMQListener<String> {

    @Autowired
    private HeyeMemberRecordService heyeMemberRecordService;

    @Override
    public void onMessage(String s) {
        log.info("荷叶会员记录mq消息：{}", s);
        try{
            List<HeyeMemberRecord> memberRecordList = JSONObject.parseArray(s, HeyeMemberRecord.class);
            if (!CollectionUtils.isEmpty(memberRecordList)){
                heyeMemberRecordService.batchAddMemberRecord(memberRecordList);
            }
        }catch (Exception e){
            log.error("荷叶会员记录异常,mq消息:{},异常信息:{}", s, e);
        }
    }
}
