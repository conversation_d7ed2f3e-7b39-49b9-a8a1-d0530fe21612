package com.xyy.heye.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.xyy.framework.redis.core.IRedis;
import com.xyy.heye.dao.HeyeActivityDao;
import com.xyy.heye.dao.HeyeMemberDao;
import com.xyy.heye.dao.HeyeMemberExtendDao;
import com.xyy.heye.excel.dto.OrderMessageDTO;
import com.xyy.heye.po.*;
import com.xyy.heye.service.HeyeActivityReportService;
import com.xyy.heye.service.HeyeActivityTaskContextService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.xyy.heye.common.RedisConstant.ORDER_IMPORT;

@Slf4j
@Component
@RocketMQMessageListener(topic = "${rocketmq.orderImport.topic}", consumerGroup = "${rocketmq.orderImport.group}")
public class HeyeOrderConsumer implements RocketMQListener<String> {

    private static final ExecutorService EXECUTOR_SERVICE;

    static {
        int nThreads = Runtime.getRuntime().availableProcessors() * 2;
        EXECUTOR_SERVICE = Executors.newFixedThreadPool(nThreads);
    }

    @Resource
    private HeyeActivityDao activityDao;
    @Resource
    private HeyeMemberDao memberDao;
    @Autowired
    private HeyeMemberExtendDao heyeMemberExtendDao;
    @Resource
    private HeyeActivityReportService activityReportService;
    @Resource
    private HeyeActivityTaskContextService activityTaskContextService;
    @Resource
    private IRedis redis;

    @Override
    public void onMessage(String s) {
        log.info("【场景营销-下单】MQ接收信息为{}", s);
        try {
            OrderMessageDTO orderMessageDTO = JSONObject.parseObject(s, OrderMessageDTO.class);
            Map<HeyeOrder, List<HeyeOrderDetail>> orderMap = orderMessageDTO.getOrderMap();
            //触发下单场景营销
            EXECUTOR_SERVICE.submit(() -> triggerOrderActivity(orderMessageDTO));
            //营销报表订单数据统计
            List<HeyeOrder> orderList = new ArrayList<>(orderMap.keySet());
            EXECUTOR_SERVICE.submit(() -> activityReportService.updateActivityOrderInfoData(orderList));
            //更新会员最近消费时间
            EXECUTOR_SERVICE.submit(() -> updateLastConsumeTime(orderList));
        } catch (Exception e) {
            log.error("【场景营销-下单】MQ接收信息:{},异常信息：{}",s,e);
        }
    }

    /**
     * 触发下单场景营销活动
     */
    private void triggerOrderActivity(OrderMessageDTO orderMessageDTO) {
        Map<HeyeOrder, List<HeyeOrderDetail>> orderMap = orderMessageDTO.getOrderMap();
        String taskIdCount = orderMessageDTO.getTaskIdCount();
        String taskIdList = orderMessageDTO.getTaskIdList();
        //获取去重后的订单
        List<String> orderMemberList = getDistinctOrderList(orderMap, taskIdCount, taskIdList);
        log.info("【场景营销-下单】去重后订单信息为:{}",orderMemberList);
        if (CollectionUtils.isEmpty(orderMemberList)) {
            return;
        }
        //key：总部机构号；value：会员id-消费时间
        Map<String, List<String>> headquartersOrganSignAndOrderMap = new HashMap<>();
        for (String orderMember : orderMemberList) {
            String[] split = orderMember.split(":");
            String key = split[0];
            List<String> memberIdAndSellTimeList = headquartersOrganSignAndOrderMap.getOrDefault(key, new ArrayList<>());
            memberIdAndSellTimeList.add(split[1]);
            headquartersOrganSignAndOrderMap.put(key, memberIdAndSellTimeList);

        }

        //获取总部机构号
        List<String> headquartersOrganSignList = orderMap.keySet().stream().map(HeyeOrder::getHeadquartersOrganSign).collect(Collectors.toList());
        //获取organSign中当前进行中的下单场景营销活动
        List<HeyeActivity> activityList = activityDao.queryActivityListByOrganSignAndType(headquartersOrganSignList, 1);
        log.info("【场景营销-下单】查询正在进行活动为:{}",activityList);
        if (!CollectionUtils.isEmpty(activityList)) {
            for (HeyeActivity activity : activityList) {
                List<Long> idList = new ArrayList<>();
                String organSign = activity.getOrganSign();
                List<String> memberIdAndSellTimeList = headquartersOrganSignAndOrderMap.get(organSign);
                long startDate = activity.getStartDate().getTime();
                long endDate = activity.getEndDate().getTime();
                for (String memberIdAndSellTime : memberIdAndSellTimeList) {
                    String[] split = memberIdAndSellTime.split("-");
                    Long memberId = Long.valueOf(split[0]);
                    long sellTime = Long.parseLong(split[1]);
                    //下单时间在活动范围内的才会发券
                    if (sellTime >= startDate && sellTime <= endDate) {
                        idList.add(memberId);
                    }
                }
                if (!CollectionUtils.isEmpty(idList)) {
                    //触发下单场景营销活动
                    List<HeyeMember> memberList = memberDao.selectListByPrimaryKey(idList);
                    log.info("【场景营销-下单发券】用户id集合{}",memberList);
                    activityTaskContextService.doTask(activity, memberList);
                }
            }
        }
        //消息处理成功，清楚redis中的相关数据
        clearOrderInRedis(taskIdCount, taskIdList);
    }

    private void clearOrderInRedis(String taskIdCount, String taskIdList) {
        Integer count = redis.get(ORDER_IMPORT, taskIdCount, Integer.class);
        if (count == 0){
            redis.del(ORDER_IMPORT, taskIdCount);
            redis.del(ORDER_IMPORT, taskIdList);
            redis.del(ORDER_IMPORT, taskIdCount + "_key");
        }
    }

    private List<String> getDistinctOrderList(Map<HeyeOrder, List<HeyeOrderDetail>> orderMap, String taskIdCount, String taskIdList) {
        long count = 0L;
        for (List<HeyeOrderDetail> value : orderMap.values()) {
            count -= value.size();
        }
        Long result = redis.incrBy(ORDER_IMPORT, taskIdCount, count);
        if (result == null || result > 0) {
            return null;
        }
        //从缓存中获取需要下单会员数据
        List<String> orderMemberList = redis.getLRange(ORDER_IMPORT, taskIdList, 0, -1);
        if (CollectionUtils.isEmpty(orderMemberList)) {
            return null;
        }
        //去重
        return orderMemberList.stream().distinct().collect(Collectors.toList());
    }

    private void updateLastConsumeTime(List<HeyeOrder> orderList) {
        Map<Long, List<HeyeOrder>> orderMap = orderList.stream().collect(Collectors.groupingBy(HeyeOrder::getMemberId));
        List<HeyeMember> list = new ArrayList<>();
        List<HeyeMemberExtend> extendList = new ArrayList<>();
        for (Long memberId:orderMap.keySet()){
            HeyeMember heyeMember = new HeyeMember();
            HeyeMemberExtend heyeMemberExtend = new HeyeMemberExtend();
            heyeMember.setId(memberId);
            heyeMemberExtend.setMemberId(memberId);
            List<HeyeOrder> heyeOrders = orderMap.get(memberId.longValue());
            Date lastConsumeTime = null;
            BigDecimal totalAmount = BigDecimal.ZERO;
            for (HeyeOrder heyeOrder : heyeOrders) {
                if(lastConsumeTime == null){
                    lastConsumeTime = heyeOrder.getSellTime();
                }else{
                    lastConsumeTime = lastConsumeTime.getTime() - heyeOrder.getSellTime().getTime() > 0 ? lastConsumeTime : heyeOrder.getSellTime();
                }
                totalAmount = totalAmount.add(heyeOrder.getBillAmount());
            }
            heyeMember.setLastConsumeTime(lastConsumeTime);
            heyeMemberExtend.setTotalAmount(totalAmount);
            list.add(heyeMember);
            extendList.add(heyeMemberExtend);
        }
        if(list.size() > 0){
            memberDao.batchUpdateByPrimaryKeySelective(list);
        }
        if(extendList.size() > 0){
            heyeMemberExtendDao.batchUpdateTotalAmountByMemberId(extendList);
        }
    }
}
