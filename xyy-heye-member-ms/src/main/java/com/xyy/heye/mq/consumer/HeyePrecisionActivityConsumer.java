package com.xyy.heye.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xyy.heye.po.HeyeActivity;
import com.xyy.heye.service.HeyeActivityTaskContextService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 新增荷叶会员精准营销活动发券MQ
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "${rocketmq.addPrecisionActivity.topic}", consumerGroup = "${rocketmq.addPrecisionActivity.group}")
public class HeyePrecisionActivityConsumer implements RocketMQListener<String> {
    private static final ExecutorService EXECUTOR_SERVICE;
    static {
        int nThreads = Runtime.getRuntime().availableProcessors() * 2;
        EXECUTOR_SERVICE = Executors.newFixedThreadPool(nThreads);
    }

    @Resource
    private HeyeActivityTaskContextService activityTaskContextService;

    @Override
    public void onMessage(String s) {
        log.info("新增荷叶会员精准营销活动后发券请求参数:{}", s);
        HeyeActivity heyeActivity = JSONObject.parseObject(s, new TypeReference<HeyeActivity>() { });
        //触发下单场景营销
        EXECUTOR_SERVICE.submit(() -> activityTaskContextService.doTask(heyeActivity));
    }
}
