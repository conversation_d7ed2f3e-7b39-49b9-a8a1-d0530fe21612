package com.xyy.heye.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.xyy.heye.dto.sms.SmsStatisticsDTO;
import com.xyy.heye.service.HeyeActivityReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
@RocketMQMessageListener(topic = "${rocketmq.sendActivitySms.topic}", consumerGroup = "${rocketmq.sendActivitySms.group}")
public class HeyeSmsSendConsumer implements RocketMQListener<String> {

    @Resource
    private HeyeActivityReportService activityReportService;

    @Override
    public void onMessage(String s) {
        log.info("HeyeSmsSendConsumer: {}", s);
        try {
            if (StringUtils.isBlank(s)) {
                return;
            }
            List<SmsStatisticsDTO> statisticsDTOList = JSONObject.parseArray(s, SmsStatisticsDTO.class);
            activityReportService.updateSmsNum(statisticsDTOList);
        } catch (Exception e) {
            log.error("HeyeSmsSendConsumer: {},异常信息：{}",s,e);
        }
    }
}
