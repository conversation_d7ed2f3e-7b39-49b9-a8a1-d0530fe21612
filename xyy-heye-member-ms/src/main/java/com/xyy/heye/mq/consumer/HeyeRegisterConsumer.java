package com.xyy.heye.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xyy.heye.common.HeYeMemberCatConstants;
import com.xyy.heye.constant.SceneActivityType;
import com.xyy.heye.dao.HeyeActivityDao;
import com.xyy.heye.dao.HeyeMemberDao;
import com.xyy.heye.dto.report.MemberStatMqDTO;
import com.xyy.heye.po.HeyeActivity;
import com.xyy.heye.po.HeyeMember;
import com.xyy.heye.service.HeyeActivityTaskContextService;
import com.xyy.heye.util.CatHeYeMemberUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xyy.heye.constant.SceneActivityType.REGISTER;
import static com.xyy.heye.constant.SceneParamConstants.BirthScene.FIRST_DAY_OF_MONTH;

/**
 * 会员导入与小程序开卡
 * 会员开卡消息消费处理，触发开卡营销活动和生日营销活动
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "${rocketmq.registerMember.topic}", consumerGroup = "${rocketmq.registerMember.activityGroup}")
public class HeyeRegisterConsumer implements RocketMQListener<String> {

    @Resource
    private HeyeActivityDao activityDao;

    @Resource
    private HeyeMemberDao memberDao;

    @Resource
    private HeyeActivityTaskContextService activityTaskContextService;

    @Override
    public void onMessage(String s) {
        log.info("会员导入与小程序开卡：{}", s);
        try {
            List<MemberStatMqDTO> memberStatMqDTOList = JSON.parseObject(s, new TypeReference<List<MemberStatMqDTO>>() {
            });
            //去除开卡时间为空的数据
            List<MemberStatMqDTO> calculateList = memberStatMqDTOList.stream().filter(item -> item.getSendCardTime() != null).collect(Collectors.toList());
            List<String> cardNoList = calculateList.stream().map(MemberStatMqDTO::getCardNo).collect(Collectors.toList());
            Map<String, HeyeMember> memberMap;
            if (!CollectionUtils.isEmpty(cardNoList)) {
                //因为会员卡号不是全局唯一 所以需要添加总部机构号进行查询
                List<HeyeMember> heyeMembers = memberDao.selectByCardNoList(calculateList.get(0).getHeadquartersOrganSign(),cardNoList);
                memberMap = heyeMembers.stream().collect(Collectors.toMap(HeyeMember::getCardNo, Function.identity(),(o1,o2) -> o1));
            } else {
                memberMap = new HashMap<>();
            }

            Map<String, List<HeyeMember>> memberListMap = new HashMap<>();
            for (MemberStatMqDTO dto : calculateList) {
                String cardNo = dto.getCardNo();
                //根据卡号查询会员
                HeyeMember member = memberMap.get(cardNo);
                if (member == null) {
                    log.error("为查询到member, cardNo: {}", cardNo);
                    continue;
                }
                //总部机构号
                String headquartersOrganSign = member.getHeadquartersOrganSign();
                List<HeyeMember> memberList = memberListMap.getOrDefault(headquartersOrganSign, new ArrayList<>());
                memberList.add(member);
                memberListMap.put(headquartersOrganSign, memberList);
            }

            for (Map.Entry<String, List<HeyeMember>> entry : memberListMap.entrySet()) {
                String headquartersOrganSign = entry.getKey();
                List<HeyeMember> memberList = entry.getValue();
                //触发开卡场景营销
                triggerSceneActivity(headquartersOrganSign, memberList, REGISTER);
            }
        }catch (Exception e){
            log.info("会员导入与小程序开卡：请求参数：{}，异常信息{}",s, e);
            CatHeYeMemberUtil.logMetricForCount(HeYeMemberCatConstants.HEYE_MEMBER_REGISTER_MQ_ERROR);
        }
    }

    private void triggerSceneActivity(String headquartersOrganSign, List<HeyeMember> memberList, SceneActivityType activityType) {
        //根据organSign查询当前正在进行的场景营销活动
        List<HeyeActivity> activityList = activityDao.queryActivityListByOrganSignAndType(Collections.singletonList(headquartersOrganSign), activityType.getSceneType());
        if (CollectionUtils.isEmpty(activityList)) {
            return;
        }
        if (activityList.size() > 1) {
            log.error("重复开发营销活动");
            return;
        }
        HeyeActivity activity = activityList.get(0);
        List<HeyeMember> list;

        //开卡场景，找出开卡时间在活动时间范围内的会员
        if (activityType == REGISTER) {
            Date startDate = activity.getStartDate();
            Date endDate = activity.getEndDate();
            list = memberList.stream()
                    .filter(o -> o.getSendCardTime().after(startDate) && o.getSendCardTime().before(endDate))
                    .collect(Collectors.toList());
        } else {
            //生日场景，根据生日场景营销活动规则，找出符合的会员
            String sceneParam = activity.getSceneParam();
            DateTime now = new DateTime();
            //当月1号发券，但今天不是1号，不触发活动
            if (FIRST_DAY_OF_MONTH.equals(sceneParam) && now.getDayOfMonth() != 1) {
                return;
            }

            list = memberList.stream().filter(o -> {
                if(Objects.isNull(o.getUserBirthday())){
                    return false;
                }
                DateTime birthday = new DateTime(o.getUserBirthday());
                if (FIRST_DAY_OF_MONTH.equals(sceneParam)) {
                    //当月1号发券，同月生日的都符合
                    return birthday.getMonthOfYear() == now.getMonthOfYear();
                } else {
                    int days = Integer.parseInt(sceneParam);
                    //生日当天或者生日前几天，判断是否是今天
                    return birthday.minusDays(days).monthOfYear() == now.monthOfYear() && birthday.minusDays(days).dayOfMonth() == now.dayOfMonth();
                }
            }).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        //执行营销活动
        activityTaskContextService.doTask(activity, list);
    }
}
