package com.xyy.heye.mq;

import com.xyy.heye.mq.consumer.HeyeOrderConsumer;
import com.xyy.heye.mq.consumer.HeyeRegisterConsumer;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@ActiveProfiles("test")
@RunWith(SpringRunner.class)
@SpringBootTest
public class RocketMqTest {
    @Autowired
    private HeyeOrderConsumer heyeOrderConsumer;
    @Autowired
    private HeyeRegisterConsumer heyeRegisterConsumer;

    @Test
    public void importOrderTest(){
        String msg = "{\"orderMap\":{{\"billAmount\":1650,\"billCostAmount\":1500,\"billNo\":\"2021407004588\",\"billNum\":50,\"billSource\":0,\"cardNo\":\"HY2021040200017\",\"cashierName\":\"测试李乐\",\"cashierUserId\":117235,\"createUser\":\"117443\",\"headquartersOrganSign\":\"ZHL00005897\",\"id\":1,\"memberId\":101874,\"memberOrganSign\":\"ZHL00005912\",\"organSign\":\"ZHL00005912\",\"sellTime\":1617736501000,\"sellerName\":\"测试李乐\",\"sellerUserId\":117235,\"updateUser\":\"117443\"}:[{\"actualAmount\":1650,\"actualUnitAmount\":40,\"billNo\":\"2021407004588\",\"costPricee\":30,\"createUser\":\"117443\",\"orderId\":1,\"organSign\":\"ZHL00005912\",\"productCode\":\"ZHL25\",\"productNum\":50,\"updateUser\":\"117443\"}]},\"taskIdCount\":\"1006457_count\",\"taskIdList\":\"1006457_list\"}";
        heyeOrderConsumer.onMessage(msg);
    }

    @Test
    public void memberRegisterTest(){
        String msg="[{\"organSign\":\"ZHL00005965\",\"headquartersOrganSign\":\"ZHL00005897\",\"cardNo\":\"HY21235468752\",\"sendCardTime\":\"2021-04-21T01:40:00.000+0000\",\"recommendUserId\":null}]";
        heyeRegisterConsumer.onMessage(msg);
    }
}
