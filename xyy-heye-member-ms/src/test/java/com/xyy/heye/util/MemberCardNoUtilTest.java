package com.xyy.heye.util;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.CountDownLatch;

@ActiveProfiles("test")
@RunWith(SpringRunner.class)
@SpringBootTest
public class MemberCardNoUtilTest {

    @Autowired
    private MemberCardNoUtil memberCardNoUtil;

    @Test
    public void testMemberCardNo(){
        CountDownLatch countDownLatch = new CountDownLatch(100);
        for (int i = 0; i < 100 ; i++) {
            new Thread(()->{
                countDownLatch.countDown();
                String ddddd = memberCardNoUtil.createMemberCard("DDDDD");
                System.out.println(ddddd);
            }).start();
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}
