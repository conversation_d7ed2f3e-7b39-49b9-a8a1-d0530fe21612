package com.xyy.heye.activity.scene.api;

import com.xyy.heye.activity.BaseTest;
import com.xyy.heye.api.HeyeActivityApi;
import com.xyy.heye.dto.AddHeyeActivityReqDTO;
import com.xyy.heye.service.HeyeActivityReportService;
import com.xyy.saas.user.center.api.client.DrugstoreApi;
import com.xyy.saas.user.center.api.pojo.request.BaseParam;
import com.xyy.saas.user.center.api.pojo.response.BaseDrugstoreDTO;
import com.xyy.user.center.common.model.Result;
import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

public class SceneActivityTest extends BaseTest {

    @Resource
    private HeyeActivityApi heyeActivityApi;

    @Resource
    private HeyeActivityReportService activityReportService;

    @Before
    public void setUp() {
        DrugstoreApi drugstoreApi = Mockito.mock(DrugstoreApi.class); // dubbo接口
        MockitoAnnotations.initMocks(this);
        MockitoAnnotations.initMocks(activityReportService);
        ReflectionTestUtils.setField(activityReportService, "drugstoreApi", drugstoreApi);  // 将mock的dubbo接口送回被测对象

        ArrayList<BaseDrugstoreDTO> list = new ArrayList<>();
        BaseDrugstoreDTO dto1 = new BaseDrugstoreDTO();
        dto1.setOrganSign("zhl001");
        dto1.setDrugstoreName("门店1");
        BaseDrugstoreDTO dto2 = new BaseDrugstoreDTO();
        dto2.setOrganSign("zhl002");
        dto2.setDrugstoreName("门店2");
        list.add(dto1);
        list.add(dto2);
        Result<List<BaseDrugstoreDTO>> vo = new Result(1, "success", list);
        Mockito.when(drugstoreApi.listStore(Mockito.any(BaseParam.class))).thenReturn(vo);
    }

    @Test
    public void addTest() {
        AddHeyeActivityReqDTO dto = new AddHeyeActivityReqDTO();
        dto.setActivityType(1);
        dto.setActivityTitle("测试生日场景营销");
        DateTime dateTime = new DateTime();
        dto.setStartDate(dateTime.toDate());
        dto.setEndDate(dateTime.plusMonths(5).toDate());
        dto.setCouponId(10097L);
        dto.setEmployeeId("2001");
        dto.setOrganSign("NO0000010");
        dto.setSceneType(2);
        dto.setSceneParam("-1");
        dto.setSmsContent("--生日测试短信--");
        dto.setUseCoupon(1);
        heyeActivityApi.addHeyeActivity(dto);
    }
}
