package com.xyy.heye.activity.scene.api;

import com.xyy.heye.activity.BaseTest;
import com.xyy.heye.dao.HeyeActivityDao;
import org.junit.Test;

import javax.annotation.Resource;

public class ActivityDaoTest extends BaseTest {

    @Resource
    private HeyeActivityDao activityDao;

    @Test
    public void existSameTitleTest(){
        String organSign = "ZHL00005897";
        String title = "下单场景营销1";
        Integer i = activityDao.existSameNameActivity(organSign, 1, title);
        System.out.println(i);
    }
}
