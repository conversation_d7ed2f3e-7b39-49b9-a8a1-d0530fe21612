package com.xyy.heye.service.impl;

import com.xyy.heye.dto.VerifyHeyeCouponReqDTO;
import com.xyy.heye.service.HeyeCouponVerifyRecordService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@ActiveProfiles("test")
@RunWith(SpringRunner.class)
@SpringBootTest
public class HeyeCouponVerifyRecordServiceTest {

    @Autowired
    private HeyeCouponVerifyRecordService heyeCouponVerifyRecordService;

    @Test
    public void testVerifyRecord(){
        VerifyHeyeCouponReqDTO reqDTO = new VerifyHeyeCouponReqDTO();
        reqDTO.setHeadquartersOrganSign("ZHL00005897");
        reqDTO.setVerifyOrganSign("ZHL00005897");
        reqDTO.setVerifyUserId(117313);
        reqDTO.setVerifyUserName("马霸");
        reqDTO.setVerifyType(1);
        reqDTO.setCouponNo("9117D8AL");
        heyeCouponVerifyRecordService.addHeyeCouponVerifyRecord(reqDTO);
    }
}
