package com.xyy.heye.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xyy.heye.dto.HeyeCouponNumRecordDTO;
import com.xyy.heye.dto.report.PcMemberStatForPcArtificiallyReqDTO;
import com.xyy.heye.dto.report.QueryDataStatForPcRespDTO;
import com.xyy.heye.excel.dto.OrderMessageDTO;
import com.xyy.heye.po.HeyeOrder;
import com.xyy.heye.po.HeyeOrderDetail;
import com.xyy.heye.po.HeyeSmsRecord;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Created by oyh.Jerry to 2021/03/15 14:29
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("test")
public class HeyeReportServiceTest {
	
	@Autowired
	private HeyeReportService heyeReportService;
	
	@Test
	public void testCountHeyeCouponNum(){
//		List<SaaSDrugstoreDto> zhl00004322 = drugstoreApi.getDrugstoreByHeadquartersOrganSign("ZHL00004322");
		
//		HeyeCouponNumRecordDTO couponNumRecordDTO = new HeyeCouponNumRecordDTO();
//		couponNumRecordDTO.setNum(28L);
//		couponNumRecordDTO.setOrganSign("ZHL00004346");
//		couponNumRecordDTO.setTime(new Date());
//		couponNumRecordDTO.setType(0);
		String s = "{\"num\":2,\"organSign\":\"ZHL00006088\",\"time\":\"2021-03-29T03:39:47.501+0000\",\"type\":0,\"couponNo\":null,\"userId\":null}";
		HeyeCouponNumRecordDTO couponNumRecord = JSONObject.parseObject(s, new TypeReference<HeyeCouponNumRecordDTO>() {
		});
		heyeReportService.countHeyeCouponNum(couponNumRecord);
//		couponNumRecordDTO.setNum(10L);
//		couponNumRecordDTO.setOrganSign("ZHL00004324");
//		couponNumRecordDTO.setTime(new Date());
//		couponNumRecordDTO.setType(1);
//		heyeReportService.countHeyeCouponNum(couponNumRecordDTO);
		
//		Calendar now = Calendar.getInstance();
//		now.set(Calendar.DATE,now.get(Calendar.DATE)-1);
//		couponNumRecordDTO.setNum(1L);
//		couponNumRecordDTO.setOrganSign("ZHL00004323");
//		couponNumRecordDTO.setTime(now.getTime());
//		couponNumRecordDTO.setType(0);
//		heyeReportService.countHeyeCouponNum(couponNumRecordDTO);
//		couponNumRecordDTO.setNum(10L);
//		couponNumRecordDTO.setOrganSign("ZHL00004324");
//		couponNumRecordDTO.setTime(now.getTime());
//		couponNumRecordDTO.setType(1);
//		heyeReportService.countHeyeCouponNum(couponNumRecordDTO);
//
//		now.set(Calendar.DATE,now.get(Calendar.DATE)+1);
//		now.set(Calendar.MONTH,now.get(Calendar.MONTH)-1);
//		couponNumRecordDTO.setNum(1L);
//		couponNumRecordDTO.setOrganSign("ZHL00004323");
//		couponNumRecordDTO.setTime(now.getTime());
//		couponNumRecordDTO.setType(0);
//		heyeReportService.countHeyeCouponNum(couponNumRecordDTO);
//		couponNumRecordDTO.setNum(10L);
//		couponNumRecordDTO.setOrganSign("ZHL00004324");
//		couponNumRecordDTO.setTime(now.getTime());
//		couponNumRecordDTO.setType(1);
//		heyeReportService.countHeyeCouponNum(couponNumRecordDTO);
		
		// ----- 出错数据
//		couponNumRecordDTO.setNum(1L);
//		couponNumRecordDTO.setOrganSign("123");
//		couponNumRecordDTO.setTime(now.getTime());
//		couponNumRecordDTO.setType(0);
//		heyeReportService.countHeyeCouponNum(couponNumRecordDTO);
//		couponNumRecordDTO.setNum(10L);
//		couponNumRecordDTO.setOrganSign("234");
//		couponNumRecordDTO.setTime(now.getTime());
//		couponNumRecordDTO.setType(1);
//		heyeReportService.countHeyeCouponNum(couponNumRecordDTO);
	}
	
	@Test
	public void testCountHeyeCouponSmsNum(){
		HeyeSmsRecord heyeSmsRecord = new HeyeSmsRecord();
		heyeSmsRecord.setSmsCount(1);
		heyeSmsRecord.setOrganSign("ZHL00004323");
		heyeSmsRecord.setCreateTime(new Date());
		heyeReportService.countHeyeCouponSmsNum(heyeSmsRecord);
		heyeSmsRecord.setSmsCount(1);
		heyeSmsRecord.setOrganSign("ZHL00004324");
		heyeSmsRecord.setCreateTime(new Date());
		heyeReportService.countHeyeCouponSmsNum(heyeSmsRecord);
		
//		Calendar now = Calendar.getInstance();
//		now.set(Calendar.DATE,now.get(Calendar.DATE)-1);
//		heyeSmsRecord.setSmsCount(1);
//		heyeSmsRecord.setOrganSign("ZHL00004323");
//		heyeSmsRecord.setCreateTime(now.getTime());
//		heyeReportService.countHeyeCouponSmsNum(heyeSmsRecord);
//		heyeSmsRecord.setSmsCount(1);
//		heyeSmsRecord.setOrganSign("ZHL00004324");
//		heyeSmsRecord.setCreateTime(now.getTime());
//		heyeReportService.countHeyeCouponSmsNum(heyeSmsRecord);
//
//		now.set(Calendar.DATE,now.get(Calendar.DATE)+1);
//		now.set(Calendar.MONTH,now.get(Calendar.MONTH)-1);
//		heyeSmsRecord.setSmsCount(1);
//		heyeSmsRecord.setOrganSign("ZHL00004323");
//		heyeSmsRecord.setCreateTime(now.getTime());
//		heyeReportService.countHeyeCouponSmsNum(heyeSmsRecord);
//		heyeSmsRecord.setSmsCount(1);
//		heyeSmsRecord.setOrganSign("ZHL00004324");
//		heyeSmsRecord.setCreateTime(now.getTime());
//		heyeReportService.countHeyeCouponSmsNum(heyeSmsRecord);
	}
	
	@Test
	public void testCountHeyeMemberConsumeNumAmountAndSKU(){
		String s = "{\"orderMap\":{{\"billAmount\":100,\"billNo\":\"2021331142601\",\"cardNo\":\"327165709\",\"cashierUserId\":117557,\"createUser\":\"117160\",\"headquartersOrganSign\":\"ZHL00005897\",\"id\":10331,\"memberId\":100892,\"memberOrganSign\":\"ZHL00005971\",\"organSign\":\"ZHL00005973\",\"sellTime\":1617170480000,\"updateUser\":\"117160\"}:[{\"actualAmount\":100,\"actualUnitAmount\":20,\"billNo\":\"2021331142601\",\"costPricee\":22.65,\"createUser\":\"117160\",\"orderId\":10331,\"organSign\":\"ZHL00005973\",\"productCode\":\"ZHL3\",\"productNum\":5,\"updateUser\":\"117160\"}],{\"billAmount\":100,\"billNo\":\"2021331142603\",\"cardNo\":\"327165710\",\"cashierUserId\":117557,\"createUser\":\"117160\",\"headquartersOrganSign\":\"ZHL00005897\",\"id\":10332,\"memberId\":100893,\"memberOrganSign\":\"ZHL00005971\",\"organSign\":\"ZHL00005971\",\"sellTime\":1617170480000,\"updateUser\":\"117160\"}:[{\"actualAmount\":100,\"actualUnitAmount\":20,\"billNo\":\"2021331142603\",\"costPricee\":22.65,\"createUser\":\"117160\",\"orderId\":10332,\"organSign\":\"ZHL00005971\",\"productCode\":\"ZHL10\",\"productNum\":5,\"updateUser\":\"117160\"}],{\"billAmount\":200,\"billNo\":\"2021331142604\",\"cardNo\":\"327165709\",\"cashierUserId\":117557,\"createUser\":\"117160\",\"headquartersOrganSign\":\"ZHL00005897\",\"id\":10333,\"memberId\":100892,\"memberOrganSign\":\"ZHL00005971\",\"organSign\":\"ZHL00005970\",\"sellTime\":1617069680000,\"updateUser\":\"117160\"}:[{\"actualAmount\":200,\"actualUnitAmount\":20,\"billNo\":\"2021331142604\",\"costPricee\":22.65,\"createUser\":\"117160\",\"orderId\":10333,\"organSign\":\"ZHL00005970\",\"productCode\":\"ZHL13\",\"productNum\":10,\"updateUser\":\"117160\"}],{\"billAmount\":200,\"billNo\":\"2021331142602\",\"cardNo\":\"327165709\",\"cashierUserId\":117557,\"createUser\":\"117160\",\"headquartersOrganSign\":\"ZHL00005897\",\"id\":10334,\"memberId\":100892,\"memberOrganSign\":\"ZHL00005971\",\"organSign\":\"ZHL00005973\",\"sellTime\":1617170480000,\"updateUser\":\"117160\"}:[{\"actualAmount\":200,\"actualUnitAmount\":20,\"billNo\":\"2021331142602\",\"costPricee\":22.65,\"createUser\":\"117160\",\"orderId\":10334,\"organSign\":\"ZHL00005973\",\"productCode\":\"ZHL4\",\"productNum\":10,\"updateUser\":\"117160\"}],{\"billAmount\":100,\"billNo\":\"2021331142605\",\"cardNo\":\"327165710\",\"cashierUserId\":117557,\"createUser\":\"117160\",\"headquartersOrganSign\":\"ZHL00005897\",\"id\":10335,\"memberId\":100893,\"memberOrganSign\":\"ZHL00005971\",\"organSign\":\"ZHL00005970\",\"sellTime\":1617170480000,\"updateUser\":\"117160\"}:[{\"actualAmount\":100,\"actualUnitAmount\":20,\"billNo\":\"2021331142605\",\"costPricee\":22.65,\"createUser\":\"117160\",\"orderId\":10335,\"organSign\":\"ZHL00005970\",\"productCode\":\"ZHL16\",\"productNum\":5,\"updateUser\":\"117160\"}]},\"taskIdCount\":\"1005634_count\",\"taskIdList\":\"1005634_list\"}";
		OrderMessageDTO orderMessageDTO = JSONObject.parseObject(s, new TypeReference<OrderMessageDTO>() {});
		Map<HeyeOrder, List<HeyeOrderDetail>> orderListMap = orderMessageDTO.getOrderMap();
		for(HeyeOrder order : orderListMap.keySet()) {
			try {
				heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order, orderListMap.get(order));
			} catch (Exception e) {
//				log.info("订单数据统计出错：{} {}", JSONObject.toJSONString(order), JSONObject.toJSON(orderListMap.get(order)));
			}
		}
//		HeyeOrder order = new HeyeOrder();
//		order2.setSellTime(new Date());
//		order2.setOrganSign("ZHL00004534");
//		order2.setHeadquartersOrganSign("ZHL00004346");
//		order2.setCardNo("o1");
//		order2.setBillAmount(BigDecimal.valueOf(1));
//		List<HeyeOrderDetail> orderDetails = new ArrayList<>();
//		HeyeOrderDetail orderDetail = new HeyeOrderDetail();
//		orderDetail.setProductCode("p123");
//		orderDetails.add(orderDetail);
//		orderDetail.setProductCode("p234");
//		orderDetails.add(orderDetail);
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails);
//		order2.setCardNo("o2");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails);
//		order2.setCardNo("o3");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails);
//		order2.setCardNo("o4");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails);
//		order2.setCardNo("o5");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails);
//		order2.setCardNo("o6");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails);
//		order2.setCardNo("o7");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails);
//		order2.setCardNo("o8");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails);
//		order2.setCardNo("o9");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails);
//		order2.setCardNo("10");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails);
//
//		order2 = new HeyeOrder();
//		order2.setSellTime(new Date());
//		order2.setOrganSign("ZHL00004324");
//		order2.setHeadquartersOrganSign("ZHL00004322");
//		order2.setCardNo("o456");
//		order2.setBillAmount(BigDecimal.valueOf(1));
//		orderDetails = new ArrayList<>();
//		orderDetail = new HeyeOrderDetail();
//		orderDetail.setProductCode("p123");
//		orderDetails.add(orderDetail);
//
//		// {{"billAmount":1,"cardNo":"o123","organSign":"ZHL00004323","sellTime":1616045128553}:[{"productCode":"p234"},{"$ref":"$.HeyeOrder\\(id\\=null\\,\\ billNo\\=null\\,\\ organSign\\=ZHL00004323\\,\\ billAmount\\=1\\,\\ billNum\\=null\\,\\ sellTime\\=Thu\\ Mar\\ 18\\ 13\\:25\\:28\\ GMT\\+08\\:00\\ 2021\\,\\ cashierUserId\\=null\\,\\ cashierName\\=null\\,\\ sellerUserId\\=null\\,\\ sellerName\\=null\\,\\ memberId\\=null\\,\\ cardNo\\=o123\\,\\ billSource\\=null\\,\\ createUser\\=null\\,\\ createTime\\=null\\,\\ updateUser\\=null\\,\\ updateTime\\=null\\,\\ yn\\=null\\)[0]"}],{"billAmount":1,"cardNo":"o456","organSign":"ZHL00004324","sellTime":1616045128554}:[{"productCode":"p123"}]}
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails);
//
//		Calendar now = Calendar.getInstance();
//		now.set(Calendar.DATE,now.get(Calendar.DATE)-1);
//		HeyeOrder order1 = new HeyeOrder();
//		order1.setSellTime(now.getTime());
//		order1.setOrganSign("ZHL00004323");
//		order1.setHeadquartersOrganSign("ZHL00004322");
//		order1.setCardNo("o123");
//		order1.setBillAmount(BigDecimal.valueOf(1));
//		List<HeyeOrderDetail> orderDetails1 = new ArrayList<>();
//		HeyeOrderDetail orderDetail1 = new HeyeOrderDetail();
//		orderDetail1.setProductCode("p123");
//		orderDetails1.add(orderDetail1);
//		orderDetail1.setProductCode("p234");
//		orderDetails1.add(orderDetail1);
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order1, orderDetails1);
//
//		order1 = new HeyeOrder();
//		order1.setSellTime(now.getTime());
//		order1.setOrganSign("ZHL00004324");
//		order1.setHeadquartersOrganSign("ZHL00004322");
//		order1.setCardNo("o456");
//		order1.setBillAmount(BigDecimal.valueOf(1));
//		orderDetails1 = new ArrayList<>();
//		orderDetail1 = new HeyeOrderDetail();
//		orderDetail1.setProductCode("p123");
//		orderDetails1.add(orderDetail1);
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order1, orderDetails1);
//
//		now.set(Calendar.DATE,now.get(Calendar.DATE)+1);
//		now.set(Calendar.MONTH,now.get(Calendar.MONTH)-1);
//		HeyeOrder order2 = new HeyeOrder();
//		order2.setSellTime(now.getTime());
//		order2.setOrganSign("ZHL00004534");
//		order2.setHeadquartersOrganSign("ZHL00004346");
//		order2.setCardNo("o1111");
//		order2.setBillAmount(BigDecimal.valueOf(1));
//		List<HeyeOrderDetail> orderDetails2 = new ArrayList<>();
//		HeyeOrderDetail orderDetail2 = new HeyeOrderDetail();
//		orderDetail2.setProductCode("p123");
//		orderDetails2.add(orderDetail2);
//		orderDetail2.setProductCode("p234");
//		orderDetails2.add(orderDetail2);
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails2);
//		order2.setCardNo("o12");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails2);
//		order2.setCardNo("o13");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails2);
//		order2.setCardNo("o14");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails2);
//		order2.setCardNo("o15");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails2);
//		order2.setCardNo("o16");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails2);
//		order2.setCardNo("o17");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails2);
//		order2.setCardNo("o18");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails2);
//		order2.setCardNo("o19");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails2);
//		order2.setCardNo("o110");
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails2);
		
//		order2.setOrganSign("ZHL00005770");

//		order2 = new HeyeOrder();
//		order2.setSellTime(now.getTime());
//		order2.setOrganSign("ZHL00004324");
//		order2.setHeadquartersOrganSign("ZHL00004322");
//		order2.setCardNo("o456");
//		order2.setBillAmount(BigDecimal.valueOf(1));
//		orderDetails2 = new ArrayList<>();
//		orderDetail2 = new HeyeOrderDetail();
//		orderDetail2.setProductCode("p123");
//		orderDetails2.add(orderDetail2);
//		heyeReportService.countHeyeMemberConsumeNumAmountAndSKU(order2, orderDetails2);
	}
	
	@Test
	public void testQueryDataStatForPc(){
		QueryDataStatForPcRespDTO queryDataStatForPcRespDTO = heyeReportService.queryDataStatForPc(null, "ZHL00005752");
		String str = JSONObject.toJSONString(queryDataStatForPcRespDTO);
		System.out.println(str);
	}
	
	@Test
	public void testPcMemberStatForPcArtificially(){
		PcMemberStatForPcArtificiallyReqDTO param = new PcMemberStatForPcArtificiallyReqDTO();
		param.setOrganSign("ZHL00005970");
		Calendar now = Calendar.getInstance();
		now.set(2021, 2, 26, 0, 0, 0);
		Date startTime = now.getTime();
		now.set(2021, 2, 26, 23, 59, 59);
		Date endTime = now.getTime();
		param.setStartTime(startTime);
		param.setEndTime(endTime);
		boolean b = heyeReportService.pcMemberStatForPcArtificially(param);
		System.out.println("===============> 是否刷新成功: "+b);
	}
	
}
