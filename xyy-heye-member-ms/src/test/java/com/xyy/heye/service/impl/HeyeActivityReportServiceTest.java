package com.xyy.heye.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.xyy.heye.excel.dto.OrderMessageDTO;
import com.xyy.heye.po.HeyeOrder;
import com.xyy.heye.po.HeyeOrderDetail;
import com.xyy.heye.service.HeyeActivityReportService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@ActiveProfiles("test")
@RunWith(SpringRunner.class)
@SpringBootTest
public class HeyeActivityReportServiceTest {

    @Resource
    private HeyeActivityReportService activityReportService;

    @Test
    public void testUpdateActivityOrderInfoData(){
        String msg = "{\"orderMap\":{{\"billAmount\":10,\"billCostAmount\":10,\"billNo\":\"2021407077123\",\"billNum\":10,\"billSource\":0,\"cardNo\":\"HY0422014\",\"cashierName\":\"钟吴\",\"cashierUserId\":117316,\"createUser\":\"117443\",\"headquartersOrganSign\":\"ZHL00005897\",\"id\":27,\"memberId\":102042,\"memberOrganSign\":\"ZHL00005965\",\"organSign\":\"ZHL00005965\",\"sellTime\":1615518061000,\"sellerName\":\"钟吴\",\"sellerUserId\":117316,\"updateUser\":\"117443\"}:[{\"actualAmount\":10,\"actualUnitAmount\":1,\"billNo\":\"2021407077123\",\"costPricee\":1,\"createUser\":\"117443\",\"orderId\":27,\"organSign\":\"ZHL00005965\",\"productCode\":\"ZHL19\",\"productNum\":10,\"updateUser\":\"117443\"}],{\"billAmount\":20,\"billCostAmount\":20,\"billNo\":\"2021407077124\",\"billNum\":20,\"billSource\":0,\"cardNo\":\"HY0422014\",\"cashierName\":\"钟吴\",\"cashierUserId\":117316,\"createUser\":\"117443\",\"headquartersOrganSign\":\"ZHL00005897\",\"id\":28,\"memberId\":102042,\"memberOrganSign\":\"ZHL00005965\",\"organSign\":\"ZHL00005965\",\"sellTime\":1616040061000,\"sellerName\":\"钟吴\",\"sellerUserId\":117316,\"updateUser\":\"117443\"}:[{\"actualAmount\":20,\"actualUnitAmount\":1,\"billNo\":\"2021407077124\",\"costPricee\":1,\"createUser\":\"117443\",\"orderId\":28,\"organSign\":\"ZHL00005965\",\"productCode\":\"ZHL19\",\"productNum\":20,\"updateUser\":\"117443\"}],{\"billAmount\":25,\"billCostAmount\":25,\"billNo\":\"2021407077125\",\"billNum\":25,\"billSource\":0,\"cardNo\":\"HY0422015\",\"cashierName\":\"钟吴\",\"cashierUserId\":117316,\"createUser\":\"117443\",\"headquartersOrganSign\":\"ZHL00005897\",\"id\":29,\"memberId\":102043,\"memberOrganSign\":\"ZHL00005965\",\"organSign\":\"ZHL00005965\",\"sellTime\":1615867261000,\"sellerName\":\"钟吴\",\"sellerUserId\":117316,\"updateUser\":\"117443\"}:[{\"actualAmount\":25,\"actualUnitAmount\":1,\"billNo\":\"2021407077125\",\"costPricee\":1,\"createUser\":\"117443\",\"orderId\":29,\"organSign\":\"ZHL00005965\",\"productCode\":\"ZHL19\",\"productNum\":25,\"updateUser\":\"117443\"}],{\"billAmount\":15,\"billCostAmount\":15,\"billNo\":\"2021407077126\",\"billNum\":15,\"billSource\":0,\"cardNo\":\"HY0422016\",\"cashierName\":\"钟吴\",\"cashierUserId\":117316,\"createUser\":\"117443\",\"headquartersOrganSign\":\"ZHL00005897\",\"id\":30,\"memberId\":102044,\"memberOrganSign\":\"ZHL00005965\",\"organSign\":\"ZHL00005965\",\"sellTime\":1615608061000,\"sellerName\":\"钟吴\",\"sellerUserId\":117316,\"updateUser\":\"117443\"}:[{\"actualAmount\":15,\"actualUnitAmount\":1,\"billNo\":\"2021407077126\",\"costPricee\":1,\"createUser\":\"117443\",\"orderId\":30,\"organSign\":\"ZHL00005965\",\"productCode\":\"ZHL19\",\"productNum\":15,\"updateUser\":\"117443\"}]},\"taskIdCount\":\"1006714_count\",\"taskIdList\":\"1006714_list\"}";
        OrderMessageDTO orderMessageDTO = JSONObject.parseObject(msg, OrderMessageDTO.class);
        Map<HeyeOrder, List<HeyeOrderDetail>> orderMap = orderMessageDTO.getOrderMap();
        List<HeyeOrder> orderList = new ArrayList<>(orderMap.keySet());
        activityReportService.updateActivityOrderInfoData(orderList);
    }
}
