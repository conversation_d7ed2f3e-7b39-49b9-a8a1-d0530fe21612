package com.xyy.heye.excel;

import com.alibaba.fastjson.JSONArray;
import com.xyy.emule.dto.InitSubTaskDto;
import com.xyy.heye.excel.upload.ImportHeyeMemberOrderInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@ActiveProfiles("test")
@RunWith(SpringRunner.class)
@SpringBootTest
public class HeyeOrderImportTest {

    @Autowired
    private ImportHeyeMemberOrderInfo importHeyeMemberOrderInfo;


    @Test
    public void testImport(){
        List<InitSubTaskDto> list = new ArrayList<>();
        InitSubTaskDto initSubTaskDto = new InitSubTaskDto();
        initSubTaskDto.setOperId("114290");
        initSubTaskDto.setOrganSign("ZHL00005897");
        initSubTaskDto.setTaskId(1l);
        JSONArray array = new JSONArray();
        array.add("325190904");
        array.add("2021407004511");
        array.add("2021/4/7 3:15:01");
        array.add("E012838");
        array.add("");
        array.add("ZHL25");
        array.add("30");
        array.add("50");
        array.add("40");
        array.add("1650");
        array.add("12345678913");
        initSubTaskDto.setTaskInfo(array.toJSONString());
        list.add(initSubTaskDto);
        List<InitSubTaskDto> initSubTaskDtos = importHeyeMemberOrderInfo.synchImportData(list);
        initSubTaskDtos.forEach(item ->{
            System.out.println(item.getErrorInfo());
        });
    }
}
