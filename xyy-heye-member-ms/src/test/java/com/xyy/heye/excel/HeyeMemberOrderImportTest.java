package com.xyy.heye.excel;

import com.alibaba.fastjson.JSONArray;
import com.xyy.emule.dto.InitSubTaskDto;
import com.xyy.heye.excel.upload.ImportHeyeMemberOrderInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@ActiveProfiles("test")
@RunWith(SpringRunner.class)
@SpringBootTest
public class HeyeMemberOrderImportTest {

    @Autowired
    private ImportHeyeMemberOrderInfo importHeyeMemberOrderInfo;

    @Test
    public void testImport(){
        List<InitSubTaskDto> list = new ArrayList<>();
        InitSubTaskDto taskDto = new InitSubTaskDto();
        taskDto.setTaskId(1111111l);
        taskDto.setOrganSign("ZHL00005897");
        taskDto.setOperId("117160");
        JSONArray taskInfo = new JSONArray();
        taskInfo.add("1234569578");
        taskInfo.add("2021407004588");
        taskInfo.add("2021/4/7 3:15:01");
        taskInfo.add("E012839");
        taskInfo.add("E012839");
        taskInfo.add("ZHL25");
        taskInfo.add("30");
        taskInfo.add("50");
        taskInfo.add("40");
        taskInfo.add("1650");
        taskInfo.add("HY2021040200017");
        taskDto.setTaskInfo(taskInfo.toJSONString());
        list.add(taskDto);
        List<InitSubTaskDto> initSubTaskDtos = importHeyeMemberOrderInfo.synchImportData(list);
    }
}
