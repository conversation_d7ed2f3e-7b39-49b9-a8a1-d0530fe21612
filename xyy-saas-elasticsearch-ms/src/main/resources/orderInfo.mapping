{"properties": {"id": {"type": "integer"}, "status": {"type": "keyword"}, "bastYn": {"type": "integer"}, "prescriptionYn": {"type": "integer"}, "promotionId": {"type": "integer"}, "ticketNo": {"type": "keyword"}, "sellerId": {"type": "integer"}, "ticketClerkId": {"type": "integer"}, "memberId": {"type": "integer"}, "receivableAmount": {"type": "double"}, "memberActualAmount": {"type": "double"}, "actualAmount": {"type": "double"}, "discountAmount": {"type": "double"}, "gatherAmount": {"type": "double"}, "changeAmount": {"type": "double"}, "costAmount": {"type": "double"}, "grossProfitAmount": {"type": "double"}, "totalNumber": {"type": "double"}, "cashPay": {"type": "double"}, "wechatPay": {"type": "double"}, "aliPay": {"type": "double"}, "medicarePay": {"type": "double"}, "unionPay": {"type": "double"}, "extractStatus": {"type": "integer"}, "buyerName": {"type": "keyword"}, "buyerIdcard": {"type": "keyword"}, "buyerMobile": {"type": "keyword"}, "createUser": {"type": "keyword"}, "createTime": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "updateUser": {"type": "keyword"}, "updateTime": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "yn": {"type": "integer"}, "upload": {"type": "integer"}, "organSign": {"type": "keyword"}, "baseVersion": {"type": "integer"}, "guid": {"type": "keyword"}, "cutAmount": {"type": "double"}, "beforeCutAmount": {"type": "double"}, "uploadDate": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "storedValuePay": {"type": "double"}, "otherPay": {"type": "double"}, "uploadTime": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "memberGuid": {"type": "keyword"}, "payStatus": {"type": "integer"}, "togetherPay": {"type": "double"}, "oldGuid": {"type": "keyword"}, "orderCanal": {"type": "integer"}, "couponInfoId": {"type": "integer"}, "couponPref": {"type": "keyword"}, "discountCouponAmount": {"type": "double"}}}