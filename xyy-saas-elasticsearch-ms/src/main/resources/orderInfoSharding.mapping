{"properties": {"id": {"type": "long"}, "guid": {"type": "keyword"}, "ticketNo": {"type": "keyword"}, "commerceOrderNumber": {"type": "keyword"}, "organSign": {"type": "keyword"}, "headquartersOrganSign": {"type": "keyword"}, "orderCanal": {"type": "integer"}, "orderSource": {"type": "integer"}, "status": {"type": "keyword"}, "payStatus": {"type": "integer"}, "extractStatus": {"type": "integer"}, "receivableAmount": {"type": "double"}, "actualAmount": {"type": "double"}, "diffValue": {"type": "double"}, "cutAmount": {"type": "double"}, "packingFree": {"type": "double"}, "platformPreferentialAmount": {"type": "double"}, "freightAmount": {"type": "double"}, "pointDeductAmount": {"type": "double"}, "discountAmount": {"type": "double"}, "changeAmount": {"type": "double"}, "lotNumberCostPrice2": {"type": "double"}, "costAmount": {"type": "double"}, "grossProfitAmount": {"type": "double"}, "gatherAmount": {"type": "double"}, "beforeCutAmount": {"type": "double"}, "discountCouponAmount": {"type": "double"}, "otherPay": {"type": "double"}, "medicarePay": {"type": "double"}, "unionPay": {"type": "double"}, "togetherPay": {"type": "double"}, "storedValuePay": {"type": "double"}, "wechatPay": {"type": "double"}, "aliPay": {"type": "double"}, "cashPay": {"type": "double"}, "totalNumber": {"type": "double"}, "couponInfoId": {"type": "integer"}, "couponPref": {"type": "keyword"}, "memberGuid": {"type": "keyword"}, "memberId": {"type": "integer"}, "memberType": {"type": "integer"}, "yn": {"type": "integer"}, "createTime": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "createUser": {"type": "keyword"}, "sellerId": {"type": "integer"}, "updateTime": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "updateUser": {"type": "keyword"}}}