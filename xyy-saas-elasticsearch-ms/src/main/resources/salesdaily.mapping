{"properties": {"organSign": {"type": "keyword"}, "oneUsed": {"type": "byte"}, "addTime": {"type": "text", "fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}}, "endDate": {"type": "text", "fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}}, "actualAmount": {"type": "double"}, "loginTimes": {"type": "integer"}, "costAmount": {"type": "double"}, "customerPrice": {"type": "double"}, "lastLoginTime": {"format": "yyyy-MM-dd HH:mm:ss", "type": "date"}, "organName": {"type": "keyword"}, "userDays": {"type": "text", "fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}}, "createTime": {"format": "yyyy-MM-dd HH:mm:ss", "type": "date"}, "salesAmount": {"type": "text", "fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}}, "purchasingTimes": {"type": "integer"}, "strDate": {"type": "text", "fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}}, "id": {"type": "integer"}, "visitorNumbers": {"type": "integer"}, "add_time": {"format": "yyyy-MM-dd HH:mm:ss", "type": "date"}, "grossProfit": {"type": "double"}, "productPrice": {"type": "double"}, "createDate": {"type": "text", "fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}}, "customerCount": {"type": "double"}}}