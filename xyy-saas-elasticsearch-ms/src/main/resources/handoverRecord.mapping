{"properties": {"id": {"type": "integer"}, "handover": {"type": "keyword"}, "handoverTime": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "handoverAmount": {"type": "double"}, "receivableAmount": {"type": "double"}, "actualAmount": {"type": "double"}, "discountAmount": {"type": "double"}, "gatherAmount": {"type": "double"}, "changeAmount": {"type": "double"}, "costAmount": {"type": "double"}, "grossProfitAmount": {"type": "double"}, "cashPay": {"type": "double"}, "aliPay": {"type": "double"}, "wechatPay": {"type": "double"}, "medicarePay": {"type": "double"}, "unionPay": {"type": "double"}, "createUser": {"type": "keyword"}, "createTime": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "updateUser": {"type": "keyword"}, "updateTime": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "yn": {"type": "integer"}, "handoverYn": {"type": "keyword"}, "baseVersion": {"type": "integer"}, "organSign": {"type": "keyword"}, "cutAmount": {"type": "double"}, "returnAmount": {"type": "double"}, "storedValuePay": {"type": "double"}, "otherPay": {"type": "double"}, "cashboxAmount": {"type": "double"}, "guid": {"type": "keyword"}, "togetherAmount": {"type": "double"}, "rechargeAmount": {"type": "double"}, "realityRechargeAmount": {"type": "double"}, "subRealityRechargeAmount": {"type": "double"}, "largessRechargeAmount": {"type": "double"}, "subLargessRechargeAmount": {"type": "double"}, "orderCount": {"type": "integer"}, "discountCouponAmount": {"type": "double"}}}