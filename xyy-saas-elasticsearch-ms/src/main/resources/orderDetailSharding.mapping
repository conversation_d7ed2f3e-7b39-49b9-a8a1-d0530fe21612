{"properties": {"id": {"type": "long"}, "ticketNo": {"type": "keyword"}, "headquartersOrganSign": {"type": "keyword"}, "organSign": {"type": "keyword"}, "orderId": {"type": "long"}, "guid": {"type": "keyword"}, "grossProfitAmount": {"type": "double"}, "discountAmount": {"type": "double"}, "beforeCutAmount": {"type": "double"}, "batchCostPrice2": {"type": "double"}, "actualAmount": {"type": "double"}, "productCostAmount": {"type": "double"}, "platformPreferentialAmount": {"type": "double"}, "shareCouponAmount": {"type": "double"}, "pointDeductAmount": {"type": "double"}, "actualUnitAmount": {"type": "double"}, "ouputTaxRate": {"type": "double"}, "afterCutPrice": {"type": "double"}, "receivableAmount": {"type": "double"}, "productNum": {"type": "double"}, "productPrice": {"type": "double"}, "productId": {"type": "long"}, "commonName": {"type": "keyword"}, "approvalNumber": {"type": "keyword"}, "dosageFormName": {"type": "keyword"}, "prescriptionClassification": {"type": "keyword"}, "productCanal": {"type": "integer"}, "productType": {"type": "long"}, "productTypeName": {"type": "keyword"}, "productCode": {"type": "keyword"}, "productManufacturer": {"type": "keyword"}, "productOriginAddress": {"type": "keyword"}, "productName": {"type": "keyword"}, "productPharmacyPref": {"type": "keyword"}, "productRetailPrice": {"type": "double"}, "productSpecifications": {"type": "keyword"}, "productUnit": {"type": "keyword"}, "abcDividingKey": {"type": "keyword"}, "bastYn": {"type": "integer"}, "diySixLevels": {"type": "keyword"}, "providerName": {"type": "keyword"}, "orderInfoAliPay": {"type": "double"}, "orderInfoCashPay": {"type": "double"}, "orderInfoMedicarePay": {"type": "double"}, "orderInfoOtherPay": {"type": "double"}, "orderInfoStoredValuePay": {"type": "double"}, "orderInfoTogetherPay": {"type": "double"}, "orderInfoUnionPay": {"type": "double"}, "orderInfoWechatPay": {"type": "double"}, "orderInfoMemberId": {"type": "integer"}, "orderInfoMemberType": {"type": "integer"}, "orderInfoOrderSource": {"type": "integer"}, "orderInfoStatus": {"type": "integer"}, "mixedQuery": {"type": "text"}, "promotionPref": {"type": "keyword"}, "prescriptionNo": {"type": "keyword"}, "bastNo": {"type": "keyword"}, "positionId": {"type": "integer"}, "positionName": {"type": "keyword"}, "batchNo": {"type": "keyword"}, "isMainItem": {"type": "integer"}, "isMemberDay": {"type": "integer"}, "createTime": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "createUser": {"type": "keyword"}, "sellerId": {"type": "integer"}, "updateTime": {"type": "date", "format": "yyyy-MM-dd HH:mm:ss"}, "updateUser": {"type": "keyword"}, "updateUserName": {"type": "keyword"}, "yn": {"type": "integer"}}}