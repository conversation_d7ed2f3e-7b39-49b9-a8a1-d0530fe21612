package com.xyy.saas.elasticsearch.core.api.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.elasticsearc.core.api.ESProductStandardInfoApi;
import com.xyy.saas.elasticsearc.core.dto.ProductStandardInfoDto;
import com.xyy.saas.elasticsearch.core.config.ElasticSearchConfiguration;
import com.xyy.saas.elasticsearch.core.enums.IndexEnum;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.action.bulk.BulkRequestBuilder;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.AdminClient;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 商品标准信息查询
 * <AUTHOR>
 * @date 2020/10/13
 */
@Service(version = "0.0.1", timeout = 60000)
public class ESProductStandardInfoApiImpl implements ESProductStandardInfoApi {
    private static final Logger logger = LoggerFactory.getLogger(ESProductStandardInfoApiImpl.class);

    private static final String INDEX = IndexEnum.PRODUCT_STANDARD_INFO.getIndex();
    private static final String TYPE = IndexEnum.PRODUCT_STANDARD_INFO.getType();

    /**
     * 初始化创建索引
     *
     * @return
     */
    @Override
    public boolean createIndex(){
        try {
            AdminClient adminClient = ElasticSearchConfiguration.client.admin();
            BufferedReader br = new BufferedReader(new InputStreamReader(OrderElasticsearchInfoApiImpl.class.getResourceAsStream("/productStandardInfo.mapping")));
            String s = null;
            StringBuilder sb = new StringBuilder();
            while ((s = br.readLine()) != null) {
                sb.append(s);
            }
            adminClient.indices().prepareCreate(INDEX)
                    .setSettings(Settings.builder().put("index.number_of_shards", "10")
                            .put("index.number_of_replicas", "1")
                            .put("index.max_result_window", "100000")
                    )
                    .addMapping(
                            TYPE, sb.toString(), XContentType.JSON
                    ).get();
        } catch (Exception e) {
            logger.error("create index:{} error:{}", INDEX,e);
            return false;
        }
        return true;
    }

    /**
     * 插入或者覆盖数据
     *
     * @param list
     */
    @Override
    public void upsert(List<ProductStandardInfoDto> list){
        BulkRequestBuilder bulkRequestBuilder = ElasticSearchConfiguration.client.prepareBulk();
        list.forEach(dto -> {
            IndexRequest indexRequest = new IndexRequest(INDEX, TYPE, dto.getId().toString());
            indexRequest.source(JSON.toJSONString(dto), XContentType.JSON);
            bulkRequestBuilder.add(indexRequest);
        });
        BulkResponse responses = bulkRequestBuilder.get();
        if (responses.hasFailures()) {
            logger.error("插入数据有失败");
        }
    }

    /**
     * 匹配-条形码
     *
     * @param barCode
     * @return
     */
    @Override
    public List<ProductStandardInfoDto> queryByBarCode(String barCode){
        barCode = barCode.trim();
        TermQueryBuilder termQuery = QueryBuilders.termQuery("smallPackageCode", barCode);
        QueryBuilder queryBuilder = QueryBuilders.constantScoreQuery(termQuery);
        return this.queryByQueryBuilder(queryBuilder);
    }

    /**
     * 匹配-批准文号+规格
     *
     * @param approvalNo
     * @param specification
     * @return
     */
    @Override
    public List<ProductStandardInfoDto> queryByApprovalNoAndSpec(String approvalNo, String specification){
        approvalNo = approvalNo.trim();
        specification = specification.trim();
        // 1.直接精准匹配
        QueryBuilder queryBuilder = this.wrapApprovalNoAndSpec(approvalNo, specification, false);
        List<ProductStandardInfoDto> list = this.queryByQueryBuilder(queryBuilder);
        if (CollectionUtils.isNotEmpty(list)) {
            return list;
        }
        // 2.去掉规格大包装,精准匹配
        String specWithoutPack = this.removeSpecPack(specification);
        if (!specification.equals(specWithoutPack)) {
            queryBuilder = this.wrapApprovalNoAndSpec(approvalNo, specWithoutPack, false);
            list = this.queryByQueryBuilder(queryBuilder);
            if (CollectionUtils.isNotEmpty(list)) {
                return list;
            }
        }
        // 3.不去掉规格大包装,提取数字,正则精准匹配
        String regex = this.specificationRegex(specification);
        queryBuilder = this.wrapApprovalNoAndSpec(approvalNo, regex, true);
        list = this.queryByQueryBuilder(queryBuilder);
        if (CollectionUtils.isNotEmpty(list)) {
            return list;
        }
        // 4.去掉规格大包装,提取数字,正则精准匹配
        if (!specification.equals(specWithoutPack)) {
            regex = this.specificationRegex(specWithoutPack);
            queryBuilder = this.wrapApprovalNoAndSpec(approvalNo, regex, true);
            list = this.queryByQueryBuilder(queryBuilder);
        }
        return list;
    }

    /**
     * 组装QueryBuilder(批准文号+规格)
     * @param approvalNo
     * @param spec
     * @param isRegex
     * @return
     */
    private QueryBuilder wrapApprovalNoAndSpec(String approvalNo, String spec, boolean isRegex){
        // 外层bool
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        // 内层bool
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("approvalNo", approvalNo));
        if (isRegex) {
            boolQuery.must(QueryBuilders.regexpQuery("spec", spec));
        } else {
            boolQuery.must(QueryBuilders.termQuery("spec", spec));
        }
        // 加入到外层bool过滤器,跳过score计算
        queryBuilder.filter(boolQuery);
        return queryBuilder;
    }

    /**
     * 匹配-通用名称+生产厂家+规格
     *
     * @param commonName
     * @param manufacturerName
     * @param specification
     * @return
     */
    @Override
    public List<ProductStandardInfoDto> queryByNameAndSpec(String commonName, String manufacturerName, String specification){
        commonName = commonName.trim();
        manufacturerName = manufacturerName.trim();
        specification = specification.trim();
        // 1.规格不做处理,模糊匹配:通用名称+生产厂家+规格
        QueryBuilder queryBuilder = this.wrapNameAndSpec(commonName, manufacturerName, specification, false);
        List<ProductStandardInfoDto> list = this.queryByQueryBuilder(queryBuilder);
        if (CollectionUtils.isNotEmpty(list)) {
            return list;
        }
        // 2.规格去掉大包装,模糊匹配:通用名称+生产厂家+规格
        String specWithoutPack = this.removeSpecPack(specification);
        if (!specification.equals(specWithoutPack)) {
            queryBuilder = this.wrapNameAndSpec(commonName, manufacturerName, specWithoutPack, false);
            list = this.queryByQueryBuilder(queryBuilder);
            if (CollectionUtils.isNotEmpty(list)) {
                return list;
            }
        }
        // 3.规格不去大包装,提取数字,正则匹配,模糊匹配:通用名称+生产厂家+规格
        String regex = this.specificationRegex(specification);
        queryBuilder = this.wrapNameAndSpec(commonName, manufacturerName, regex, true);
        list = this.queryByQueryBuilder(queryBuilder);
        if (CollectionUtils.isNotEmpty(list)) {
            return list;
        }
        // 4.规格去掉大包装,提取数字,正则匹配,模糊匹配:通用名称+生产厂家+规格
        if (!specification.equals(specWithoutPack)) {
            regex = this.specificationRegex(specWithoutPack);
            queryBuilder = this.wrapNameAndSpec(commonName, manufacturerName, regex, true);
            list = this.queryByQueryBuilder(queryBuilder);
        }
        return list;
    }

    /**
     * 组装QueryBuilder(通用名称+生产厂家+规格)
     * @param commonName
     * @param manufacturerName
     * @param spec
     * @param isRegex
     * @return
     */
    private QueryBuilder wrapNameAndSpec(String commonName, String manufacturerName, String spec, boolean isRegex){
        // 外层bool
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        // 内层bool
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.wildcardQuery("generalName", "*" + commonName + "*"));
        boolQuery.must(QueryBuilders.wildcardQuery("manufacturerName", "*" + manufacturerName + "*"));
        if (isRegex) {
            boolQuery.must(QueryBuilders.regexpQuery("spec", spec));
        } else {
            boolQuery.must(QueryBuilders.termQuery("spec", spec));
        }
        // 加入到外层bool过滤器,跳过score计算
        queryBuilder.filter(boolQuery);
        return queryBuilder;
    }

    /**
     * 去掉规格大包装
     * @param specification
     * @return
     */
    private String removeSpecPack(String specification){
        if (specification.contains("*")){
            return specification.substring(0, specification.lastIndexOf("*"));
        }
        return specification;
    }

    /**
     * 根据组装好的QueryBuilder进行查询
     * @param queryBuilder
     * @return
     */
    private List<ProductStandardInfoDto> queryByQueryBuilder(QueryBuilder queryBuilder){
        SearchResponse response = ElasticSearchConfiguration.client.
                prepareSearch(INDEX).
                setTypes(TYPE).
                setQuery(queryBuilder)
                .setFrom(0)
                .setSize(10)
                .get();
        SearchHits searchHits = response.getHits();
        List<ProductStandardInfoDto> list = new ArrayList<>();
        if (searchHits.totalHits <= 0) {
            return null;
        }
        SearchHit[] hits = searchHits.getHits();
        for (SearchHit hit : hits) {
            Map<String, Object> source = hit.getSource();
            ProductStandardInfoDto dto = JSON.parseObject(JSON.toJSONString(source), ProductStandardInfoDto.class);
            list.add(dto);
        }
        return list;
    }

    /**
     * 非数字
     */
    private static final String NAN_REGEX = "[^\\d.]+";
    private static final Pattern NAN_PATTERN = Pattern.compile(NAN_REGEX);
    /**
     * 根据规则字段生成匹配正则
     * @param specification
     * @return
     */
    private String specificationRegex(String specification){
        Matcher matcher = NAN_PATTERN.matcher(specification);
        return matcher.replaceAll(NAN_REGEX).trim();
    }
}
