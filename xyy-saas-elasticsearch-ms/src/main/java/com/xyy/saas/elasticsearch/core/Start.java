package com.xyy.saas.elasticsearch.core;

import com.alibaba.dubbo.config.spring.context.annotation.EnableDubbo;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * Created by zh on 2018/6/27.
 */

@SpringBootApplication
@EnableDubbo
@EnableApolloConfig
@ComponentScan(basePackages={"com.xyy.*"})
public class Start {
    public static void main(String[] args) {
        //spring容器关闭时，如果没有检测到dubbo销毁线程启动，就等待一段时间
        System.setProperty("dubbo.start.notiry.waiting.millis","5000"); //默认3秒，启动时等待通知客服端时间
        /**
         * Springboot整合Elasticsearch 在项目启动前设置一下的属性，防止报错
         * 解决netty冲突后初始化client时还会抛出异常
         * java.lang.IllegalStateException: availableProcessors is already set to [4], rejecting [4]
         */
        System.setProperty("es.set.netty.runtime.available.processors", "false");
        //dubbo销毁线程结束后，等待一段时间，再继续执行spring容器的销毁
        System.setProperty("dubbo.shutownhook.notiry.waiting.millis","5000"); //默认1秒，停用时等待通知客服端时间
        System.setProperty("dubbo.service.shutdown.wait","15000"); //默认10秒，dubbo优雅停机最大停机时间，设置优雅停机超时时间，如果超时则强制关闭。
         SpringApplication.run(Start.class, args);
    }
}
