package com.xyy.saas.elasticsearch.core.util;

import com.alibaba.fastjson.JSON;
import com.xyy.saas.elasticsearch.core.enums.NewIndexEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;

@Slf4j
public class PartitioningAlgorithmUtils {
    //根据机构号分片索引
    public static String getPartitioningIndex(String organSign,NewIndexEnum newIndexEnum){
        organSign = organSign.replace("ZHL","0");
        int index = NumberUtils.toInt(organSign,0)%newIndexEnum.getDivisor();
        String indexName = newIndexEnum.getIndex();
        indexName = String.format(indexName, index);
        return indexName;
    }
    //根据机构号分片类型
    public static String getPartitioningType(String organSign,NewIndexEnum newIndexEnum){
        organSign = organSign.replace("ZHL","0");
        int index = NumberUtils.toInt(organSign,0)%newIndexEnum.getDivisor();
        String indexType = newIndexEnum.getType();
        indexType = String.format(indexType, index);
        return indexType;
    }

    //根据机构号分片类型
    public static String getPartitioningShard(String organSign,NewIndexEnum newIndexEnum){
        int index = Math.abs(organSign.hashCode());
        return index%newIndexEnum.getShard()+"";
    }

}
