package com.xyy.saas.elasticsearch.core.enums;

/**
 * Created by pc on 2019/1/26.
 */
public enum AppSalesDataEnum {


    /**
     * 按日周月查询经验数据
     */
    DAY(1),WEEK(2),MONTH(3);
    private int type;

    AppSalesDataEnum(int value){
        this.type = value;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public static AppSalesDataEnum getAppSalesDataType(int type){
        for (AppSalesDataEnum appSalesDataEnum : values()) {
            if(appSalesDataEnum.getType() == type){
                return appSalesDataEnum;
            }
        }
        return null;
    }
}
