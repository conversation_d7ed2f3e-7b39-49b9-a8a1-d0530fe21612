package com.xyy.saas.elasticsearch.core.api.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.elasticsearc.core.api.ESProductBaseinfoApi;
import com.xyy.saas.elasticsearc.core.dto.ProductBaseinfoDto;
import com.xyy.saas.elasticsearc.core.dto.ProductBaseinfoParamDto;
import com.xyy.saas.elasticsearch.core.config.ElasticSearchConfiguration;
import com.xyy.saas.elasticsearch.core.enums.IndexEnum;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequestBuilder;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/5/27 20:30
 * @Version 1.0
 */
@Slf4j
@Service(version = "1.0.0")
public class ESProductBaseinfoApiImpl implements ESProductBaseinfoApi {

    private static final String INDEX = IndexEnum.PRODUCT_BASEINFO.getIndex();
    private static final String TYPE = IndexEnum.PRODUCT_BASEINFO.getType();

    @Override
    public void upsert(List<ProductBaseinfoDto> baseinfoDtos) {
        BulkRequestBuilder bulkRequestBuilder = ElasticSearchConfiguration.client.prepareBulk();
        baseinfoDtos.forEach(dto -> {
            IndexRequest indexRequest = new IndexRequest(INDEX, TYPE, dto.getId().toString());
            indexRequest.source(JSON.toJSONString(dto), XContentType.JSON);
            bulkRequestBuilder.add(indexRequest);
        });
        BulkResponse responses = bulkRequestBuilder.get();
        if (responses.hasFailures()) {
            log.error("ESProductBaseinfoApiImpl插入数据有失败");
        }
    }

    @Override
    public List<ProductBaseinfoDto> queryBaseinfo(ProductBaseinfoParamDto paramDto,String organSign) {
        List<ProductBaseinfoDto> result = new ArrayList<>();
        if (null == paramDto){
            return result;
        }

        String productName = null == paramDto.getProductName()?"":paramDto.getProductName().trim();
        String manufacturer = null == paramDto.getManufacturer()?"":paramDto.getManufacturer().trim();
        String specifications = null == paramDto.getSpecifications()?"":paramDto.getSpecifications().trim();
        QueryBuilder queryBuilder = wrapProduct(productName, manufacturer, specifications, organSign ,paramDto.getIsHidden());
        result = queryByQueryBuilder(queryBuilder);
        return result;
    }

    /**
     * 组装QueryBuilder
     * @param productName
     * @param manufacturer
     * @param specifications
     * @return
     */
    private QueryBuilder wrapProduct(String productName, String manufacturer, String specifications,String organSign,Integer isHidden){

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (!StringUtils.isEmpty(productName)) {
            boolQuery.must(QueryBuilders.matchQuery("commonName", productName));
        }
        if (!StringUtils.isEmpty(manufacturer)) {
            boolQuery.must(QueryBuilders.matchQuery("manufacturer", manufacturer));
        }
        if (!StringUtils.isEmpty(specifications)) {
            specifications = specifications.replaceAll("x","*");
            specifications = specifications.replaceAll("X","*");
            boolQuery.must(QueryBuilders.matchQuery("attributeSpecification", specifications));
        }
        if (null != isHidden && isHidden == 1){
            boolQuery.must(QueryBuilders.matchQuery("isHidden.keyword", "0"));
        }
        boolQuery.must(QueryBuilders.termQuery("organSign.keyword", organSign));
        boolQuery.must(QueryBuilders.termQuery("yn.keyword", "1"));
        boolQuery.must(QueryBuilders.termQuery("used.keyword", "1"));
        boolQuery.must(QueryBuilders.termQuery("status.keyword", "1"));

        return boolQuery;
    }

    /**
     * 根据组装好的QueryBuilder进行查询
     * @param queryBuilder
     * @return
     */
    private List<ProductBaseinfoDto> queryByQueryBuilder(QueryBuilder queryBuilder){
        SearchResponse response = ElasticSearchConfiguration.client.
                prepareSearch(INDEX).
                setTypes(TYPE)
                .setQuery(queryBuilder)
                .setFrom(0)
                .setSize(10)
                .get();
        SearchHits searchHits = response.getHits();
        List<ProductBaseinfoDto> list = new ArrayList<>();
        if (searchHits.totalHits <= 0) {
            return null;
        }
        SearchHit[] hits = searchHits.getHits();
        for (SearchHit hit : hits) {
            Map<String, Object> source = hit.getSource();
            ProductBaseinfoDto dto = JSON.parseObject(JSON.toJSONString(source), ProductBaseinfoDto.class);
            list.add(dto);
        }
        return list;
    }
}
