package com.xyy.saas.elasticsearch.core.api.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.elasticsearc.core.api.ESHytProductApi;
import com.xyy.saas.elasticsearc.core.dto.HytProductDto;
import com.xyy.saas.elasticsearch.core.config.ElasticSearchConfiguration;
import com.xyy.saas.elasticsearch.core.enums.IndexEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.bulk.BulkRequestBuilder;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.DeleteByQueryAction;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/14
 */
@Slf4j
@Service(version = "0.0.1")
public class ESHytProductApiImpl implements ESHytProductApi {
    private static final String INDEX = IndexEnum.HYT_SAAS_MERCHANT_PRODUCT.getIndex();
    private static final String TYPE = IndexEnum.HYT_SAAS_MERCHANT_PRODUCT.getType();

    /**
     * 新增/更新数据
     *
     * @param list
     */
    @Override
    public void upsert(List<HytProductDto> list){
        BulkRequestBuilder bulkRequestBuilder = ElasticSearchConfiguration.client.prepareBulk();
        list.forEach(dto -> {
            String esId = dto.getProductId() + "_" + dto.getMerchantId() + "_" + dto.getErpRelationId();
            IndexRequest indexRequest = new IndexRequest(INDEX, TYPE, esId);
            indexRequest.source(JSON.toJSONString(dto), XContentType.JSON);
            bulkRequestBuilder.add(indexRequest);
        });
        BulkResponse responses = bulkRequestBuilder.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE).get();
        if (responses.hasFailures()) {
            log.error("插入荷叶通商品数据有失败");
        }
    }

    /**
     * 按条件删除数据
     * @param condition
     */
    @Override
    public void deleteData(HytProductDto condition){
        if (condition == null) {
            return;
        }
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        if (condition.getId() != null) {
            queryBuilder.must(QueryBuilders.termQuery("id", condition.getId()));
        }
        if (StringUtils.isNotBlank(condition.getProductId())) {
            queryBuilder.must(QueryBuilders.termQuery("productId", condition.getProductId()));
        }
        if (condition.getMerchantId() != null) {
            queryBuilder.must(QueryBuilders.termQuery("merchantId", condition.getMerchantId()));
        }
        if (condition.getErpRelationId() != null) {
            queryBuilder.must(QueryBuilders.termQuery("erpRelationId", condition.getErpRelationId()));
        }
        if (queryBuilder.hasClauses()) {
            DeleteByQueryAction.INSTANCE
                    .newRequestBuilder(ElasticSearchConfiguration.client)
                    .filter(queryBuilder)
                    .source(INDEX)
                    .get();
        }
    }
}
