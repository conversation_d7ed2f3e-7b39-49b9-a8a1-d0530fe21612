package com.xyy.saas.elasticsearch.core.api.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.elasticsearc.core.api.ESPurchaseStatisticsApi;
import com.xyy.saas.elasticsearc.core.common.ESQueryBuilderConstructor;
import com.xyy.saas.elasticsearc.core.common.ESQueryBuilders;
import com.xyy.saas.elasticsearc.core.common.Page;
import com.xyy.saas.elasticsearc.core.dto.PurchaseDayStatisticsDto;
import com.xyy.saas.elasticsearc.core.dto.PurchaseDayStatisticsVoDto;
import com.xyy.saas.elasticsearch.core.service.ESPurchaseService;
import com.xyy.saas.elasticsearch.core.service.ElasticsearchReadService;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramInterval;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @annotation:
 * <AUTHOR>
 * @create 2019-08-08 16:01
 **/
@Service(version = "0.0.1")
public class ESPurchaseStatisticsApiImpl implements ESPurchaseStatisticsApi {
    private static final Logger logger = LoggerFactory.getLogger(ESPurchaseStatisticsApiImpl.class);
    @Autowired
    private ESPurchaseService esPurchaseService;
    @Override
    public <T> Page monthQueryByParma(PurchaseDayStatisticsVoDto dto, String index, String type) {
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        AggregationBuilder aggregationBuilderOrganSign = AggregationBuilders.terms("organsign").field("organsign");
        AggregationBuilder aggregationBuilderBindName = AggregationBuilders.terms("bindingName").field("bindingName");
        DateHistogramAggregationBuilder dateHistogramAggregationBuilder = AggregationBuilders.dateHistogram("statisticsTime");
        AggregationBuilder format = dateHistogramAggregationBuilder.dateHistogramInterval(DateHistogramInterval.MONTH).field("statisticsTime").format("yyyy-MM");
        aggregationBuilderOrganSign.subAggregation(esQueryBuilders.sum("taxAmountSum","taxAmountSum"));
        aggregationBuilderOrganSign.subAggregation(esQueryBuilders.sum("ybmPurchaseSum","ybmPurchaseSum"));
        format.subAggregation(aggregationBuilderBindName).subAggregation(aggregationBuilderOrganSign);
        if(!StringUtils.isEmpty(dto.getStartTime())&&!StringUtils.isEmpty(dto.getEndTime())){
            esQueryBuilders.range("statisticsTime",dto.getStartTime(),dto.getEndTime());
        }
        if(!StringUtils.isEmpty(dto.getStartTime())&&StringUtils.isEmpty(dto.getEndTime())){
            esQueryBuilders.rangeStart("statisticsTime",dto.getStartTime());
        }
        if(StringUtils.isEmpty(dto.getStartTime())&&!StringUtils.isEmpty(dto.getEndTime())){
            esQueryBuilders.rangeEnd("statisticsTime",dto.getEndTime());
        }
        if(!StringUtils.isEmpty(dto.getOrgansign())){
            esQueryBuilders.wildcardQuery("organsign",dto.getOrgansign());
        }
        if(!StringUtils.isEmpty(dto.getBindingName())){
            esQueryBuilders.wildcardQuery("bindingName",dto.getBindingName());
        }
        logger.info("采购月报ES查询条件esQueryBuilders"+ JSON.toJSONString(esQueryBuilders));
        constructor.setDesc("statisticsTime");
        constructor.must(esQueryBuilders);
        if(dto.getPageNum()>0&& dto.getPageSize()>0){
            constructor.setFrom(dto.getPageNum());
            constructor.setSize(dto.getPageSize());
            Page page = esPurchaseService.searchPage(constructor,format,PurchaseDayStatisticsDto.class, "saas_purchase_day_statistics", "saas_purchase_day_statistics");
            Long total = page.getTotal();
            Integer size = constructor.getSize();
            int m = total.intValue()/size;
            if(total.intValue()%size==0){
                page.setTotalPages(m);
            }else {
                page.setTotalPages(m+1);
            }
            return page;
        }else {
            constructor.setFrom(0);
            constructor.setSize(0);
            Page page = esPurchaseService.searchPage(constructor,format,PurchaseDayStatisticsDto.class, "saas_purchase_day_statistics", "saas_purchase_day_statistics");
            return page;
        }
    }
}

