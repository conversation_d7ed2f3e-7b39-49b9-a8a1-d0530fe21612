package com.xyy.saas.elasticsearch.core.service;

import com.xyy.saas.elasticsearc.core.dto.buss.ElasticFiveOrderDetailQueryDto;

import java.util.List;
import java.util.Map;

/**
 * @Author:chenxiaoyi
 * @Date:2023/04/25 10:44
 */
public interface ElasticFiveOrderDetailService {

    Map<String, Object> getEs5OrderDetailCommon(ElasticFiveOrderDetailQueryDto queryDto);

    Map<String, Object> getEs5OrderDetailCommonSum(ElasticFiveOrderDetailQueryDto queryDto);

    List<Map<String, Object>> getUnitProductSalesQuery(ElasticFiveOrderDetailQueryDto queryDTO);


}
