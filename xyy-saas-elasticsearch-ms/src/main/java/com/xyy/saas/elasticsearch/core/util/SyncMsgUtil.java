package com.xyy.saas.elasticsearch.core.util;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import java.util.List;

/**
 * 发生钉钉机器人报警
 * <AUTHOR>
 *
 */
public class SyncMsgUtil {

    
    public static OapiRobotSendResponse executeText(String url, String content, List<String> mobiles, String atAll) throws Exception {
    	DingTalkClient client = new DefaultDingTalkClient(url);
    	OapiRobotSendRequest request = new OapiRobotSendRequest();
    	request.setMsgtype("text");
    	OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
    	text.setContent(content);
    	request.setText(text);
    	OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
    	at.setAtMobiles(mobiles);
    	at.setIsAtAll(atAll);
    	request.setAt(at);
    	OapiRobotSendResponse response = client.execute(request);
    	return response;
    }

}
