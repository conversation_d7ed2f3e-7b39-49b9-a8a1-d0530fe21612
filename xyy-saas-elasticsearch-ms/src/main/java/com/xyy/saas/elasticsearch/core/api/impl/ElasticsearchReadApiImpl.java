package com.xyy.saas.elasticsearch.core.api.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.xyy.saas.elasticsearc.core.api.ElasticsearchReadApi;
import com.xyy.saas.elasticsearc.core.common.ESQueryBuilders;
import com.xyy.saas.elasticsearc.core.common.ESQueryNewConstructor;
import com.xyy.saas.elasticsearch.core.service.ElasticsearchReadService;
import com.xyy.saas.elasticsearc.core.common.ESQueryBuilderConstructor;
import com.xyy.saas.elasticsearc.core.common.Page;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * es查询apiImpl
 * Created by ouyangsiyong on 2017/8/2.
 */
@Service(version="0.0.1")
public class ElasticsearchReadApiImpl implements ElasticsearchReadApi {


    @Autowired
    private ElasticsearchReadService elasticsearchReadService;


    @Override
    public <T> Page searchPage(ESQueryBuilderConstructor constructor, Class<T> clazz, String index, String type) {
        return elasticsearchReadService.searchPage(constructor,clazz,index,type);
    }

    @Override
    public <T> Page searchNewPage(ESQueryNewConstructor constructor, String index, String type) {
        ESQueryBuilderConstructor builder = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        Map<String, Object> term = constructor.getTerm();
        Map<String, Object> wildcardQuery = constructor.getWildcardQuery();
        String asc = constructor.getAsc();
        String desc = constructor.getDesc();
        int size = constructor.getSize();
        int from = constructor.getFrom();
        for (String key : term.keySet()) {
            esQueryBuilders.term(key, term.get(key));
        }
        for (String key : wildcardQuery.keySet()) {
            esQueryBuilders.wildcardQuery(key, wildcardQuery.get(key));
        }
        builder.setDesc(desc);
        builder.setAsc(asc);
        builder.setSize(size);
        builder.setFrom(from);
        builder.must(esQueryBuilders);
        Page page = elasticsearchReadService.searchPage(builder, null, index, type);
        return page;
    }


    @Override
    public <T, S> Page searchPage(ESQueryBuilderConstructor constructor, Class<T> clazz, Class<S> subsetClazz, String index, String type) {
        return elasticsearchReadService.searchPage(constructor,clazz,subsetClazz,index,type);
    }

    @Override
    public Map<String, Object> searchPageMap(ESQueryBuilderConstructor constructor, String index, String type) {
        return elasticsearchReadService.searchPageMap(constructor,index,type);
    }

    /**
     * 分页查询，返回map
     *
     * @param constructor
     * @param organSign
     * @param headOrganSign
     * @return
     */
    @Override
    public Map<String, Object> searchIndexPageMap(ESQueryBuilderConstructor constructor, String organSign, String headOrganSign , String indexType) {
        return elasticsearchReadService.searchIndexPageMap(constructor,organSign,headOrganSign,indexType);
    }


    @Override
    public Map<Object, Object> statSearch(ESQueryBuilderConstructor constructor, String field, String groupBy, String index, String type) {
        return elasticsearchReadService.statSearch(constructor, field, groupBy, index, type);
    }

    @Override
    public Map<Object, Object> statSearch(ESQueryBuilderConstructor constructor, AggregationBuilder agg, String index, String type) {
        return elasticsearchReadService.statSearch(constructor, agg, index, type);
    }

    @Override
    public Map<String, Object> searchNewClusterPageMap(ESQueryBuilderConstructor constructor, String index, String type) {
        return elasticsearchReadService.searchNewClusterPageMap(constructor,index,type);
    }

    @Override
    public Map<String, Object> searchBatchIndexPageMap(ESQueryBuilderConstructor constructor, String organSign, String headquartersOrganSign, String indexType) {
        return elasticsearchReadService.searchBatchIndexPageMap(constructor,organSign,headquartersOrganSign,null);
    }
}
