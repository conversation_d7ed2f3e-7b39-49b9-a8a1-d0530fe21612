package com.xyy.saas.elasticsearch.core.api.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.elasticsearc.core.api.ESShopIncomeStatementApi;
import com.xyy.saas.elasticsearc.core.dto.ESInventoryShopIncomeDTO;
import com.xyy.saas.elasticsearch.core.config.ElasticSearchConfiguration;
import com.xyy.saas.elasticsearch.core.enums.IndexEnum;
import net.sf.jsqlparser.expression.StringValue;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequestBuilder;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static ch.lambdaj.Lambda.collect;

/**
 * 总部查询门店机构报损报溢列表服务
 */
@Service(version = "0.0.1")
public class ESShopIncomeStatementApiImpl implements ESShopIncomeStatementApi {
    private static final Logger logger = LoggerFactory.getLogger(ESShopIncomeStatementApiImpl.class);
    //最大查询条数
    private final static int MAX_SIZE = 10000;

    @Override
    public Map<String, Object> getShopListIncomeStatement(ESInventoryShopIncomeDTO shopIncomeDTO) {
        logger.info("getShopListIncomeStatement | params={}", JSONObject.toJSONString(shopIncomeDTO));
        SearchRequestBuilder searchRequestBuilder = ElasticSearchConfiguration.client.prepareSearch(IndexEnum.INCOME_STATEMENT.getIndex()).setTypes(IndexEnum.INCOME_STATEMENT.getType());
        //初始化查询构造
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        //查询机构号
        boolQuery.must(QueryBuilders.termQuery("organSign", shopIncomeDTO.getShopOrganSign()));
        //查询删除状态：1未删除
        boolQuery.must(QueryBuilders.termQuery("yn", 1));

        //查询起始时间和结束时间
        if (StringUtils.isNotEmpty(shopIncomeDTO.getTicketStartDate()) && StringUtils.isNotEmpty(shopIncomeDTO.getTicketEndDate())) {
            boolQuery.must(QueryBuilders.rangeQuery("createTime")
                    .from(shopIncomeDTO.getTicketStartDate() +" 00:00:00")
                    .to(shopIncomeDTO.getTicketEndDate() +" 23:59:59")
                    .includeLower(true)
                    .includeUpper(true));
        } else if (StringUtils.isNotEmpty(shopIncomeDTO.getTicketStartDate()) && StringUtils.isEmpty(shopIncomeDTO.getTicketEndDate())) {
            boolQuery.must(QueryBuilders.rangeQuery("createTime").gte(shopIncomeDTO.getTicketStartDate() +" 00:00:00"));
        } else if (StringUtils.isNotEmpty(shopIncomeDTO.getTicketEndDate()) && StringUtils.isEmpty(shopIncomeDTO.getTicketStartDate())) {
            boolQuery.must(QueryBuilders.rangeQuery("createTime").lte(shopIncomeDTO.getTicketEndDate() +" 23:59:59"));
        }

        //查询编号
        if (StringUtils.isNoneBlank(shopIncomeDTO.getTicketPref())) {
            boolQuery.must(QueryBuilders.wildcardQuery("pref", "*" + shopIncomeDTO.getTicketPref() + "*"));
        }

        //状态
        if (shopIncomeDTO.getIncomeType() != null && shopIncomeDTO.getIncomeType() == 1) {
            boolQuery.must(QueryBuilders.rangeQuery("lossProductCounts").gt(0));
        }

        if (shopIncomeDTO.getIncomeType() != null && shopIncomeDTO.getIncomeType() == 2) {
            boolQuery.must(QueryBuilders.rangeQuery("yiProductCounts").gt(0));
        }

        searchRequestBuilder.setQuery(boolQuery);

        if (StringUtils.isNoneBlank(shopIncomeDTO.getSidx(), shopIncomeDTO.getSord())) {
            SortOrder sortOrder = shopIncomeDTO.getSord().equalsIgnoreCase("desc") ? SortOrder.DESC : SortOrder.ASC;
            searchRequestBuilder.addSort(shopIncomeDTO.getSidx(), sortOrder);
        } else {
            searchRequestBuilder.addSort("createTime", SortOrder.DESC);
        }
        //处理分页参数
        getPage(searchRequestBuilder, shopIncomeDTO);

        SearchResponse searchResponse = searchRequestBuilder.execute().actionGet();
        SearchHits hits = searchResponse.getHits();
        List<Map<String, Object>> result = new ArrayList<>();
        SearchHit[] searchHists = hits.getHits();
        for (SearchHit sh : searchHists) {
            result.add(sh.getSource());
        }
        Map<String, Object> map = new HashMap<>(2);
        map.put("list", result);
        map.put("total", hits.getTotalHits());
        logger.info("getShopListIncomeStatement | es请求参数={}", searchRequestBuilder.toString());
        return map;
    }

    @Override
    public Map<String, Object> getShopListIncomeStatementDetail(ESInventoryShopIncomeDTO shopIncomeDTO) {
        logger.info("getShopListIncomeStatementDetail | params={}", JSONObject.toJSONString(shopIncomeDTO));
        SearchRequestBuilder searchRequestBuilder = ElasticSearchConfiguration.client.prepareSearch(IndexEnum.INCOME_STATEMENT_DETAIL.getIndex()).setTypes(IndexEnum.INCOME_STATEMENT_DETAIL.getType());
        BoolQueryBuilder boolQuery = buildIncomeStatementDetailQuery(shopIncomeDTO);
        searchRequestBuilder.setQuery(boolQuery);

        if (StringUtils.isNoneBlank(shopIncomeDTO.getSidx(), shopIncomeDTO.getSord())) {
            SortOrder sortOrder = shopIncomeDTO.getSord().equalsIgnoreCase("desc") ? SortOrder.DESC : SortOrder.ASC;
            searchRequestBuilder.addSort(shopIncomeDTO.getSidx(), sortOrder);
        } else {
            searchRequestBuilder.addSort("createTime", SortOrder.DESC);
        }

        //处理分页参数
        getPage(searchRequestBuilder, shopIncomeDTO);

        SearchResponse searchResponse = searchRequestBuilder.execute().actionGet();
        SearchHits hits = searchResponse.getHits();
        List<Map<String, Object>> result = new ArrayList<>();
        SearchHit[] searchHists = hits.getHits();
        for (SearchHit sh : searchHists) {
            result.add(sh.getSource());
        }
        Map<String, Object> map = new HashMap<>();
        map.put("list", result);
        map.put("total", hits.getTotalHits());
        logger.info("getShopListIncomeStatementDetail | es请求参数={}", searchRequestBuilder.toString());
        return map;
    }
    @Override
    public Map<String, Object> getIncomeStatementDetail(ESInventoryShopIncomeDTO shopIncomeDTO) {
        logger.info("getShopListIncomeStatement | params={}", JSONObject.toJSONString(shopIncomeDTO));
        SearchRequestBuilder searchRequestBuilder = ElasticSearchConfiguration.client.prepareSearch(IndexEnum.INCOME_STATEMENT_DETAIL.getIndex()).setTypes(IndexEnum.INCOME_STATEMENT_DETAIL.getType());
        //初始化查询构造
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        //查询机构号

        if(!StringUtils.isEmpty(shopIncomeDTO.getShopOrganSign())){
            boolQuery.must(QueryBuilders.termQuery("organSign", shopIncomeDTO.getShopOrganSign()));
        }

        if(CollectionUtils.isNotEmpty(shopIncomeDTO.getOrganSignList())){
            boolQuery.filter(QueryBuilders.termsQuery("organSign", shopIncomeDTO.getOrganSignList()));
        }

        //查询删除状态：1未删除
        boolQuery.must(QueryBuilders.termQuery("yn", 1));

        //查询起始时间和结束时间
        if (StringUtils.isNotEmpty(shopIncomeDTO.getTicketStartDate()) && StringUtils.isNotEmpty(shopIncomeDTO.getTicketEndDate())) {
            boolQuery.must(QueryBuilders.rangeQuery("createTime")
                    .from(shopIncomeDTO.getTicketStartDate() +" 00:00:00")
                    .to(shopIncomeDTO.getTicketEndDate() +" 23:59:59")
                    .includeLower(true)
                    .includeUpper(true));
        } else if (StringUtils.isNotEmpty(shopIncomeDTO.getTicketStartDate()) && StringUtils.isEmpty(shopIncomeDTO.getTicketEndDate())) {
            boolQuery.must(QueryBuilders.rangeQuery("createTime").gte(shopIncomeDTO.getTicketStartDate() +" 00:00:00"));
        } else if (StringUtils.isNotEmpty(shopIncomeDTO.getTicketEndDate()) && StringUtils.isEmpty(shopIncomeDTO.getTicketStartDate())) {
            boolQuery.must(QueryBuilders.rangeQuery("createTime").lte(shopIncomeDTO.getTicketEndDate() +" 23:59:59"));
        }
        //商品模糊查询
        if(StringUtils.isNoneBlank(shopIncomeDTO.getProductMax())) {
            boolQuery.must(QueryBuilders.wildcardQuery("productMax", "*"+shopIncomeDTO.getProductMax().toUpperCase()+"*"));
        }
        //生产厂家模糊查询
        if(StringUtils.isNoneBlank(shopIncomeDTO.getManufacturer())) {
            boolQuery.must(QueryBuilders.wildcardQuery("manufacturer", "*"+shopIncomeDTO.getManufacturer()+"*"));
        }
        //状态
        if(shopIncomeDTO.getIncomeType() != null && shopIncomeDTO.getIncomeType() == 1) {
            boolQuery.must(QueryBuilders.rangeQuery("lostNumbers").gt(0));
        }

        if(shopIncomeDTO.getIncomeType() != null && shopIncomeDTO.getIncomeType() == 2) {
            boolQuery.must(QueryBuilders.rangeQuery("overFlowNumbers").lt(0));
        }
        List<String> profitLossPrefList = getProfitLossPref(shopIncomeDTO);
        boolQuery.mustNot(QueryBuilders.termsQuery("incomeId", profitLossPrefList));

        searchRequestBuilder.setQuery(boolQuery);
        if (StringUtils.isNoneBlank(shopIncomeDTO.getSidx(), shopIncomeDTO.getSord())) {
            SortOrder sortOrder = shopIncomeDTO.getSord().equalsIgnoreCase("desc") ? SortOrder.DESC : SortOrder.ASC;
            searchRequestBuilder.addSort(shopIncomeDTO.getSidx(), sortOrder);
        }
        searchRequestBuilder.addSort("organSign", SortOrder.DESC).addSort("createTime", SortOrder.DESC);

        //处理分页参数
        getPage(searchRequestBuilder, shopIncomeDTO);
        
        SearchResponse searchResponse = searchRequestBuilder.execute().actionGet();
        SearchHits hits = searchResponse.getHits();
        List<Map<String, Object>> result = new ArrayList<>();
        SearchHit[] searchHists = hits.getHits();
        for (SearchHit sh : searchHists) {
            result.add(sh.getSource());
        }
        Map<String, Object> map = new HashMap<>(2);
        map.put("list", result);
        map.put("total", hits.getTotalHits());
        logger.info("getShopListIncomeStatement | es请求参数={}", searchRequestBuilder.toString());
        return map;
    }


    private List<String> getProfitLossPref(ESInventoryShopIncomeDTO shopIncomeDTO){

        logger.info("getProfitLossPref | 请求参数={}", JSONObject.toJSONString(shopIncomeDTO));
        List<String> result = new ArrayList<>();
        SearchRequestBuilder searchRequestBuilder = ElasticSearchConfiguration.client.prepareSearch(IndexEnum.INCOME_STATEMENT.getIndex()).setTypes(IndexEnum.INCOME_STATEMENT.getType());
        //初始化查询构造
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if(!StringUtils.isEmpty(shopIncomeDTO.getShopOrganSign())){
            boolQuery.must(QueryBuilders.termQuery("organSign", shopIncomeDTO.getShopOrganSign()));
        }

        if(CollectionUtils.isNotEmpty(shopIncomeDTO.getOrganSignList())){
            boolQuery.filter(QueryBuilders.termsQuery("organSign", shopIncomeDTO.getOrganSignList()));
        }
        boolQuery.must(QueryBuilders.termQuery("status", 1));

        searchRequestBuilder.setQuery(boolQuery);

        SearchResponse searchResponse = searchRequestBuilder.execute().actionGet();
        SearchHits hits = searchResponse.getHits();
        List<Map<String, Object>> list = new ArrayList<>();
        SearchHit[] searchHists = hits.getHits();
        for (SearchHit sh : searchHists) {
            list.add(sh.getSource());
        }

        for(Map<String,Object> map :list){
            result.add(String.valueOf(map.get("id")));
        }
        logger.info("getProfitLossPref | es请求参数={}", searchRequestBuilder.toString());
        logger.info("录入单据{}", JSONObject.toJSONString(result));
        return result;
    }

    /**
     * 構建boolquery對象
     *
     * @param incomeDTO
     */
    private BoolQueryBuilder buildIncomeStatementDetailQuery(ESInventoryShopIncomeDTO incomeDTO) {
        //初始化查询构造
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        //查询机构号
        boolQuery.must(QueryBuilders.termQuery("organSign", incomeDTO.getShopOrganSign()));
        //查询删除状态：1未删除
        boolQuery.must(QueryBuilders.termQuery("yn", 1));
        //查询编号
        if (StringUtils.isNoneBlank(incomeDTO.getTicketPref())) {
            boolQuery.must(QueryBuilders.wildcardQuery("profitLossPref", "*" + incomeDTO.getTicketPref() + "*"));
        }
        //状态
        if (incomeDTO.getIncomeType() != null && incomeDTO.getIncomeType() == 1) {
            boolQuery.must(QueryBuilders.rangeQuery("lostNumbers").gt(0));
        }

        if (incomeDTO.getIncomeType() != null && incomeDTO.getIncomeType() == 2) {
            boolQuery.must(QueryBuilders.rangeQuery("overFlowNumbers").lt(0));
        }
        return boolQuery;
    }

    private void getPage(SearchRequestBuilder searchRequestBuilder, ESInventoryShopIncomeDTO inventoryShopIncomeDTO) {
        if (inventoryShopIncomeDTO.getPageNum() != null && inventoryShopIncomeDTO.getPageSize() != null) {
            int startRow = inventoryShopIncomeDTO.getPageNum() < 0 ? 0 : (inventoryShopIncomeDTO.getPageNum() - 1) * inventoryShopIncomeDTO.getPageSize();
            int pageSize = inventoryShopIncomeDTO.getPageSize();
            pageSize = pageSize < 0 ? 0 : pageSize > MAX_SIZE ? MAX_SIZE : pageSize;
            searchRequestBuilder.setFrom(startRow);
            searchRequestBuilder.setSize(pageSize);
        } else {
            searchRequestBuilder.setSize(MAX_SIZE);
        }
    }


    @Override
    public Map<String, Object> getIncomeStatementDetailSum(ESInventoryShopIncomeDTO shopIncomeDTO) {
        logger.info("getShopListIncomeStatement | params={}", JSONObject.toJSONString(shopIncomeDTO));
        SearchRequestBuilder searchRequestBuilder = ElasticSearchConfiguration.client.prepareSearch(IndexEnum.INCOME_STATEMENT_DETAIL.getIndex()).setTypes(IndexEnum.INCOME_STATEMENT_DETAIL.getType());
        //初始化查询构造
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        //查询机构号

        if(!StringUtils.isEmpty(shopIncomeDTO.getShopOrganSign())){
            boolQuery.must(QueryBuilders.termQuery("organSign", shopIncomeDTO.getShopOrganSign()));
        }

        if(CollectionUtils.isNotEmpty(shopIncomeDTO.getOrganSignList())){
            boolQuery.filter(QueryBuilders.termsQuery("organSign", shopIncomeDTO.getOrganSignList()));
        }

        //查询删除状态：1未删除
        boolQuery.must(QueryBuilders.termQuery("yn", 1));

        //查询起始时间和结束时间
        if (StringUtils.isNotEmpty(shopIncomeDTO.getTicketStartDate()) && StringUtils.isNotEmpty(shopIncomeDTO.getTicketEndDate())) {
            boolQuery.must(QueryBuilders.rangeQuery("createTime")
                    .from(shopIncomeDTO.getTicketStartDate() +" 00:00:00")
                    .to(shopIncomeDTO.getTicketEndDate() +" 23:59:59")
                    .includeLower(true)
                    .includeUpper(true));
        } else if (StringUtils.isNotEmpty(shopIncomeDTO.getTicketStartDate()) && StringUtils.isEmpty(shopIncomeDTO.getTicketEndDate())) {
            boolQuery.must(QueryBuilders.rangeQuery("createTime").gte(shopIncomeDTO.getTicketStartDate() +" 00:00:00"));
        } else if (StringUtils.isNotEmpty(shopIncomeDTO.getTicketEndDate()) && StringUtils.isEmpty(shopIncomeDTO.getTicketStartDate())) {
            boolQuery.must(QueryBuilders.rangeQuery("createTime").lte(shopIncomeDTO.getTicketEndDate() +" 23:59:59"));
        }
        //商品模糊查询
        if(StringUtils.isNoneBlank(shopIncomeDTO.getProductMax())) {
            boolQuery.must(QueryBuilders.wildcardQuery("productMax", "*"+shopIncomeDTO.getProductMax().toUpperCase()+"*"));
        }
        //生产厂家模糊查询
        if(StringUtils.isNoneBlank(shopIncomeDTO.getManufacturer())) {
            boolQuery.must(QueryBuilders.wildcardQuery("manufacturer", "*"+shopIncomeDTO.getManufacturer()+"*"));
        }
        //状态
        if(shopIncomeDTO.getIncomeType() != null && shopIncomeDTO.getIncomeType() == 1) {
            boolQuery.must(QueryBuilders.rangeQuery("lostNumbers").gt(0));
        }

        if(shopIncomeDTO.getIncomeType() != null && shopIncomeDTO.getIncomeType() == 2) {
            boolQuery.must(QueryBuilders.rangeQuery("overFlowNumbers").lt(0));
        }
        List<String> profitLossPrefList = getProfitLossPref(shopIncomeDTO);
        boolQuery.mustNot(QueryBuilders.termsQuery("incomeId", profitLossPrefList));

        searchRequestBuilder.setQuery(boolQuery);
        if (StringUtils.isNoneBlank(shopIncomeDTO.getSidx(), shopIncomeDTO.getSord())) {
            SortOrder sortOrder = shopIncomeDTO.getSord().equalsIgnoreCase("desc") ? SortOrder.DESC : SortOrder.ASC;
            searchRequestBuilder.addSort(shopIncomeDTO.getSidx(), sortOrder);
        }
        searchRequestBuilder.addSort("organSign", SortOrder.DESC).addSort("createTime", SortOrder.DESC);
        searchRequestBuilder.setSize(MAX_SIZE);
        SearchResponse searchResponse = searchRequestBuilder.execute().actionGet();
        SearchHits hits = searchResponse.getHits();
        List<Map<String, Object>> result = new ArrayList<>();
        SearchHit[] searchHists = hits.getHits();
        for (SearchHit sh : searchHists) {
            result.add(sh.getSource());
        }
        Map<String, Object> map = new HashMap<>(2);
        map.put("list", result);
        map.put("total", hits.getTotalHits());
        logger.info("getShopListIncomeStatement | es请求参数={}", searchRequestBuilder.toString());
        return map;
    }
}
