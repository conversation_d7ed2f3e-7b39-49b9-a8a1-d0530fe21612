package com.xyy.saas.elasticsearch.core.mq;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.elasticsearc.core.api.ESNewOrderIndexReportApi;
import com.xyy.saas.elasticsearc.core.api.ESOrderInfoApi;
import com.xyy.saas.elasticsearch.core.service.ElasticFiveDataService;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQPushConsumerLifecycleListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/*
 * Description: 更新es5中订单状态、以及成本价字段
 * @auther: xucao
 * @date: 2023/05/10 下午17:57
 */
@Service
@RocketMQMessageListener(topic = "${demo.rocketmq.syncOrderStatusTopicNew}", consumerGroup = "${rocketmq.consumer.esFiveSyncOrderStatusGroupNew}")
public class OrderSyncStatusToEsFiveMqConsumerNew implements RocketMQListener<Message>, RocketMQPushConsumerLifecycleListener {
    private static final Logger logger = LoggerFactory.getLogger(OrderSyncStatusToEsFiveMqConsumerNew.class);

    @Autowired
    private ElasticFiveDataService elasticFiveDataService;



    //重试次数定为5次，总时间超过5分钟，如果还未成功则表示定时任务重试也失败了
    private static final int RECONSUME_TIMES = 6;

    @Value("${es5.syncStatusSwitch:true}")
    private boolean syncStatusSwitch;

    @Override
    public void onMessage(Message message) {
        logger.info("onMessage  {}", JSONObject.toJSONString(message));
    }

    @Override
    public void prepareStart(DefaultMQPushConsumer defaultMQPushConsumer) {
        defaultMQPushConsumer.registerMessageListener(new MessageListenerConcurrently() {
            @Override
            public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
                for (MessageExt messageExt : msgs) {
                    String msgText = new String(messageExt.getBody());
                    logger.info("【更新Es订单信息到ES5】消息体:{},重试次数{}", msgText, messageExt.getReconsumeTimes());
                    if (StringUtils.isBlank(msgText) || messageExt.getBody() == null) {
                        logger.error("【更新Es订单信息到ES5】消息体为空{}", msgText);
                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                    }
                    if(messageExt.getReconsumeTimes() >= RECONSUME_TIMES){
                        logger.error("【更新Es订单信息到ES5】重试次数超限，消息体为{}", msgText);
                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                    }

                    if(!syncStatusSwitch){
                        logger.error("【更新Es订单信息到ES5】开关关闭，默认ACK，消息体为{}", msgText);
                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                    }

                    Map<String, Object> updateMap = JSONObject.parseObject(msgText,Map.class);
                    try {
                        Object orderUpdateObj = updateMap.get("order");
                        //获取总部机构号
                        Object headquartersOrganSignObj = updateMap.get("headquartersOrganSign");
                        //获取门店机构号
                        Object organSign = updateMap.get("organSign");
                        if(Objects.nonNull(orderUpdateObj)){
                            Map<String, Object> orderUpdateMap = (Map<String, Object>) orderUpdateObj;
                            //同步更新ES5订单信息
                            elasticFiveDataService.updateOrderInfo(orderUpdateMap,organSign,headquartersOrganSignObj);
                        }
                        Object orderDetailUpdateObj = updateMap.get("orderDetail");
                        if(Objects.nonNull(orderDetailUpdateObj)){
                            Map<String,Map<String,Object>> orderDetailUpdateMap = (Map<String,Map<String,Object>>) orderDetailUpdateObj;
                            //同步更新ES5订单明细信息
                            elasticFiveDataService.updateOrderDetail(orderDetailUpdateMap,organSign,headquartersOrganSignObj);
                        }
                    }catch (Exception e){
                        logger.error("prepareStart error ",e);
                        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                    }
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
        });
    }
}
