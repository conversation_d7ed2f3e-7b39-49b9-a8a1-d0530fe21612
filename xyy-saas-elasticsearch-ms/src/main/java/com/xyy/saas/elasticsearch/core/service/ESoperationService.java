package com.xyy.saas.elasticsearch.core.service;

import com.xyy.saas.elasticsearc.core.dto.SalesDailyNewDto;

import java.util.List;
import java.util.Map;

/**
 * @Auther: chen1
 * @Date: 2020/3/11 16:50
 * @Description: 用户登录日志es操作类
 */
public interface ESoperationService {

    /**
     * 根据机构查询最大id
     * @param index
     * @param type
     * @return
     */
    Long findMaxId(String index,String type);

    /**
     * 批量导入es
     * @param list
     * @param index
     * @param type
     * @return
     */
    boolean batchInsert(List<Map<String, Object>> list, String index, String type);

}
