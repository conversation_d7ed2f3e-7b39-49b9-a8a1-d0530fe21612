package com.xyy.saas.elasticsearch.core.mq;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.elasticsearc.core.api.ESNewOrderIndexReportApi;
import com.xyy.saas.elasticsearc.core.api.ESOrderInfoApi;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQPushConsumerLifecycleListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.messaging.Message;

import java.util.List;
import java.util.Map;

/*
 * Description: 更新es中订单状态，废弃，由于上线时可能存在消息堆积以及历史重试，故保留，上线观察流量后可删除
 */
@Service
@RocketMQMessageListener(topic = "${demo.rocketmq.syncOrderStatusTopic}", consumerGroup = "${rocketmq.consumer.esSyncOrderStatusGroup}")
public class OrderSyncStatusMqConsumer implements RocketMQListener<Message>, RocketMQPushConsumerLifecycleListener {
    private static final Logger logger = LoggerFactory.getLogger(OrderSyncStatusMqConsumer.class);
    @Autowired
    private ESOrderInfoApi eSOrderInfoApi;
    @Autowired
    private ESNewOrderIndexReportApi esNewOrderIndexReportApi;

    private static final int RECONSUME_TIMES = 3;

    @Override
    public void onMessage(Message message) {
        logger.info("onMessage  {}", JSONObject.toJSONString(message));
    }

    @Override
    public void prepareStart(DefaultMQPushConsumer defaultMQPushConsumer) {
        defaultMQPushConsumer.registerMessageListener(new MessageListenerConcurrently() {
            @Override
            public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
                for (MessageExt messageExt : msgs) {
                    String msgText = new String(messageExt.getBody());
                    logger.info("接受到的消息:{},重试次数{}", msgText, messageExt.getReconsumeTimes());
                    if (StringUtils.isBlank(msgText) || messageExt.getBody() == null) {
                        logger.error("消息体为空{}", msgText);
                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                    }
                    if(messageExt.getReconsumeTimes() >= RECONSUME_TIMES){
                        logger.error("重试次数超限，消息体为{}", msgText);
                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                    }
                    Map<String, Object> orderStatus = JSONObject.parseObject(msgText,Map.class);
                    try {
                        //更新es订单状态
                        eSOrderInfoApi.updateOrderInfo(orderStatus);
                        //跟新es总部订单状态
                        esNewOrderIndexReportApi.updateOrderInfoByHeadquarters(orderStatus);
                        //跟新es单体订单状态
                        //esNewOrderIndexReportApi.updateOrderInfoByMonomer(orderStatus);
                    }catch (Exception e){
                        logger.error("prepareStart error ",e);
                        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                    }
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
        });
    }
}
