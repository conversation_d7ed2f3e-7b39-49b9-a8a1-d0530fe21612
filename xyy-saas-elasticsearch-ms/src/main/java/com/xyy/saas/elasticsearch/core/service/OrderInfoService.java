package com.xyy.saas.elasticsearch.core.service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: JiangxiMedicalService
 * @date 2019/9/22  19:40
 * @description: TODO
 */
public interface OrderInfoService {

    /**
     * 保存订单数据单条
     */
    void saveOrderInfoToES(Map<String,Object> map);

    /**
     * 保存订单数据单条
     */
    void saveOrderDetailToES(Map<String,Object> map);

    /**
     * 保存订单批次信息
     */
    void saveOrderDetailBatchToES(Map<String,Object> map);

}
