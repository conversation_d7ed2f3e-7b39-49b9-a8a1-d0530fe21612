package com.xyy.saas.elasticsearch.core.api.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.xyy.saas.elasticsearc.core.api.JiangxiMedicalApi;
import com.xyy.saas.elasticsearch.core.service.JiangxiMedicalService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title: JiangxiMedicalApiImpl
 * @date 2019/9/22  19:59
 * @description: TODO
 */
@Service(version="0.0.1")
public class JiangxiMedicalApiImpl implements JiangxiMedicalApi {

    @Autowired
    JiangxiMedicalService jiangxiMedicalService;

    @Override
    public List<String> findMedicalSerialNumList() {
        return jiangxiMedicalService.findMedicalSerialNumList();
    }

    @Override
    public Map<String,Object> findByMedicalSerialNum(String medicalSerialNum) {
        return jiangxiMedicalService.findByMedicalSerialNum(medicalSerialNum);
    }
}
