package com.xyy.saas.elasticsearch.core.service;

import com.xyy.saas.elasticsearch.core.enums.BussinessMoveEnum;
import org.elasticsearch.action.search.SearchRequestBuilder;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * ES5 集群，数据服务接口，包括初始化索引，数据  增删改 等操作
 * @date 2023/04/25 11:28
 */
public interface ElasticFiveDataService {


    /**
     * 初始化ES5订单主表索引
     * @param json
     * @return
     * @throws Exception
     */
    Boolean createOrderInfoShardingIndex(String json) throws Exception;

    /**
     * 初始化ES5订单明细索引
     * @param json
     * @return
     * @throws Exception
     */
    Boolean createOrderDetailShardingIndex(String json) throws Exception;


    /**
     * 初始化ES5订单明细批次索引
     * @param json
     * @return
     * @throws Exception
     */
    Boolean createOrderDetailBatchShardingIndex(String json) throws Exception;

    /**
     * 保存订单主表es索引
     * @param orderMap
     */
    void saveOrderInfoIndex(Map<String,Object> orderMap, boolean isSetEsRefreshPolicy);


    /**
     * 保存订单明细es索引
     * @param orderMap
     */
    void saveOrderDetailIndex(Map<String, Object> orderMap, boolean isSetEsRefreshPolicy);

    /**
     * 保存订单明细批次es索引
     * @param orderMap
     */
    void saveOrderDetailBatchIndex(Map<String, Object> orderMap, boolean isSetEsRefreshPolicy);

    /**
     * 批量保存订单数据到es集群
     * @param orders
     * @return
     */
    Boolean batchSaveOrderInfo(String orders);


    /**
     * 更新订单主表索引
     * @param orderMap
     */
    void updateOrderInfo(Map<String,Object> orderMap,Object organSign,Object headOrganSign);

    /**
     * 更新订单明细索引
     * @param orderDetailUpdateMap
     */
    void updateOrderDetail(Map<String, Map<String, Object>> orderDetailUpdateMap,Object organSign,Object headOrganSign);


    /**
     * es5迁移灰度开关
     * @param organSign 机构号
     * @param organSigns  机构集合
     * @param moveEnum  当前迁移业务场景
     * @return  true - 走 es5   false - 走es3
     */
    Boolean es5OrganSignGraySwitch(String organSign, List<String> organSigns, BussinessMoveEnum moveEnum);

    /**
     * 根据机构号、总部机构、查询类型，获取灰度策略，并根据灰度策略返回es查询对象
     * @param organSign
     * @param headOrganSign
     * @param organSigns
     * @param moveEnum
     * @return
     */
    SearchRequestBuilder getSearchBuild(String organSign, String headOrganSign , List<String> organSigns, BussinessMoveEnum moveEnum);

    /**
     * 根据机构号、总部机构、查询类型，获取灰度策略，并根据灰度策略返回es-info索引的查询对象
     * @param organSign
     * @param headOrganSign
     * @param organSigns
     * @param moveEnum
     * @return
     */
    SearchRequestBuilder getInfoSearchBuild(String organSign, String headOrganSign , List<String> organSigns, BussinessMoveEnum moveEnum);


    /**
     * 根据机构号、总部机构、查询类型，获取灰度策略，并根据灰度策略返回es-info索引的查询对象
     * @param organSign
     * @param headOrganSign
     * @param organSigns
     * @param moveEnum
     * @return
     */
    SearchRequestBuilder getDetailBatchSearchBuild(String organSign, String headOrganSign , List<String> organSigns, BussinessMoveEnum moveEnum);

    /**
     * 批量填充追溯码
     * @param json
     * @return
     */
    Boolean batchFillTraceCode(String json);
}
