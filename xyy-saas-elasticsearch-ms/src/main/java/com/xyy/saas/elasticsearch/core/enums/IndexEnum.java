package com.xyy.saas.elasticsearch.core.enums;

public enum IndexEnum {

	ORDER("saas_order_info_all", "saas_order_info_index", "订单"),
	ORDER_DETAIL("saas_order_detail_all", "saas_order_detail_index", "订单明细"),
	HANDOVER_RECORD("saas_handover_record_all", "saas_handover_record_index", "交班"),
	MAINTAIN("saas_maintain_index", "saas_maintain_index", "药品养护"),
	DISPLAY("saas_display_index", "saas_display_index", "陈列检查"),
	SALES_SALESDAILY("saas_salesdaily", "saas_salesdaily", "销售日报"),
	BUSINESS_STAFF_OPINION("saas_order_business_staff_opinion", "saas_order_business_staff_opinion", "生意参谋意见"),
	ADVERTISING_OPINION("saas_advertising_opinion", "saas_advertising_opinion", "广告意见"),
	INCOME_STATEMENT("saas_income_statement","saas_income_statement","报损报溢"),
	INCOME_STATEMENT_DETAIL("saas_income_statement_detail","saas_income_statement_detail","报损报溢详情"),
    PRODUCT_STANDARD_INFO("saas_product_standard_info","saas_product_standard_info","产品标准信息"),
    PRODUCT_BASEINFO("saas_product_baseinfo","saas_product_baseinfo","saas商品信息"),
    HYT_SAAS_MERCHANT_PRODUCT("hyt_saas_merchant_product","hyt_saas_merchant_product","荷叶通商品信息"),
	ORDER_DETAIL_BATCH("saas_order_detail_batch_index", "saas_order_detail_batch_type", "订单批次信息");

	private String index;
	private String type;
	private String description;

	IndexEnum(String index, String type, String description) {
		this.index = index;
		this.type = type;
		this.description = description;
	}

	public String getIndex() {
		return index;
	}

	public String getType() {
		return type;
	}

	public String getDescription() {
		return description;
	}
}
