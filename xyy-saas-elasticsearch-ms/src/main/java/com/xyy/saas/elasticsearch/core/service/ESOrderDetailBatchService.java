package com.xyy.saas.elasticsearch.core.service;

import com.xyy.saas.elasticsearc.core.common.ESQueryBuilderConstructor;
import com.xyy.saas.elasticsearc.core.common.Page;



/**
 * @Auther: zheng.peng
 * @Date: 2019/8/6 10:12
 * @Description:
 */
public interface ESOrderDetailBatchService {

    /**
     * 分页带返回指定实体功能
     * @param constructor 查询构造
     * @param clazz 实体类型类
     */
    <T> Page searchOrderDetailBatchPage(ESQueryBuilderConstructor constructor, Class<T> clazz, String index, String type);

 }
