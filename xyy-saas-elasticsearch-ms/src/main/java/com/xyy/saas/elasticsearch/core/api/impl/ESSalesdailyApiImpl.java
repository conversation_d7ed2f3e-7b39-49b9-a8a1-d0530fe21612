package com.xyy.saas.elasticsearch.core.api.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.elasticsearc.core.api.ESSalesdailyApi;
import com.xyy.saas.elasticsearc.core.common.ESQueryBuilderConstructor;
import com.xyy.saas.elasticsearc.core.common.ESQueryBuilders;
import com.xyy.saas.elasticsearc.core.common.ESSearchUtil;
import com.xyy.saas.elasticsearc.core.common.Page;
import com.xyy.saas.elasticsearc.core.dto.*;
import com.xyy.saas.elasticsearch.core.config.ElasticSearchConfiguration;
import com.xyy.saas.elasticsearch.core.service.ESSalesdailyService;
import com.xyy.saas.elasticsearch.core.service.ElasticsearchReadService;
import com.xyy.saas.elasticsearch.core.service.ElasticsearchWriteService;
import org.elasticsearch.action.search.SearchRequestBuilder;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.filter.FilterAggregationBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @annotation:
 * <AUTHOR>
 * @create 2019-08-05 13:39
 **/
@Service(version="0.0.1")
public class ESSalesdailyApiImpl implements ESSalesdailyApi {
    /**
     * 最大查询条数
     */
    private final static int MAX_SIZE = 10000;
    private static final Logger logger = LoggerFactory.getLogger(ESSalesdailyApiImpl.class);
    @Autowired
    private ElasticsearchReadService elasticsearchReadService;
    @Autowired
    private ElasticsearchWriteService elasticsearchWriteService;
    @Autowired
    private ESSalesdailyService esSalesdailyService;


    @Override
    public <T> Page logQueryByParma(AnalysisQueryDto dto, String index, String type) {
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        AggregationBuilder aggregationBuilder = AggregationBuilders.terms("organSign").field("organSign").size(10000);
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        ESQueryBuilders esQueryBuildersTotal = new ESQueryBuilders();
        ESQueryBuilderConstructor constructorTotal = new ESQueryBuilderConstructor();
        aggregationBuilder.subAggregation(esQueryBuilders.max("lastLoginTime","lastLoginTime"));
        aggregationBuilder.subAggregation(esQueryBuilders.sum("loginTimesTotal","loginTimes"));
        aggregationBuilder.subAggregation(esQueryBuilders.sum("visitorNumbers","visitorNumbers"));
        aggregationBuilder.subAggregation(esQueryBuilders.sum("dayUsed","oneUsed"));
        aggregationBuilder.subAggregation(esQueryBuilders.sum("purchasingTimes","purchasingTimes"));
        if(!StringUtils.isEmpty(dto.getStartTime())&&!StringUtils.isEmpty(dto.getEndTime())){
            esQueryBuilders.listBuilders().add(QueryBuilders.rangeQuery("createTime").gte(dto.getStartTime()).lte(dto.getEndTime()));
        }
        if(!StringUtils.isEmpty(dto.getStartTime())&&StringUtils.isEmpty(dto.getEndTime())){
            esQueryBuilders.rangeStart("createTime",dto.getStartTime());
        }
        if(StringUtils.isEmpty(dto.getStartTime())&&!StringUtils.isEmpty(dto.getEndTime())){
            esQueryBuilders.rangeEnd("createTime",dto.getEndTime());
        }
        if(!StringUtils.isEmpty(dto.getOrganSign())){
            esQueryBuilders.term("organSign",dto.getOrganSign());
        }
        if(!StringUtils.isEmpty(dto.getOrganName())){
            esQueryBuilders.wildcardQuery("organName",dto.getOrganName());
        }
        esQueryBuildersTotal.listBuilders().addAll(esQueryBuilders.listBuilders());
        if(dto.getOrganSigns()!=null&&dto.getOrganSigns().size()>0){
            if(dto.getOrganSigns().indexOf(dto.getOrganSign())>=0){
                dto.setOrganSigns(null);
            }else {
               /* List<String> list = new ArrayList<>();
                if(dto.getOrganSigns().size()>500){
                    list = dto.getOrganSigns().subList(0,500);
                    esQueryBuilders.terms("organSign",list);
                }else {
                    esQueryBuilders.terms("organSign",dto.getOrganSigns());
                }*/
                BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                boolQueryBuilder.filter(QueryBuilders.termsQuery("organSign",dto.getOrganSigns()));
                esQueryBuilders.listBuilders().add(boolQueryBuilder);

            }
        }
        logger.info("药店登录ES查询条件esQueryBuilders"+ JSON.toJSONString(esQueryBuilders));
        constructor.must(esQueryBuilders);
        Page page = new Page();
        if(dto.getPageNum()<1&&dto.getPageSize()<1){
            List<T> ts = esSalesdailyService.searchList(constructor, aggregationBuilder, SalesDailyDto.class, index, type);
            page.setModelDatas(ts);
            return page;
        }
        constructor.setFrom(dto.getPageNum());
        constructor.setSize(dto.getPageSize());
        logger.info("药店登录ES查询条件constructor"+ JSON.toJSONString(constructor));
        page = esSalesdailyService.searchPage(constructor, aggregationBuilder,SalesDailyDto.class, index, type);
      /*  //根据所有机构查询总条数
        Integer totalCount =0;
        totalCount = totalSizeQuery(dto.getOrganSigns(),constructorTotal,esQueryBuildersTotal,"salesCount","id",totalCount,index,type,"organSign.keyword");
        if(totalCount>0){
            page.setTotal(totalCount);
        }*/
        Long total = page.getTotal();
        Integer size = constructor.getSize();
        List back = page.getModelDatas();
        if(back!=null&&back.size()>0){
            page.setModelDatas(back.subList(constructor.getFrom()<0?0:size*(constructor.getFrom()-1),size*constructor.getFrom()>back.size()? back.size():size*constructor.getFrom()));
        }else {
            page.setModelDatas(null);
        }
        page.setCurrentPage(constructor.getFrom());
        int m = total.intValue()/size;
        if(total.intValue()%size==0){
            page.setTotalPages(m);
        }else {
            page.setTotalPages(m+1);
        }
        return page;
    }

    @Override
    public <T> Page userQueryActiveByParma(AnalysisQueryDto dto, String index, String type) {
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        AggregationBuilder aggregationBuilder = AggregationBuilders.terms("organSign").field("organSign").size(10000);
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        ESQueryBuilders esQueryBuildersTotal = new ESQueryBuilders();
        ESQueryBuilderConstructor constructorTotal = new ESQueryBuilderConstructor();
        aggregationBuilder.subAggregation(esQueryBuilders.sum("actualAmount","actualAmount"));
        aggregationBuilder.subAggregation(esQueryBuilders.sum("visitorNumbers","visitorNumbers"));
        aggregationBuilder.subAggregation(esQueryBuilders.sum("costAmount","costAmount"));
        aggregationBuilder.subAggregation(esQueryBuilders.sum("grossProfit","grossProfit"));
        aggregationBuilder.subAggregation(esQueryBuilders.sum("customerPrice","customerPrice"));
        aggregationBuilder.subAggregation(esQueryBuilders.sum("productPrice","productPrice"));
        aggregationBuilder.subAggregation(esQueryBuilders.sum("customerCount","customerCount"));
        aggregationBuilder.subAggregation(esQueryBuilders.sum("salesAmount","actualAmount"));
        aggregationBuilder.subAggregation(esQueryBuilders.sum("userDays","oneUsed"));
        aggregationBuilder.subAggregation(esQueryBuilders.sum("purchasingTimes","purchasingTimes"));
        if(!StringUtils.isEmpty(dto.getStartTime())&&!StringUtils.isEmpty(dto.getEndTime())){
            esQueryBuilders.listBuilders().add(QueryBuilders.rangeQuery("createTime").gte(dto.getStartTime()).lte(dto.getEndTime()));
        }
        if(!StringUtils.isEmpty(dto.getStartTime())&&StringUtils.isEmpty(dto.getEndTime())){
            esQueryBuilders.rangeStart("createTime",dto.getStartTime());
        }
        if(StringUtils.isEmpty(dto.getStartTime())&&!StringUtils.isEmpty(dto.getEndTime())){
            esQueryBuilders.rangeEnd("createTime",dto.getEndTime());
        }
        if(!StringUtils.isEmpty(dto.getOrganSign())){
            esQueryBuilders.term("organSign",dto.getOrganSign());
        }
        if(!StringUtils.isEmpty(dto.getOrganName())){
            esQueryBuilders.wildcardQuery("organName",dto.getOrganName());
        }
        esQueryBuildersTotal.listBuilders().addAll(esQueryBuilders.listBuilders());
        constructor.setDesc("createTime");
        BeanUtils.copyProperties(constructor,constructorTotal);
        if(dto.getOrganSigns()!=null&&dto.getOrganSigns().size()>0){
            if(dto.getOrganSigns().indexOf(dto.getOrganSign())>=0){
                dto.setOrganSigns(null);
            }else {
                /*List<String> list = new ArrayList<>();
                if(dto.getOrganSigns().size()>500){
                    list = dto.getOrganSigns().subList(0,500);
                    esQueryBuilders.terms("organSign",list);
                }else {
                    esQueryBuilders.terms("organSign",dto.getOrganSigns());
                }*/
                BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                boolQueryBuilder.filter(QueryBuilders.termsQuery("organSign",dto.getOrganSigns()));
                esQueryBuilders.listBuilders().add(boolQueryBuilder);

            }
        }
        logger.info("药店活跃ES查询条件esQueryBuilders"+ JSON.toJSONString(esQueryBuilders));
        constructor.must(esQueryBuilders);
        Page page = new Page();
        if(dto.getPageNum()<1&&dto.getPageSize()<1){
            List<T> ts = esSalesdailyService.searchList(constructor, aggregationBuilder, SalesDailyDto.class, index, type);
            page.setModelDatas(ts);
            return page;
        }
        constructor.setFrom(dto.getPageNum());
        constructor.setSize(dto.getPageSize());
        logger.info("药店活跃ES查询条件constructor"+ JSON.toJSONString(constructor));
        page = esSalesdailyService.searchPage(constructor,aggregationBuilder,SalesDailyDto.class, index, type);
       /* //根据所有机构查询总条数
        Integer totalCount =0;
        totalCount = totalSizeQuery(dto.getOrganSigns(),constructorTotal,esQueryBuildersTotal,"salesCount","id",totalCount,index,type,"organSign.keyword");
        if(totalCount>0){
            page.setTotal(totalCount);
        }*/
        Long total = page.getTotal();
        Integer size = constructor.getSize();
        List back = page.getModelDatas();
        if(back!=null&&back.size()>0){
            page.setModelDatas(back.subList(constructor.getFrom()<0?0:size*(constructor.getFrom()-1),size*constructor.getFrom()>back.size()? back.size():size*constructor.getFrom()));
        }else {
            page.setModelDatas(null);
        }
        page.setCurrentPage(constructor.getFrom());
        int m = total.intValue()/size;
        if(total.intValue()%size==0){
            page.setTotalPages(m);
        }else {
            page.setTotalPages(m+1);
        }
        return page;
    }

    @Override
    public <T> Page salesQueryByParma(AnalysisQueryDto dto, String index, String type) {
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        ESQueryBuilderConstructor constructorTotal = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuildersTotal = new ESQueryBuilders();
        if(!StringUtils.isEmpty(dto.getStartTime())&&!StringUtils.isEmpty(dto.getEndTime())){
            esQueryBuilders.listBuilders().add(QueryBuilders.rangeQuery("createTime").gte(dto.getStartTime()).lte(dto.getEndTime()));
        }
        if(!StringUtils.isEmpty(dto.getStartTime())&&StringUtils.isEmpty(dto.getEndTime())){
            esQueryBuilders.rangeStart("createTime",dto.getStartTime());
        }
        if(StringUtils.isEmpty(dto.getStartTime())&&!StringUtils.isEmpty(dto.getEndTime())){
            esQueryBuilders.rangeEnd("createTime",dto.getEndTime());
        }
        if(dto.getOrganSign()!=null){
            esQueryBuilders.term("organSign",dto.getOrganSign());
        }
        if(dto.getOrganName()!=null){
            esQueryBuilders.wildcardQuery("organName",dto.getOrganName());
        }
        esQueryBuildersTotal.listBuilders().addAll(esQueryBuilders.listBuilders());
        if(dto.getOrganSigns()!=null&&dto.getOrganSigns().size()>0){
            if(dto.getOrganSigns().indexOf(dto.getOrganSign())>=0){
                dto.setOrganSigns(null);
            }else {
                BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                boolQueryBuilder.filter(QueryBuilders.termsQuery("organSign",dto.getOrganSigns()));
                esQueryBuilders.listBuilders().add(boolQueryBuilder);

            }
        }
        logger.info("销售日报ES查询条件esQueryBuilders"+ JSON.toJSONString(esQueryBuilders));
        constructor.setDesc("createTime");
        constructor.must(esQueryBuilders);
        Page page = new Page();
        if(dto.getPageNum()<1&&dto.getPageSize()<1){
            constructor.setFrom(0);
            constructor.setSize(10000);
            page = elasticsearchReadService.searchListCloud(constructor,  SalesDailyDto.class, index, type);
            if(page!=null&&page.getModelDatas()!=null){
                List<SalesDailyDto> list = page.getModelDatas();
                for (SalesDailyDto salesDailyDto : list){
                    salesDailyDto.setSalesAmount(salesDailyDto.getActualAmount());
                }
            }
            return page;
        }
        constructor.setFrom(dto.getPageNum());
        constructor.setSize(dto.getPageSize());
        logger.info("销售日报ES查询条件constructor"+ JSON.toJSONString(constructor));
        page = elasticsearchReadService.searchPage(constructor, SalesDailyDto.class, index, type);
        if(page!=null&&page.getModelDatas()!=null){
            List<SalesDailyDto> list = page.getModelDatas();
            for (SalesDailyDto salesDailyDto : list){
                salesDailyDto.setSalesAmount(salesDailyDto.getActualAmount());
            }
        }
        Long total = page.getTotal();
        Integer size = constructor.getSize();
        int m = total.intValue()/size;
        if(total.intValue()%size==0){
            page.setTotalPages(m);
        }else {
            page.setTotalPages(m+1);
        }

        return page;
    }

    @Override
    public boolean deleteQueryByParam(AnalysisQueryDto dto, String index, String type) {
        //增加es查询
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        if(dto.getTimes()!=null&&dto.getTimes().size()>0){
            esQueryBuilders.terms("createTime",dto.getTimes());
        }
        constructor.must(esQueryBuilders);
        boolean b = elasticsearchWriteService.deleteByQuery(constructor, index, type);
        return b;
    }

    @Override
    public <T> Page logNewQueryByParma(OperationLogSearchDto dto, String index, String type) {
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ESQueryBuilderConstructor constructorTotal = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        ESQueryBuilders esQueryBuildersTotal = new ESQueryBuilders();
        if(dto.getBeginDate()!=null&&dto.getEndDate()!=null){
            esQueryBuilders.range("operationTime",sf.format(dto.getBeginDate()),sf.format(dto.getEndDate()));
        }
        if(dto.getBeginDate()!=null&&dto.getEndDate()==null){
            esQueryBuilders.rangeStart("operationTime",sf.format(dto.getBeginDate()));
        }
        if(dto.getBeginDate()==null&&dto.getEndDate()!=null){
            esQueryBuilders.rangeEnd("operationTime",sf.format(dto.getEndDate()));
        }
        if(!StringUtils.isEmpty(dto.getOrganSign())){
            esQueryBuilders.term("organSign.keyword", dto.getOrganSign());
        }
        if(!StringUtils.isEmpty(dto.getDrugstoreName())){
            esQueryBuilders.wildcardQuery("drugstoreName.keyword",dto.getDrugstoreName());
        }
        List<QueryBuilder> queryBuilders = esQueryBuildersTotal.listBuilders();
        queryBuilders.addAll(esQueryBuilders.listBuilders());
        if(dto.getOrganSigns()!=null&&dto.getOrganSigns().size()>0){
            if(dto.getOrganSigns().indexOf(dto.getOrganSign())>=0){
                dto.setOrganSigns(null);
            }else {
                BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                boolQueryBuilder.filter(QueryBuilders.termsQuery("organSign.keyword",dto.getOrganSigns()));
                esQueryBuilders.listBuilders().add(boolQueryBuilder);
               /* List<String> list = new ArrayList<>();
                if(dto.getOrganSigns().size()>500){
                    list = dto.getOrganSigns().subList(0,500);
                    esQueryBuilders.terms("organSign.keyword",list);
                }else {
                    esQueryBuilders.terms("organSign.keyword",dto.getOrganSigns());
                }*/

            }
        }

        logger.info("账号登录信息ES查询条件esQueryBuilders"+ JSON.toJSONString(esQueryBuilders));
        constructor.setDesc("operationTime");
        constructor.must(esQueryBuilders);
        Page page = new Page();
        if(dto.getPageNo()<1&&dto.getPageSize()<1){
            constructor.setFrom(0);
            constructor.setSize(0);
            page = elasticsearchReadService.searchList(constructor,  SalesDailyDto.class, index, type);
            return page;
        }
        constructor.setFrom(dto.getPageNo());
        constructor.setSize(dto.getPageSize());
        page = elasticsearchReadService.searchPage(constructor, OperationLogDto.class, index, type);
       /* //根据所有机构查询总条数
        Integer totalCount =0;
        totalCount = totalSizeQuery(dto.getOrganSigns(),constructorTotal,esQueryBuildersTotal,"salesCount","id",totalCount,index,type,"organSign.keyword");
        if(totalCount>0){
            page.setTotal(totalCount);
        }*/
        Long total = page.getTotal();
        Integer size = constructor.getSize();
        int m = total.intValue()/size;
        if(total.intValue()%size==0){
            page.setTotalPages(m);
        }else {
            page.setTotalPages(m+1);
        }

        return page;
    }

    @Override
    public <T> Page memberNewQueryByParma(MemberBaseDto dto, String index, String type) {
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        ESQueryBuilderConstructor constructorTotal = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        ESQueryBuilders esQueryBuildersTotal = new ESQueryBuilders();
        if(dto.getStartPoint()!=null&&dto.getEndPoint()!=null){
            esQueryBuilders.range("point",dto.getStartPoint(),dto.getEndPoint());
        }
        if(dto.getStartPoint()==null&&dto.getEndPoint()!=null){
            esQueryBuilders.rangeStart("point",dto.getStartPoint());
        }
        if(dto.getStartPoint()!=null&&dto.getEndPoint()==null){
            esQueryBuilders.rangeEnd("point",dto.getEndPoint());
        }
        if(!StringUtils.isEmpty(dto.getOrgansign())){
            esQueryBuilders.term("organsign.keyword",dto.getOrgansign());
        }
        if(!StringUtils.isEmpty(dto.getCartNo())){
            esQueryBuilders.wildcardQuery("cartNo.keyword",dto.getCartNo());
        }
        if(!StringUtils.isEmpty(dto.getMixedQuery())){
            esQueryBuilders.wildcardQuery("mixedQuery.keyword",dto.getMixedQuery());
        }
        BeanUtils.copyProperties(esQueryBuilders,esQueryBuildersTotal);
        esQueryBuildersTotal.listBuilders().addAll(esQueryBuilders.listBuilders());
        if(dto.getOrganSigns()!=null&&dto.getOrganSigns().size()>0){
            if(dto.getOrganSigns().indexOf(dto.getOrgansign())>=0){
                dto.setOrganSigns(null);
            }else {
                BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
                boolQueryBuilder.filter(QueryBuilders.termsQuery("organsign.keyword",dto.getOrganSigns()));
                esQueryBuilders.listBuilders().add(boolQueryBuilder);
               /* List<String> list = new ArrayList<>();
                if(dto.getOrganSigns().size()>500){
                    list = dto.getOrganSigns().subList(0,500);
                    esQueryBuilders.terms("organsign.keyword",list);
                }else {
                    esQueryBuilders.terms("organsign.keyword",dto.getOrganSigns());
                }*/
            }
        }
        constructor.must(esQueryBuilders);
        Page page = new Page();
        if(dto.getPageNum()<1&&dto.getPageSize()<1){
            constructor.setFrom(0);
            constructor.setSize(0);
            page = elasticsearchReadService.searchList(constructor,  SalesDailyDto.class, index, type);
            return page;
        }
        constructor.setFrom(dto.getPageNum());
        constructor.setSize(dto.getPageSize());

        logger.info("门店会员信息ES查询条件esQueryBuilders"+ JSON.toJSONString(esQueryBuilders));
        page = elasticsearchReadService.searchPage(constructor, MemberBaseDto.class, index, type);
        //根据所有机构查询总条数
       /* Integer totalCount =0;
        totalCount = totalSizeQuery(dto.getOrganSigns(),constructorTotal,esQueryBuildersTotal,"salesCount","id",totalCount,index,type,"organsign.keyword");
        if(totalCount>0){
            page.setTotal(totalCount);
        }*/
        Long total = page.getTotal();
        Integer size = constructor.getSize();
        int m = total.intValue()/size;
        if(total.intValue()%size==0){
            page.setTotalPages(m);
        }else {
            page.setTotalPages(m+1);
        }

        return page;
    }

    @Override
    public <T> Page findSumWithOrganSignByParam(AnalysisQueryDto dto, String index, String type) {
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        AggregationBuilder aggregationBuilder = AggregationBuilders.terms("organSign").field("organSign").size(10000);
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        aggregationBuilder.subAggregation(esQueryBuilders.sum("actualAmount","actualAmount"));
        aggregationBuilder.subAggregation(esQueryBuilders.sum("visitorNumbers","visitorNumbers"));
        aggregationBuilder.subAggregation(esQueryBuilders.sum("dayUsed","oneUsed"));
        aggregationBuilder.subAggregation(esQueryBuilders.sum("purchasingTimes","purchasingTimes"));
        if(!StringUtils.isEmpty(dto.getStartTime())&&!StringUtils.isEmpty(dto.getEndTime())){
            esQueryBuilders.listBuilders().add(QueryBuilders.rangeQuery("createTime").gte(dto.getStartTime()).lte(dto.getEndTime()));
        }
        if(!StringUtils.isEmpty(dto.getStartTime())&&StringUtils.isEmpty(dto.getEndTime())){
            esQueryBuilders.rangeStart("createTime",dto.getStartTime());
        }
        if(StringUtils.isEmpty(dto.getStartTime())&&!StringUtils.isEmpty(dto.getEndTime())){
            esQueryBuilders.rangeEnd("createTime",dto.getEndTime());
        }
        if(dto.getOrganSigns()!=null&&dto.getOrganSigns().size()>0){
            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.filter(QueryBuilders.termsQuery("organSign",dto.getOrganSigns()));
            esQueryBuilders.listBuilders().add(boolQueryBuilder);
        }
        logger.info("crm指标ES查询条件esQueryBuilders"+ JSON.toJSONString(esQueryBuilders));
        constructor.must(esQueryBuilders);
        List<T> ts = esSalesdailyService.searchListForCloud(constructor, aggregationBuilder, SalesDailyDto.class, index, type);
        Page page = new Page();
        page.setModelDatas(ts);
        return page;
    }


    private Integer totalSizeQuery(List<String> organSigns,ESQueryBuilderConstructor constructor,ESQueryBuilders esQueryBuilders,String name,String fild,Integer totalCount,String index,String type,String termsName){


        if(organSigns!=null&&organSigns.size()>500){
            List<String> temp = new ArrayList<>();
            for(int i=0;i<organSigns.size();i++){
                temp.add(organSigns.get(i));
                if(i>0&&i%500==0){
                    ESQueryBuilderConstructor constructorNew =new ESQueryBuilderConstructor();
                    BeanUtils.copyProperties(constructor,constructorNew);
                    ESQueryBuilders esQueryBuildersNew = new ESQueryBuilders();
                    esQueryBuildersNew.listBuilders().addAll(esQueryBuilders.listBuilders());
                    esQueryBuildersNew.terms(termsName,temp);
                    constructorNew.must(esQueryBuildersNew);
                    constructorNew.setFrom(0);
                    constructorNew.setSize(MAX_SIZE);
                    logger.info("查询总条数时，500条循环内ES查询条件esQueryBuilders"+ JSON.toJSONString(esQueryBuildersNew));
                    AggregationBuilder aggregationBuilder = AggregationBuilders.count(name).field(fild);
                    Map<Object, Object> objectObjectMap = elasticsearchReadService.statSearchNew(constructorNew, aggregationBuilder, index, type,name);
                    if(objectObjectMap!=null&&(Long)objectObjectMap.get(name)>0){
                        totalCount+=((Long) objectObjectMap.get(name)).intValue();
                    }
                    temp.clear();
                }
            }
            if(temp.size()>0){
                ESQueryBuilderConstructor constructorNew =new ESQueryBuilderConstructor();
                BeanUtils.copyProperties(constructor,constructorNew);
                ESQueryBuilders esQueryBuildersNew = new ESQueryBuilders();
                BeanUtils.copyProperties(esQueryBuilders,esQueryBuildersNew);
                esQueryBuildersNew.terms(termsName,temp);
                constructorNew.must(esQueryBuildersNew);
                logger.info("查询总条数时，500条循环外ES查询条件esQueryBuilders"+ JSON.toJSONString(esQueryBuildersNew));
                constructorNew.setFrom(0);
                constructorNew.setSize(MAX_SIZE);
                logger.info("查询总条数时，500条循环外ES查询条件constructor"+ JSON.toJSONString(constructorNew));
                AggregationBuilder aggregationBuilder = AggregationBuilders.count(name).field(fild);
                Map<Object, Object> objectObjectMap = elasticsearchReadService.statSearchNew(constructorNew, aggregationBuilder, index, type,name);
                if(objectObjectMap!=null&&(Long)objectObjectMap.get(name)>0){
                    totalCount+=((Long) objectObjectMap.get(name)).intValue();
                }
            }
        }
        return totalCount;
    }


}

