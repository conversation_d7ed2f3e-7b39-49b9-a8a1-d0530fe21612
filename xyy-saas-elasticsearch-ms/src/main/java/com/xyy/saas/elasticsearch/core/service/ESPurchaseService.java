package com.xyy.saas.elasticsearch.core.service;

import com.xyy.saas.elasticsearc.core.common.ESQueryBuilderConstructor;
import com.xyy.saas.elasticsearc.core.common.Page;
import org.elasticsearch.search.aggregations.AggregationBuilder;

/**
 * @annotation:
 * <AUTHOR>
 * @create 2019-08-08 17:14
 **/
public interface ESPurchaseService {
    /**
     * 分页带返回指定实体功能
     * @param constructor 查询构造
     *  @param aggregationBuilder
     * @param clazz 实体类型类
     */
    <T> Page searchPage(ESQueryBuilderConstructor constructor, AggregationBuilder aggregationBuilder, Class<T> clazz, String index, String type);


}

