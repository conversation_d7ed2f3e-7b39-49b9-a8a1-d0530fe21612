package com.xyy.saas.elasticsearch.core.api.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.elasticsearc.core.api.ESOrderDetailBatchApi;
import com.xyy.saas.elasticsearc.core.common.ESQueryBuilderConstructor;
import com.xyy.saas.elasticsearc.core.common.ESQueryBuilders;
import com.xyy.saas.elasticsearc.core.common.ESSearchUtil;
import com.xyy.saas.elasticsearc.core.common.Page;
import com.xyy.saas.elasticsearc.core.dto.DetailBatchQueryDto;
import com.xyy.saas.elasticsearc.core.dto.DetailBatchQuerySumDto;
import com.xyy.saas.elasticsearc.core.dto.OrderDetailBatchDto;
import com.xyy.saas.elasticsearch.core.config.ElasticSearchConfiguration;
import com.xyy.saas.elasticsearch.core.config.ElasticSearchConfigurationEsFive;
import com.xyy.saas.elasticsearch.core.enums.BussinessMoveEnum;
import com.xyy.saas.elasticsearch.core.enums.EsFiveIndexEnum;
import com.xyy.saas.elasticsearch.core.enums.NewIndexEnum;
import com.xyy.saas.elasticsearch.core.service.ESOrderDetailBatchService;
import com.xyy.saas.elasticsearch.core.service.ElasticFiveDataService;
import com.xyy.saas.elasticsearch.core.util.*;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.elasticsearch.action.bulk.BulkRequestBuilder;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.search.SearchRequestBuilder;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.*;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.metrics.sum.Sum;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @createTime 2022/6/21
 */
@Slf4j
@Service(version = "0.0.1")
public class ESOrderDetailBatchApiImpl implements ESOrderDetailBatchApi {

    private static final Logger logger = LoggerFactory.getLogger(ESOrderDetailBatchApiImpl.class);

    @Autowired
    private ESOrderDetailBatchService eSOrderDetailBatchService;

    @Autowired
    private ElasticFiveDataService elasticFiveDataService;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    //ES5 headDeatiBatch索引写开关
    @Value("${es5.headDeatiBatch.writeSwitch:true}")
    private Boolean headDeatiBatchWriteSwitch;

    @Override
    public void saveOrderDetailBatchToESBatch(String json) {
        if(!headDeatiBatchWriteSwitch){
            return;
        }
        try {
            //批量添加数据
            List<Map> resultMap = JSONArray.parseArray(json, Map.class);
            BulkRequestBuilder bulk = ElasticSearchConfigurationEsFive.client.prepareBulk();
            //循环添加数据
            for (Map<String,Object> orderDetailBatch : resultMap) {
                //这里是用我数据库里面的id作为ElasticSearch的文档id
                //销售时间
                if(Objects.nonNull(orderDetailBatch.get("saleTime"))){
                    String saleTime = DateUtil.getDateTimeFormat((Long)orderDetailBatch.get("saleTime"));
                    orderDetailBatch.put("saleTime", saleTime);
                }
                //销售数量
                if(Objects.nonNull(orderDetailBatch.get("stockNumber"))){
                    orderDetailBatch.put("stockNumber", ConvertUtil.obj2BigDecimal(orderDetailBatch.get("stockNumber")));
                }
                //批次成本价
                if(Objects.nonNull(orderDetailBatch.get("batchPrefCostPrice"))){
                    orderDetailBatch.put("batchPrefCostPrice", ConvertUtil.obj2BigDecimal(orderDetailBatch.get("batchPrefCostPrice")));
                }
                //成本均价
                if(Objects.nonNull(orderDetailBatch.get("costAverage"))){
                    orderDetailBatch.put("costAverage", ConvertUtil.obj2BigDecimal(orderDetailBatch.get("costAverage")));
                }
                //实际售价
                if(Objects.nonNull(orderDetailBatch.get("actualPrice"))){
                    orderDetailBatch.put("actualPrice", ConvertUtil.obj2BigDecimal(orderDetailBatch.get("actualPrice")));
                }
                //生产日期
                if(Objects.nonNull(orderDetailBatch.get("productTime"))){
                    String productTime = DateUtil.getDateTimeFormat((Long)orderDetailBatch.get("productTime"));
                    orderDetailBatch.put("productTime", productTime);
                }
                //有效期至
                if(Objects.nonNull(orderDetailBatch.get("productValiditTime"))){
                    String productValiditTime = DateUtil.getDateTimeFormat((Long)orderDetailBatch.get("productValiditTime"));
                    orderDetailBatch.put("productValiditTime", productValiditTime);
                }
                bulk.add(ElasticSearchConfigurationEsFive.client.prepareIndex(
                        PartitioningAlgorithmUtils.getPartitioningIndex(String.valueOf(orderDetailBatch.get("headquartersOrganSign")), NewIndexEnum.ORDER_HEADQUARTERS_ORDER_BATCH),
                        PartitioningAlgorithmUtils.getPartitioningType(String.valueOf(orderDetailBatch.get("headquartersOrganSign")), NewIndexEnum.ORDER_HEADQUARTERS_ORDER_BATCH),
                                orderDetailBatch.get("id").toString()).setSource(JSON.toJSONString(orderDetailBatch), XContentType.JSON));
            }
            //执行
            bulk.execute().actionGet();
            log.info("saveESOrderDetailBatch end");
        }catch (Exception e){
            log.error("saveESOrderDetailBatch error", e);
        }
    }

    @Override
    public <T> Page findOrderDetailBatchPageOld(DetailBatchQueryDto dto, String index, String type) {

        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        ESQueryBuilders esQueryBuildersTotal = new ESQueryBuilders();
        //销售时间
        if(!StringUtils.isEmpty(dto.getStartTime())&&!StringUtils.isEmpty(dto.getEndTime())){
            esQueryBuilders.listBuilders().add(QueryBuilders.rangeQuery("saleTime").gte(dto.getStartTime()).lte(dto.getEndTime()));
        }
        //机构查询
        if(!CollectionUtils.isEmpty(dto.getOrganSigns())){
            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.filter(QueryBuilders.termsQuery("organSign",dto.getOrganSigns()));
            esQueryBuilders.listBuilders().add(boolQueryBuilder);
        }
        //小票号
        if(!StringUtils.isEmpty(dto.getTicketNo())){
            esQueryBuilders.wildcardQuery("ticketNo",dto.getTicketNo());
        }
        //供应商名称
        if(!StringUtils.isEmpty(dto.getSupplierName())){
            esQueryBuilders.wildcardQuery("supplierName",dto.getSupplierName());
        }
        //商品信息
        if(!StringUtils.isEmpty(dto.getProductInfo())){
            esQueryBuilders.wildcardQuery("productInfo",dto.getProductInfo());
        }
        //批号
        if(!StringUtils.isEmpty(dto.getBatchNo())){
            esQueryBuilders.wildcardQuery("batchNo",dto.getBatchNo());
        }
        //批号
        if(!StringUtils.isEmpty(dto.getBatchPref())){
            esQueryBuilders.wildcardQuery("batchPref",dto.getBatchPref());
        }
        //生产厂家
        if(!StringUtils.isEmpty(dto.getManufacturerName())){
            esQueryBuilders.wildcardQuery("manufacturerName",dto.getManufacturerName());
        }

        esQueryBuildersTotal.listBuilders().addAll(esQueryBuilders.listBuilders());
        logger.info("订单批次信息查询条件: {}", JSON.toJSONString(esQueryBuilders));
        constructor.must(esQueryBuilders);
        Page page = new Page();
        constructor.setFrom(dto.getPageNum());
        constructor.setSize(dto.getPageSize());
        logger.info("订单批次信息查询条件constructor"+ JSON.toJSONString(constructor));
        page = eSOrderDetailBatchService.searchOrderDetailBatchPage(constructor, OrderDetailBatchDto.class, index, type);

        Long total = page.getTotal();
        Integer size = constructor.getSize();
        List back = page.getModelDatas();
        if(back!=null&&back.size()>0){
            page.setModelDatas(back.subList(constructor.getFrom()<0?0:size*(constructor.getFrom()-1),size*constructor.getFrom()>back.size()? back.size():size*constructor.getFrom()));
        }else {
            page.setModelDatas(null);
        }
        page.setCurrentPage(constructor.getFrom());
        int m = total.intValue()/size;
        if(total.intValue()%size==0){
            page.setTotalPages(m);
        }else {
            page.setTotalPages(m+1);
        }
        return page;
    }

    @Override
    public PageInfo<OrderDetailBatchDto> findOrderDetailBatchPage(PageInfo pageInfo, DetailBatchQueryDto detailBatchQueryDto) throws Exception {
        logger.info("detailBatchQueryDto入参值{} :", JSON.toJSONString(detailBatchQueryDto));
        List<String> organSigns = detailBatchQueryDto.getOrganSigns();
        String organSign = CollectionUtils.isEmpty(organSigns) ? "" : detailBatchQueryDto.getOrganSigns().get(0);
        String headOrganSign = detailBatchQueryDto.getHeadquartersOrganSign();
        if(StringUtils.isEmpty(organSign) && StringUtils.isEmpty(headOrganSign)){
            throw new RuntimeException("机构信息都为空，请检查！");
        }
        if(StringUtils.isEmpty(organSign) && !StringUtils.isEmpty(headOrganSign)){
            organSign = detailBatchQueryDto.getHeadquartersOrganSign();
        }
        Boolean chooseNewBatchIndex = elasticFiveDataService.es5OrganSignGraySwitch(organSign,organSigns, BussinessMoveEnum.QUERY_ORDER_DETAIL_BATCH);
        SearchRequestBuilder searchRequestBuilder = null;
        if(chooseNewBatchIndex){
            //如果传入的总部机构号为空，则查询用户服务，确认总部机构
            if(org.apache.commons.lang3.StringUtils.isBlank(headOrganSign)){
                SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);
                headOrganSign = drugstore.getHeadquartersOrganSign();
            }
            searchRequestBuilder = ElasticSearchConfigurationEsFive.client
                    .prepareSearch(RouteAlgorithmUtil.getPartitioningIndex(organSign, headOrganSign, EsFiveIndexEnum.ORDER_DETAIL_BATCH_SHARDING))
                    .setTypes(RouteAlgorithmUtil.getPartitioningType(organSign, headOrganSign, EsFiveIndexEnum.ORDER_DETAIL_BATCH_SHARDING))
                    .setRouting(RouteAlgorithmUtil.getPartitioningShard(organSign, headOrganSign, EsFiveIndexEnum.ORDER_DETAIL_BATCH_SHARDING));
            logger.info("机构号：{},总部机构号：{},查询批次明细走了ES5新批次明细索引",organSign, headOrganSign);
        }else{
            //获取es请求
            searchRequestBuilder = ElasticSearchConfigurationEsFive.client.prepareSearch(
                            PartitioningAlgorithmUtils.getPartitioningIndex(detailBatchQueryDto.getHeadquartersOrganSign(), NewIndexEnum.ORDER_HEADQUARTERS_ORDER_BATCH)).
                    setTypes(PartitioningAlgorithmUtils.getPartitioningType(String.valueOf(detailBatchQueryDto.getHeadquartersOrganSign()), NewIndexEnum.ORDER_HEADQUARTERS_ORDER_BATCH));
            logger.info("机构号：{},总部机构号：{},查询批次明细走了ES5旧批次明细索引",organSign, headOrganSign);
        }

        //设置查询条件  BoolQueryBuilder
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        //销售时间
        if (!StringUtils.isEmpty(detailBatchQueryDto.getStartTime()) && !StringUtils.isEmpty(detailBatchQueryDto.getEndTime())) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("saleTime")
                    .from(detailBatchQueryDto.getStartTime() + " 00:00:00")
                    .to(detailBatchQueryDto.getEndTime() + " 23:59:59")
                    .includeLower(true)
                    .includeUpper(true));
        }

        //总部机构号
        if(!StringUtils.isEmpty(detailBatchQueryDto.getHeadquartersOrganSign())){
            boolQueryBuilder.filter(QueryBuilders.termsQuery("headquartersOrganSign", detailBatchQueryDto.getHeadquartersOrganSign()));
        }

        //机构查询
        if(!CollectionUtils.isEmpty(detailBatchQueryDto.getOrganSigns())){
            boolQueryBuilder.filter(QueryBuilders.termsQuery("organSign",detailBatchQueryDto.getOrganSigns()));
        }
        //小票号
        if(!StringUtils.isEmpty(detailBatchQueryDto.getTicketNo())){
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("ticketNo","*"+detailBatchQueryDto.getTicketNo()+"*"));
        }
        //供应商名称
        if(!StringUtils.isEmpty(detailBatchQueryDto.getSupplierName())){
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("supplierName","*"+detailBatchQueryDto.getSupplierName()+"*"));
        }
        //商品信息
        if(!StringUtils.isEmpty(detailBatchQueryDto.getProductInfo())){
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("maxQuery","*"+detailBatchQueryDto.getProductInfo()+"*"));
        }
        //批号
        if(!StringUtils.isEmpty(detailBatchQueryDto.getBatchNo())){
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("batchNo","*"+detailBatchQueryDto.getBatchNo()+"*"));
        }
        //批次号
        if(!StringUtils.isEmpty(detailBatchQueryDto.getBatchPref())){
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("batchPref","*"+detailBatchQueryDto.getBatchPref()+"*"));
        }
        //生产厂家
        if(!StringUtils.isEmpty(detailBatchQueryDto.getManufacturerName())){
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("manufacturerName","*"+detailBatchQueryDto.getManufacturerName()+"*"));
        }


        QueryBuilder queryBuilder1 = boolQueryBuilder;
        int start = (pageInfo.getPageNum()-1)*pageInfo.getPageSize()>0?(pageInfo.getPageNum()-1)*pageInfo.getPageSize():0;
        searchRequestBuilder.setQuery(queryBuilder1).setSize(pageInfo.getPageSize()).setFrom(start).addSort("saleTime", SortOrder.DESC);

        log.info("findOrderDetailBatchPage:{}", searchRequestBuilder.toString());

        List<OrderDetailBatchDto> list = new ArrayList<>();
        //获取搜索结果集
        SearchHits hits = searchRequestBuilder.execute().actionGet().getHits();
        if (hits != null && hits.getHits() != null&&hits.getHits().length>0){
            ObjectMapper objectMapper = new ObjectMapper();
            for (SearchHit hit : hits.getHits()) {
                String productJson = hit.getSourceAsString();
                //将json转对象
                OrderDetailBatchDto libraryDto = objectMapper.readValue(productJson, OrderDetailBatchDto.class);
                libraryDto.setId(null);
                //添加至集合
                list.add(libraryDto);
            }
        }
        int count = (int)hits.getTotalHits();
        pageInfo.setTotal(hits.getTotalHits());
        pageInfo.setList(list);
        int pages = count%pageInfo.getPageSize()==0 ? count/pageInfo.getPageSize() : (count / pageInfo.getPageSize()) + 1;
        pageInfo.setPages(pages);
        pageInfo.setPageNum(pageInfo.getPageNum());
        pageInfo.setPageSize(pageInfo.getPageSize());
        pageInfo.setNavigateLastPage(pages);
        pageInfo.setNavigateFirstPage(1);
        pageInfo.setHasNextPage(pages==pageInfo.getPageNum()?false:true);
        pageInfo.setHasPreviousPage(pageInfo.getPageNum()==1?false:true);
        pageInfo.setIsFirstPage(pageInfo.getPageNum()==1?true:false);
        pageInfo.setIsLastPage(pageInfo.getPageNum()==pages?true:false);
        return pageInfo;
    }

    @Override
    public Map<String, Object> getSumOrderDetailBatch(DetailBatchQuerySumDto detailBatchQuerySumDto) {
        logger.info("getSumOrderDetailBatch param indexName:{} indexType:{} orderSearchDisplayDto:{}", JSONObject.toJSON(detailBatchQuerySumDto));
        List<String> organSigns = detailBatchQuerySumDto.getOrganSigns();
        String organSign = CollectionUtils.isEmpty(organSigns) ? "" : detailBatchQuerySumDto.getOrganSigns().get(0);
        String headOrganSign = detailBatchQuerySumDto.getHeadquartersOrganSign();
        if(StringUtils.isEmpty(organSign) && StringUtils.isEmpty(headOrganSign)){
            throw new RuntimeException("机构信息都为空，请检查！");
        }
        if(StringUtils.isEmpty(organSign) && !StringUtils.isEmpty(headOrganSign)){
            organSign = detailBatchQuerySumDto.getHeadquartersOrganSign();
        }
        Boolean chooseNewBatchIndex = elasticFiveDataService.es5OrganSignGraySwitch(organSign,organSigns, BussinessMoveEnum.QUERY_ORDER_DETAIL_BATCH);
        SearchRequestBuilder searchRequestBuilder = null;
        if(chooseNewBatchIndex){
            //如果传入的总部机构号为空，则查询用户服务，确认总部机构
            if(org.apache.commons.lang3.StringUtils.isBlank(headOrganSign)){
                SaaSDrugstoreDto drugstore = drugstoreApi.getDrugstoreByOrganSign(organSign);
                headOrganSign = drugstore.getHeadquartersOrganSign();
            }
            searchRequestBuilder = ElasticSearchConfigurationEsFive.client
                    .prepareSearch(RouteAlgorithmUtil.getPartitioningIndex(organSign, headOrganSign, EsFiveIndexEnum.ORDER_DETAIL_BATCH_SHARDING))
                    .setTypes(RouteAlgorithmUtil.getPartitioningType(organSign, headOrganSign, EsFiveIndexEnum.ORDER_DETAIL_BATCH_SHARDING))
                    .setRouting(RouteAlgorithmUtil.getPartitioningShard(organSign, headOrganSign, EsFiveIndexEnum.ORDER_DETAIL_BATCH_SHARDING));
            logger.info("机构号：{},总部机构号：{},查询批次明细走了ES5新批次明细索引",organSign, headOrganSign);
        }else{
            //获取es请求
            searchRequestBuilder = ElasticSearchConfigurationEsFive.client.prepareSearch(
                            PartitioningAlgorithmUtils.getPartitioningIndex(detailBatchQuerySumDto.getHeadquartersOrganSign(), NewIndexEnum.ORDER_HEADQUARTERS_ORDER_BATCH)).
                    setTypes(PartitioningAlgorithmUtils.getPartitioningType(String.valueOf(detailBatchQuerySumDto.getHeadquartersOrganSign()), NewIndexEnum.ORDER_HEADQUARTERS_ORDER_BATCH));
            logger.info("机构号：{},总部机构号：{},查询批次明细走了ES5旧批次明细索引",organSign, headOrganSign);
        }
        //设置sum列 cashPay  对应取结果列  cash_pay 对应数据库sum列
        searchRequestBuilder.addAggregation(AggregationBuilders.sum("stockNumber").field("stockNumber"));
        searchRequestBuilder.addAggregation(AggregationBuilders.sum("batchPrefCostPrice").field("batchPrefCostPrice"));
        if(!chooseNewBatchIndex){
            searchRequestBuilder.addAggregation(AggregationBuilders.sum("costAverage").field("costAverage"));
            searchRequestBuilder.addAggregation(AggregationBuilders.sum("actualPrice").field("actualPrice"));
        }
        BoolQueryBuilder boolQuery = buildShouldQuery(detailBatchQuerySumDto);

        searchRequestBuilder.setQuery(boolQuery);
        logger.info("getSumReceiptOrderDisplayInfo:{}", searchRequestBuilder.toString());
        SearchResponse searchResponse = searchRequestBuilder.execute().actionGet();

        return parseAggs(searchResponse.getAggregations().asMap());
    }

    /**
     * 構建shouldBoolquery對象
     * @param detailBatchQuerySumDto
     */
    private BoolQueryBuilder buildShouldQuery(DetailBatchQuerySumDto detailBatchQuerySumDto){
        //设置查询条件  BoolQueryBuilder
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        //销售时间
        if (!StringUtils.isEmpty(detailBatchQuerySumDto.getStartTime()) && !StringUtils.isEmpty(detailBatchQuerySumDto.getEndTime())) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("saleTime")
                    .from(detailBatchQuerySumDto.getStartTime() + " 00:00:00")
                    .to(detailBatchQuerySumDto.getEndTime() + " 23:59:59")
                    .includeLower(true)
                    .includeUpper(true));
        }

        //总部机构号
        if(!StringUtils.isEmpty(detailBatchQuerySumDto.getHeadquartersOrganSign())){
            boolQueryBuilder.filter(QueryBuilders.termsQuery("headquartersOrganSign", detailBatchQuerySumDto.getHeadquartersOrganSign()));
        }

        //机构查询
        if(!CollectionUtils.isEmpty(detailBatchQuerySumDto.getOrganSigns())){
            boolQueryBuilder.filter(QueryBuilders.termsQuery("organSign",detailBatchQuerySumDto.getOrganSigns()));
        }
        //小票号
        if(!StringUtils.isEmpty(detailBatchQuerySumDto.getTicketNo())){
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("ticketNo","*"+detailBatchQuerySumDto.getTicketNo()+"*"));
        }
        //供应商名称
        if(!StringUtils.isEmpty(detailBatchQuerySumDto.getSupplierName())){
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("supplierName","*"+detailBatchQuerySumDto.getSupplierName()+"*"));
        }
        //商品信息
        if(!StringUtils.isEmpty(detailBatchQuerySumDto.getProductInfo())){
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("maxQuery","*"+detailBatchQuerySumDto.getProductInfo()+"*"));
        }
        //批号
        if(!StringUtils.isEmpty(detailBatchQuerySumDto.getBatchNo())){
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("batchNo","*"+detailBatchQuerySumDto.getBatchNo()+"*"));
        }
        //批次号
        if(!StringUtils.isEmpty(detailBatchQuerySumDto.getBatchPref())){
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("batchPref","*"+detailBatchQuerySumDto.getBatchPref()+"*"));
        }
        //生产厂家
        if(!StringUtils.isEmpty(detailBatchQuerySumDto.getManufacturerName())){
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("manufacturerName","*"+detailBatchQuerySumDto.getManufacturerName()+"*"));
        }

        return boolQueryBuilder;
    }

    /**
     * 解析sum结果
     * @param asMap
     * @return
     */
    private Map<String,Object> parseAggs(Map<String, Aggregation> asMap){
        Map<String,Object> result = new HashMap<String,Object>();
        for (String key : asMap.keySet()) {
            Aggregation aggregation = asMap.get(key);
            double resultValue = ((Sum) aggregation).getValue();
            BigDecimal b = new BigDecimal(Double.toString(resultValue));
            result.put(key,b.setScale(3, BigDecimal.ROUND_HALF_UP));
        }
        return result;
    }

    @Override
    public Boolean deleteOrderDetailBatch(String organSign, String headOrganSign, String ticketNo, boolean isSetEsRefreshPolicy) {

        // 仅连锁、联营门店保存订单明细批次信息
        if (StringUtils.isEmpty(organSign) || StringUtils.isEmpty(headOrganSign) || StringUtils.isEmpty(ticketNo)) {
            return false;
        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("organSign", organSign))
                .must(QueryBuilders.termQuery("ticketNo", ticketNo));

        DeleteByQueryRequestBuilder builder = DeleteByQueryAction.INSTANCE.newRequestBuilder(ElasticSearchConfigurationEsFive.client);
        builder.filter(boolQueryBuilder)
                .source(RouteAlgorithmUtil.getPartitioningIndex(organSign, headOrganSign, EsFiveIndexEnum.ORDER_DETAIL_BATCH_SHARDING))  // index
                .source().setTypes(RouteAlgorithmUtil.getPartitioningType(organSign, headOrganSign, EsFiveIndexEnum.ORDER_DETAIL_BATCH_SHARDING))// type
                .setRouting(RouteAlgorithmUtil.getPartitioningShard(organSign, headOrganSign, EsFiveIndexEnum.ORDER_DETAIL_BATCH_SHARDING));
        if (isSetEsRefreshPolicy) {
            builder.refresh(true);
        }
        builder.get();

        return true;
    }

    @Override
    public void batchSaveOrderDetailBatch(String organSign, String headOrganSign, String json, boolean isSetEsRefreshPolicy) {

        logger.info("batchSaveOrderDetailBatch{} :", json);

        // 仅连锁、联营门店保存订单明细批次信息
        if (StringUtils.isEmpty(organSign) || StringUtils.isEmpty(headOrganSign) || StringUtils.isEmpty(json)) {
            return;
        }

        try {
            Map<String, Object> map = JacksonUtil.json2map(json);

            List<Map<String, Object>> orderDetailBatchsMap = (List<Map<String, Object>>) map.get("orderDetailBatchs");
            if (CollectionUtils.isEmpty(orderDetailBatchsMap)) {
                return;
            }

            BulkRequestBuilder bulkRequest = ElasticSearchConfigurationEsFive.client.prepareBulk();
            if (isSetEsRefreshPolicy) {
                bulkRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.WAIT_UNTIL);
            }
            for (Map<String, Object> orderDetailBatch : orderDetailBatchsMap) {

                if (orderDetailBatch.get("saleTime") != null && org.apache.commons.lang3.StringUtils.isNotBlank(orderDetailBatch.get("saleTime").toString())) {
                    orderDetailBatch.put("saleTime", DateUtil.getDateTimeFormat((Long) orderDetailBatch.get("saleTime")));
                }
                orderDetailBatch.put("supplierName", orderDetailBatch.get("providerName"));
                orderDetailBatch.put("manufacturerName", orderDetailBatch.get("productManufacturer"));
                orderDetailBatch.put("batchPrefCostPrice", orderDetailBatch.get("costPriceByInventory"));
                if (orderDetailBatch.get("producedDate") != null && org.apache.commons.lang3.StringUtils.isNotBlank(orderDetailBatch.get("producedDate").toString())) {
                    orderDetailBatch.put("productTime", DateUtil.getDateTimeFormat((Long) orderDetailBatch.get("producedDate")));
                }
                if (orderDetailBatch.get("expirationDate") != null && org.apache.commons.lang3.StringUtils.isNotBlank(orderDetailBatch.get("expirationDate").toString())) {
                    orderDetailBatch.put("productValiditTime", DateUtil.getDateTimeFormat((Long) orderDetailBatch.get("expirationDate")));
                }

                String detailBatchId = orderDetailBatch.get("id").toString();

                orderDetailBatch.remove("commonName");
                orderDetailBatch.remove("refundNum");
                orderDetailBatch.remove("producedDate");
                orderDetailBatch.remove("costPriceByInventory");
                orderDetailBatch.remove("organSignName");
                orderDetailBatch.remove("batchId");
                orderDetailBatch.remove("productRetailPrice");
                orderDetailBatch.remove("productManufacturer");
                orderDetailBatch.remove("unitId");
                orderDetailBatch.remove("id");
                orderDetailBatch.remove("providerName");
                orderDetailBatch.remove("expirationDate");
                orderDetailBatch.remove("orderDetailGuid");
                orderDetailBatch.remove("productPref");
                orderDetailBatch.remove("updateUser");
                orderDetailBatch.remove("updateTime");
                orderDetailBatch.remove("attributeSpecification");
                orderDetailBatch.remove("inTaxPrice");
                orderDetailBatch.remove("lotNumber");
                orderDetailBatch.remove("productOriginAddress");
                orderDetailBatch.remove("approvalNumber");
                orderDetailBatch.remove("uploadTime");
                orderDetailBatch.remove("productCode");
                orderDetailBatch.remove("createTime");
                orderDetailBatch.remove("guid");
                orderDetailBatch.remove("createUser");
                orderDetailBatch.remove("dosageFormId");
                orderDetailBatch.remove("pharmacyPref");
                orderDetailBatch.remove("beginTime");
                orderDetailBatch.remove("guids");
                orderDetailBatch.remove("costPrice");
                orderDetailBatch.remove("outChannelId");
                orderDetailBatch.remove("ticketNoList");
                orderDetailBatch.remove("endTime");

                bulkRequest.add(ElasticSearchConfigurationEsFive.client.prepareIndex(
                        RouteAlgorithmUtil.getPartitioningIndex(organSign, headOrganSign, EsFiveIndexEnum.ORDER_DETAIL_BATCH_SHARDING),
                        RouteAlgorithmUtil.getPartitioningType(organSign, headOrganSign, EsFiveIndexEnum.ORDER_DETAIL_BATCH_SHARDING),
                        detailBatchId).setSource(JSON.toJSONString(orderDetailBatch), XContentType.JSON).setRouting(RouteAlgorithmUtil.getPartitioningShard(organSign, headOrganSign, EsFiveIndexEnum.ORDER_DETAIL_BATCH_SHARDING)));

            }

            if (bulkRequest.numberOfActions() > 0) {
                log.info("保存ES5订单明细批次索引bulk请求数:{},organSign:{},ticketNo:{}", bulkRequest.numberOfActions(), organSign, orderDetailBatchsMap.get(0).get("ticketNo"));
                bulkRequest.get();
            }

        } catch (Exception e) {
            log.error("ES5订单明细批次索引数据保存失败，失败原因:{}", e.getMessage());
            throw new RuntimeException("ES5订单明细批次索引数据保存失败", e);
        }
    }

}