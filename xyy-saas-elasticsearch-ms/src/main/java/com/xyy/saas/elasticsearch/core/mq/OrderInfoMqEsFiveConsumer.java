package com.xyy.saas.elasticsearch.core.mq;

import com.xyy.saas.elasticsearch.core.service.ElasticFiveDataService;
import com.xyy.saas.elasticsearch.core.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQPushConsumerLifecycleListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * 订单信息写ES5消费者
 * @date 2023/04/25 11:13
 */
@Service
@Slf4j
@RocketMQMessageListener(topic = "${demo.rocketmq.esOrderTopic}", consumerGroup = "${rocketmq.consumer.groupEsFiveOrderTopic}" , consumeMode = ConsumeMode.CONCURRENTLY)
public class OrderInfoMqEsFiveConsumer implements RocketMQListener<String>, RocketMQPushConsumerLifecycleListener {

    @Autowired
    private ElasticFiveDataService elasticFiveDataService;

    @Value("${es5.syncOrderInfoSwitch:true}")
    private boolean syncOrderInfoSwitch;

    @Override
    public void onMessage(String orderStr) {
        if(!syncOrderInfoSwitch){
            return;
        }
        try {
            log.info("ES5集群监听收到的订单信息:{}" , orderStr);
            Map<String, Object> orders = JacksonUtil.json2map(orderStr);
            elasticFiveDataService.saveOrderInfoIndex(orders, false);
            elasticFiveDataService.saveOrderDetailIndex(orders, false);
            elasticFiveDataService.saveOrderDetailBatchIndex(orders, false);
            log.info("ES5集群消费订单信息完成！");
        } catch (Exception e) {
            log.error("ES5集群消费订单信息异常，即将再次返回队列处理...{}",e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void prepareStart(DefaultMQPushConsumer defaultMQPushConsumer) {

    }
}
