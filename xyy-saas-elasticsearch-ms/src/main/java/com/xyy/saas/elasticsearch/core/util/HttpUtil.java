package com.xyy.saas.elasticsearch.core.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HttpUtil {
	private static Logger logger = LoggerFactory.getLogger(HttpUtil.class);

	private static SSLContext sslcontext = null;

	private static X509TrustManager tm = null;

	private static SSLConnectionSocketFactory sslsf = null;

	static {
		tm = new X509TrustManager() {

			@Override
			public void checkClientTrusted(X509Certificate[] xcs, String string)
					throws CertificateException {
			}

			@Override
			public void checkServerTrusted(X509Certificate[] xcs, String string)
					throws CertificateException {
			}

			@Override
			public X509Certificate[] getAcceptedIssuers() {
				return null;
			}
		};

		try {
			sslcontext = SSLContext.getInstance("SSL");
			sslcontext.init(null, new TrustManager[]{tm}, null);
		} catch (NoSuchAlgorithmException e) {
			logger.error("error", e);
		} catch (KeyManagementException e) {
			logger.error("error", e);
		}

		sslsf = new SSLConnectionSocketFactory(sslcontext,
				new String[]{"TLSv1"}, null,
				SSLConnectionSocketFactory.BROWSER_COMPATIBLE_HOSTNAME_VERIFIER);
	}

	public static String getAsString(String url) throws IOException,
			KeyManagementException, NoSuchAlgorithmException {
		CloseableHttpClient httpclient = HttpUtil.createHttpClient(url);
		HttpGet httpGet = new HttpGet(url);
		CloseableHttpResponse response = httpclient.execute(httpGet);
		return EntityUtils.toString(response.getEntity());
	}

	public static String postAsString(String url)
			throws IOException, KeyManagementException,
			NoSuchAlgorithmException {
		CloseableHttpClient httpclient = HttpUtil.createHttpClient(url);
		HttpPost httpPost = new HttpPost(url);
		CloseableHttpResponse response = httpclient.execute(httpPost);
		return EntityUtils.toString(response.getEntity());
	}

	public static String postAsList(String url,List<String> list)
			throws IOException, KeyManagementException,
			NoSuchAlgorithmException {
		CloseableHttpClient httpclient = HttpUtil.createHttpClient(url);
		HttpPost httpPost = new HttpPost(url);
		List<NameValuePair> params = new ArrayList<NameValuePair>();
		NameValuePair pair = new BasicNameValuePair("productIdList", String.join(",",list));
		params.add(pair);
		httpPost.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
		CloseableHttpResponse response = httpclient.execute(httpPost);
		return EntityUtils.toString(response.getEntity());
	}

	public static String post(String url, Map<String, String> params, Map<String, String> heads)
			throws IOException, KeyManagementException, NoSuchAlgorithmException {
		CloseableHttpClient httpclient = HttpUtil.createHttpClient(url);
		HttpPost httpPost = new HttpPost(url);
		List<NameValuePair> nvps = new ArrayList<NameValuePair>();
		if (params != null) {
			for (Map.Entry<String, String> entry : params.entrySet()) {
				nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
			}
		}

		if (heads != null) {
			for (Map.Entry<String, String> entry : heads.entrySet()) {
				httpPost.setHeader(entry.getKey(), entry.getValue());
			}
		}

		httpPost.setEntity(new UrlEncodedFormEntity(nvps));
		CloseableHttpResponse response = httpclient.execute(httpPost);
		return EntityUtils.toString(response.getEntity());
	}

	public static String postJson(String url, String json, Map<String, String> headers) {
		URL u = null;
		HttpURLConnection con = null;
		OutputStreamWriter osw = null;
		// 构建请求参数
		StringBuffer sb = new StringBuffer();
		sb.append(json);
		// 尝试发送请求
		try {
			u = new URL(url);
			con = (HttpURLConnection) u.openConnection();
			// // POST 只能为大写，严格限制，post会不识别
			con.setRequestMethod("POST");
			con.setDoOutput(true);
			con.setDoInput(true);
			con.setUseCaches(false);
			con.setRequestProperty("Content-Type", "application/json");
			for (String header : headers.keySet()) {
				con.setRequestProperty(header, headers.get(header));
			}

			osw = new OutputStreamWriter(con.getOutputStream(), "UTF-8");
			osw.write(sb.toString());
			osw.flush();
			osw.close();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (con != null) {
				con.disconnect();
			}
			if (osw != null) {
				try {
					osw.close();
				} catch (Exception ex) {
					ex.printStackTrace();
				}
			}
		}
		// 读取返回内容
		StringBuffer buffer = new StringBuffer();
		try {
			// 一定要有返回值，否则无法把请求发送给server端。
			BufferedReader br = new BufferedReader(new InputStreamReader(
					con.getInputStream(), "UTF-8"));
			String temp;
			while ((temp = br.readLine()) != null) {
				buffer.append(temp);
				buffer.append("\n");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return buffer.toString();
	}

	public static String postJson(String url, String json) {
		return postJson(url, json, new HashMap<String, String>(0));
	}

	private static CloseableHttpClient createHttpClient(String url)
			throws KeyManagementException, NoSuchAlgorithmException,
			MalformedURLException {
		URL u = new URL(url);
		CloseableHttpClient httpclient = null;
		if ("https".equals(u.getProtocol())) {
			logger.trace("https");
			httpclient = HttpClients.custom().setSSLSocketFactory(sslsf)
					.build();
		} else {
			logger.trace("http");
			httpclient = HttpClients.createDefault();
		}

		return httpclient;
	}
	
	/** 
     * 获取用户真实IP地址
     *  
     * @return ip
     */
    public static String getIpAddr(HttpServletRequest request) {
    		String ip = request.getHeader("X-Forwarded-For");
		if(StringUtils.isEmpty(ip)) {
			ip = request.getRemoteHost();
		}else {
			String [] tmps = ip.split(",");
			String s = null;
			if(tmps.length >= 1) {
				s = tmps[0].trim();
			}
			if(ip.matches("^\\d{1,3}(\\.\\d{1,3}){3}$")) {
				return s;
			}
			ip = request.getRemoteHost();
		}
		return ip;  
    }
    
    public static void addCookie(HttpServletResponse resp, HttpServletRequest req, String key, String value,
                                 int maxAgeInSec, boolean httpOnly, boolean dynamicDomain) {
		req.getRemoteHost();
		Cookie cookie = new Cookie(key, value);
		cookie.setHttpOnly(httpOnly);
		cookie.setMaxAge(maxAgeInSec);
//		String domain = getDomain(req, dynamicDomain);
//		if (!(StringUtil.isEmpty(domain))) {
//			cookie.setDomain(domain);
//			cookie.setPath("/");
//		} else {
			cookie.setPath(req.getContextPath());
//		}
		resp.addCookie(cookie);
	}
    
    public static String getCookie(HttpServletRequest request, String name) {
		try{
			Cookie[] cookies = request.getCookies();
			if (cookies == null)
				return null;
			for (Cookie cookie : cookies) {
				if (cookie.getName().equals(name)) {
					return cookie.getValue();
				}
			}
		}catch(Throwable t){
			logger.error("error", t);
		}
		return null;
	}
}
