package com.xyy.saas.elasticsearch.core.enums;

import java.util.Arrays;
import java.util.List;

public enum OrderPayTypeEnum {

//    ALL(-1,"", "未知类型"),
    CASH_PAY(1,"cash_pay", "现金支付"),
    ALI_PAY(2,"ali_pay", "支付宝支付"),
    WECHAT_PAY(3,"wechat_pay", "微信支付"),
    MEDICARE_PAY(4,"medicare_pay", "医保支付"),
    UNION_PAY(5,"union_pay", "银联支付"),
    STORED_VALUE_PAY(6,"stored_value_pay", "储值支付"),
    TOGETHER_PAY(7,"together_pay", "聚合支付"),
    OTHER_PAY(8,"other_pay", "其他支付");

    private Integer index;
    private String type;
    private String name;

    OrderPayTypeEnum(Integer index, String type, String name) {
        this.index = index;
        this.type = type;
        this.name = name;
    }


    public static List<OrderPayTypeEnum> get() {
        OrderPayTypeEnum[] values = OrderPayTypeEnum.values();
        List<OrderPayTypeEnum> clientEnums = Arrays.asList(values);
        return clientEnums;
    }

    public Integer getIndex() {
        return index;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }


}
