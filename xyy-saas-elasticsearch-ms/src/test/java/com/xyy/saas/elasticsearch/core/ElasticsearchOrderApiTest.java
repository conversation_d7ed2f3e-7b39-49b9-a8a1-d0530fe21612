package com.xyy.saas.elasticsearch.core;

import com.xyy.saas.elasticsearc.core.api.*;
import com.xyy.saas.elasticsearc.core.dto.EsOrderQueryDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Map;

/**
 * Created by pc on 2019/1/26.
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class ElasticsearchOrderApiTest {

    @Autowired
    private ESNewOrderIndexReportApi esOrderInfoApi;

    /**
     * 初始化索引
     * */
    @Test
    public void createOrderHeadquartersAndMonomerIndex() throws Exception{
        Boolean result = esOrderInfoApi.createOrderHeadquartersAndMonomerIndex(null);
        System.out.println(result);
    }

    @Test
    public void getMonomerOrderList(){
        EsOrderQueryDTO esOrderQueryDTO = new EsOrderQueryDTO();
        esOrderQueryDTO.setOrganSign("ZHL00000272");
        esOrderQueryDTO.setPageIndex(1);
        esOrderQueryDTO.setPageSize(100);
        Map result = esOrderInfoApi.getMonomerOrderList(esOrderQueryDTO);
        System.out.println(result);
    }

    @Test
    public void getHeadquartersOrderListCount(){
        Map result = esOrderInfoApi.getHeadquartersOrderListCount(null);
        System.out.println(result);
    }

    @Test
    public void getMonomerOrderListCount(){
        Map result = esOrderInfoApi.getMonomerOrderListCount(null);
        System.out.println(result);
    }

    /**
     * 初始化索引
     * */
    @Test
    public void createOrderMonomerIndex() throws Exception{
        Boolean result = esOrderInfoApi.createOrderMonomerIndex(null);
        System.out.println(result);
    }


    /**
     * 初始化索引
     * */
    @Test
    public void deleteOrderMonomerIndex() throws Exception{
        Boolean result = esOrderInfoApi.deleteOrderMonomerIndex(null);
        System.out.println(result);
    }

}
