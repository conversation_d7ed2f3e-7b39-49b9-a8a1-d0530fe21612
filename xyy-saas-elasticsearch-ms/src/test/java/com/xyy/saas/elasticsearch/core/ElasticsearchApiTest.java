package com.xyy.saas.elasticsearch.core;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.elasticsearc.core.api.*;
import com.xyy.saas.elasticsearc.core.common.ESQueryBuilderConstructor;
import com.xyy.saas.elasticsearc.core.common.ESQueryBuilders;
import com.xyy.saas.elasticsearc.core.common.Page;
import com.xyy.saas.elasticsearc.core.dto.*;
import com.xyy.saas.elasticsearch.core.api.impl.ESorderReportApiImpl;
import com.xyy.saas.elasticsearch.core.service.ESOrderReportService;
import com.xyy.saas.elasticsearch.core.service.EsStandLibraryService;
import org.elasticsearch.action.search.SearchRequestBuilder;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.StringTerms;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by pc on 2019/1/26.
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("test3")
public class ElasticsearchApiTest {

    @Autowired
    private ElasticsearchReadApi elasticsearchReadApi;

    @Autowired
    private ElasticsearchWriteApi elasticsearchWriteApi;

    @Reference(version = "0.0.1")
    private EsStandLibraryApi esStandLibraryApi;

    @Autowired
    private EsStandLibraryService esStandLibraryService;
    @Reference(version = "0.0.1")
    private ESorderReportApi eSorderReportApi;

    @Autowired
    private ESorderReportApi esOrderReportApi;

    @Reference(version = "0.0.1")
    private HandoverAndOrderDeleteApi handoverAndOrderDeleteApi;

    @Reference(version = "0.0.1")
    private ESIncomeStatementApi esIncomeStatementApi;

    @Reference(version = "0.0.1")
    private ESOrderInfoApi esOrderInfoApi;
    @Autowired
    private ESOrderDetailApi esOrderDetailApi;

    @Reference(version = "1.0.0")
    private ESProductBaseinfoApi esProductBaseinfoApi;


    @Test
    public void testDeleteIncome() throws Exception {
        Boolean bool = esIncomeStatementApi.deleteIncomeStatement("ZHL00000456");
        System.out.println(bool);
    }

    @Test
    public void testDelete() throws Exception {
        Boolean bool = handoverAndOrderDeleteApi.deleteHandoverAndOrder("ZHL00001642");
        System.out.println(bool);
    }

    @Autowired
    private OrderElasticsearchInfoApi orderElasticsearchInfoApi;



    @Test
    public void testPage2() throws Exception {
        ProductReportQueryDto dto = new ProductReportQueryDto();
        dto.setStartTime("2019-07-23");
        dto.setEndTime("2019-08-22");
        dto.setPageSize(50);
        dto.setPageNum(1);
        Page page1 =  esOrderReportApi.ybmPriceAdvantageAnaysis(dto, "saas_summary_statistics", "saas_summary_statistics");
        System.out.println(page1.getTotal());
    }

    @Test
    public void testPage() throws Exception {
        StandLibraryDto standLibraryDto = new StandLibraryDto();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(10);
        pageInfo.setPageNum(1);
        standLibraryDto.setCommonName("6970617491697");
        //standLibraryDto.setCommonName("骨筋丸胶囊");
        //standLibraryDto.setApprovalNumber("国药准字*********");
        //standLibraryDto.setManufacturer("湖北");
        PageInfo<StandLibraryDto> pageInfo1 =  esStandLibraryService.findStandLibraryPage(pageInfo,standLibraryDto);
        System.out.println("aaa");
    }

    @Test
    public void testPage1() throws Exception {
        StandLibraryDto standLibraryDto = new StandLibraryDto();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(10);
        pageInfo.setPageNum(1);
        standLibraryDto.setCommonName("6922348918189");
        //standLibraryDto.setManufacturer("湖北");
        PageInfo<StandLibraryDto> pageInfo1 =  esStandLibraryApi.findStandLibraryPage(pageInfo,standLibraryDto);
        System.out.println("aaa");
    }

    /**
     * @Description  term 查询 相当于mysql 等于
     * @return void
     * <AUTHOR>
     * @Date 2019/4/1
     **/
    @Test
    public void term() throws Exception{
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        esQueryBuilders.wildcardQuery("mixCondition", "6922348918189");
        constructor.must(esQueryBuilders);
        constructor.setSize(10);
        constructor.setFrom(0);
        Map<String,Object> map = elasticsearchReadApi.searchPageMap(constructor,"saas_standlibrary","standLibrary");
        System.out.println(JSON.toJSONString(map.toString()));
    }

    /**
     * @Description prefixQuery前缀查询相当于 mysql的like 'prefix%'
     * @return void
     * <AUTHOR>
     * @Date 2019/4/1
     **/
    @Test
    public void prefixQuery() throws Exception{
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        esQueryBuilders.prefixQuery("organSign.keyword", "ZHL00000438");
        constructor.must(esQueryBuilders);
        constructor.setSize(10);
        constructor.setFrom(0);
        Map<String,Object> map = elasticsearchReadApi.searchPageMap(constructor,"saas_order_info","saas_order_info");
        System.out.println(JSON.toJSONString(map.toString()));
    }

    /**
     * @Description  wildcardQuery 相当于mysql 模糊查询
     * @return void
     * <AUTHOR>
     * @Date 2019/4/1
     **/
    @Test
    public void wildcardQuery() throws Exception{
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        esQueryBuilders.wildcardQuery("organSign.keyword", "ZHL00000438");
        constructor.must(esQueryBuilders);
        constructor.setSize(10);
        constructor.setFrom(0);
        Map<String,Object> map = elasticsearchReadApi.searchPageMap(constructor,"saas_order_info","saas_order_info");
        System.out.println(JSON.toJSONString(map.toString()));
    }

    /**
     * @Description  wildcardQuery 相当于mysql 左模糊查询
     * @return void
     * <AUTHOR>
     * @Date 2019/4/1
     **/
    @Test
    public void wildcardLeftQuery() throws Exception{
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        esQueryBuilders.wildcardLeftQuery("organSign.keyword", "ZHL00000438");
        constructor.must(esQueryBuilders);
        constructor.setSize(10);
        constructor.setFrom(0);
        Map<String,Object> map = elasticsearchReadApi.searchPageMap(constructor,"saas_order_info","saas_order_info");
        System.out.println(JSON.toJSONString(map.toString()));
    }

    /**
     * @Description  wildcardQuery 相当于mysql 右模糊查询
     * @return void
     * <AUTHOR>
     * @Date 2019/4/1
     **/
    @Test
    public void wildcardRightQuery() throws Exception{
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        esQueryBuilders.wildcardRightQuery("organSign.keyword", "ZHL00000438");
        constructor.must(esQueryBuilders);
        constructor.setSize(10);
        constructor.setFrom(0);
        Map<String,Object> map = elasticsearchReadApi.searchPageMap(constructor,"saas_order_info","saas_order_info");
        System.out.println(JSON.toJSONString(map.toString()));
    }

    /**
     * @Description  terms 查询 相当于mysql in
     * @return void
     * <AUTHOR>
     * @Date 2019/4/1
     **/
    @Test
    public void terms() throws Exception{
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        List<String> list = new ArrayList<>();
        list.add("ZHL00000438");
        esQueryBuilders.terms("organSign.keyword", list);
        constructor.must(esQueryBuilders);
        constructor.setSize(10);
        constructor.setFrom(0);
        Map<String,Object> map = elasticsearchReadApi.searchPageMap(constructor,"saas_order_info","saas_order_info");
        System.out.println(JSON.toJSONString(map.toString()));
    }

    /**
     * @Description  Not 查询
     * @return void
     * <AUTHOR>
     * @Date 2019/4/1
     **/
    @Test
    public void mustNot() throws Exception{
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        esQueryBuilders.term("organSign.keyword","ZHL00000438");
        constructor.mustNot(esQueryBuilders);
        constructor.setSize(10);
        constructor.setFrom(0);
        Map<String,Object> map = elasticsearchReadApi.searchPageMap(constructor,"saas_order_info","saas_order_info");
        System.out.println(JSON.toJSONString(map.toString()));
    }

    /**
     * @Description  OR 查询
     * @return void
     * <AUTHOR>
     * @Date 2019/4/1
     **/
    @Test
    public void should() throws Exception{
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        List<String> list = new ArrayList<>();
        list.add("ZHL00000438");
        esQueryBuilders.terms("organSign.keyword", list);
        esQueryBuilders.term("organSign.keyword","ZHL00000438");
        constructor.should(esQueryBuilders);
        constructor.setSize(10);
        constructor.setFrom(0);
        Map<String,Object> map = elasticsearchReadApi.searchPageMap(constructor,"saas_order_info","saas_order_info");
        System.out.println(JSON.toJSONString(map.toString()));
    }



    /**
     * @Description  range 查询 相当于mysql 大小于比较
     * @return void
     * <AUTHOR>
     * @Date 2019/4/1
     **/
    @Test
    public void range() throws Exception{
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        esQueryBuilders.range("total_number",1,1000);
        constructor.must(esQueryBuilders);
        constructor.setSize(10);
        constructor.setFrom(0);
        Map<String,Object> map = elasticsearchReadApi.searchPageMap(constructor,"saas_order_info","saas_order_info");
        System.out.println(JSON.toJSONString(map.toString()));
    }


    /**
     * @Description  添加数据
     * @return void
     * <AUTHOR>
     * @Date 2019/4/1
     **/
    @Test
    public void bulkInsertData() throws Exception{
        String json = "[{total_number=2.000, base_version=1, upload=true, discount_amount=0.00, actual_amount=25.00, buyer_name=, receivable_amount=25.00, prescription_yn=false, change_amount=75.00, update_time=2018-10-21T19:06:51, buyer_mobile=, update_user=1090, yn=true, ticket_no=POS17101, gross_profit_amount=7.65, wechat_pay=0.00, id=1351811, seller_id=1090, organSign=ZHL00000438, medicare_pay=0.00, before_cut_amount=0.00, union_pay=0.00, extract_status=0, create_time=2018-10-21T19:06:51, bast_yn=false, cash_pay=100.00, other_pay=0.00, member_actual_amount=0.00, gather_amount=100.00, cost_amount=17.35, buyer_idcard=, guid=42883cc7-e770-4e5b-8a40-bb959c7bb006, stored_value_pay=0.00, ali_pay=0.00, create_user=1090, status=0}]";
        List<Map<String,Object>> listObjectFir = (List<Map<String,Object>>) JSONArray.parse(json);
        System.out.println(elasticsearchWriteApi.bulkInsertData(listObjectFir,"saas_order_info","saas_order_info"));
    }


    /**
     * @Description  更新数据
     * @return void
     * <AUTHOR>
     * @Date 2019/4/1
     **/
    @Test
    public void updateData() throws Exception{
        String json = "{total_number=2.000, base_version=1, upload=true, discount_amount=0.00, actual_amount=25.00, buyer_name=, receivable_amount=25.00, prescription_yn=false, change_amount=75.00, update_time=2018-10-21T19:06:51, buyer_mobile=, update_user=1090, yn=true, ticket_no=POS17101, gross_profit_amount=7.65, wechat_pay=0.00, id=1351811, seller_id=1090, organSign=ZHL00000438, medicare_pay=0.00, before_cut_amount=0.00, union_pay=0.00, extract_status=0, create_time=2018-10-21T19:06:51, bast_yn=false, cash_pay=100.00, other_pay=0.00, member_actual_amount=0.00, gather_amount=100.00, cost_amount=17.35, buyer_idcard=, guid=42883cc7-e770-4e5b-8a40-bb959c7bb006, stored_value_pay=0.00, ali_pay=0.00, create_user=1090, status=0}";
        Map<String,Object> map = (Map<String,Object>)JSONObject.parse(json);
        System.out.println(elasticsearchWriteApi.updateData(map,"1351811","saas_order_info","saas_order_info"));
    }

    /**
     * @Description  删除数据
     * @return void
     * <AUTHOR>
     * @Date 2019/4/1
     **/
    @Test
    public void deleteData() throws Exception{
        System.out.println(elasticsearchWriteApi.deleteData("1351811","saas_order_info","saas_order_info"));
    }


    /**
     * @Description  按查询条件删除数据
     * @return void
     * <AUTHOR>
     * @Date 2019/4/1
     **/
    @Test
    public void deleteByQuery() throws Exception{
        ESQueryBuilderConstructor constructor = new ESQueryBuilderConstructor();
        ESQueryBuilders esQueryBuilders = new ESQueryBuilders();
        esQueryBuilders.term("organSign.keyword", "ZHL00000438");
        constructor.must(esQueryBuilders);
        constructor.setSize(10);
        constructor.setFrom(0);
        System.out.println(elasticsearchWriteApi.deleteByQuery(constructor,"saas_order_info","saas_order_info"));
    }
    @Test
    public void esProductReportQuery(){
        ProductReportQueryDto dto =new ProductReportQueryDto();
        dto.setStartTime("2019-07-22 00:00:00");
        dto.setEndTime("2019-08-21 23:59:59");
        //dto.setStandardLibraryId(39170);
        dto.setPageNum(1);
        dto.setPageSize(10);
        //Page page = eSorderReportApi.ybmLackProductAnaysis(dto, "saas_summary_statistics", "saas_summary_statistics");
        Page page =eSorderReportApi.ybmHighPriceAnaysis(dto, "saas_summary_statistics", "saas_summary_statistics");
        System.out.println("成功");
    }


    @Test
    public void testGetOrderDetailByPromotionRef(){
        /*Map<String, Object> result = orderElasticsearchInfoApi.sumOrderDetailByPromotionRef("NCX374",3);
        System.err.println(JSONObject.toJSONString(result));*/
    }

    @Test
    public void countOrganOrderProduct(){
    }

    @Test
    public void queryMedicalOrderDetailNew(){
        OrderSearchDisplayDto paramDto = new OrderSearchDisplayDto();
        paramDto.setOrganSign("ZHL00000272");
        paramDto.setStartDate("2020-01-01 00:00:00");
        paramDto.setEndDate("2020-12-30 23:59:59");
        paramDto.setPageIndex(1);
        paramDto.setSize(200);
        long a = System.currentTimeMillis();
        Page result = esOrderDetailApi.queryMedicalOrderDetailNew(paramDto);
        long b = System.currentTimeMillis();
        System.out.println(b-a);
        System.out.println(JSONObject.toJSONString(result));
    }
    /*@Autowired
    private ESNewOrderIndexReportApi esNewOrderIndexReportApi;*/
    @Reference(version = "0.0.1")
    private ESNewOrderIndexReportApi esNewOrderIndexReportApi;
    @Test
    public void updateOrderStatus(){
        System.out.println("456");
        for(int i = 0 ;i<=100;i++){
            System.out.println("12345677");
            new Thread(new Runnable(){
                @Override
                public void run(){
                    System.out.println("888888888888");
                    Map orderMap =   new HashMap();
                    orderMap.put("id",2535561);
                    orderMap.put("detailId","3270143,3270143,3270143");
                    orderMap.put("organSign","ZHL00004161");
                    orderMap.put("extractStatus", 0);
                    orderMap.put("headquartersOrganSign","ZHL00004165");
                    esNewOrderIndexReportApi.updateOrderInfoByHeadquarters(orderMap);
                    System.out.println("789");
                }
            }).start();
        }
    }

    @Test
    public void baseinfo(){
        List<ProductBaseinfoDto> baseinfoDtos = new ArrayList<>();
        ProductBaseinfoDto productBaseinfoDto = new ProductBaseinfoDto();
        productBaseinfoDto.setApprovalNumber("test");
        productBaseinfoDto.setAttributeSpecification("10*20ml");
        productBaseinfoDto.setId(1L);
        productBaseinfoDto.setPref("ZHL10081");
        productBaseinfoDto.setYn("1");
        productBaseinfoDto.setUsed("1");
        productBaseinfoDto.setOrganSign("ZHL556");
        productBaseinfoDto.setProductName("test");
        productBaseinfoDto.setBarcode("69005");
        productBaseinfoDto.setManufacturer("test");
        productBaseinfoDto.setIsHidden("1");
        productBaseinfoDto.setStatus("1");
        baseinfoDtos.add(productBaseinfoDto);
        esProductBaseinfoApi.upsert(baseinfoDtos);
    }

    @Test
    public void matchBaseinfo(){
        try {
            ProductBaseinfoParamDto paramDto = new ProductBaseinfoParamDto();
            paramDto.setIndex(1);
            paramDto.setProductName("盐酸西替利嗪片(特力)");
            paramDto.setManufacturer("成都恒瑞制药有限公司");
            paramDto.setSpecifications("10mg6");
            List<ProductBaseinfoDto> dtos = esProductBaseinfoApi.queryBaseinfo(paramDto, "ZHL00002198");
            System.out.println(JSON.toJSONString(dtos));
        }catch (Exception e){
            System.out.println(e.getMessage());
        }
    }

}
