package com.xyy.saas.elasticsearch.core;

import com.alibaba.fastjson.JSON;
import com.xyy.saas.elasticsearc.core.api.ESProductStandardInfoApi;
import com.xyy.saas.elasticsearc.core.dto.ProductStandardInfoDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/13
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class ESProductStandardInfoTest {
    @Autowired
    private ESProductStandardInfoApi esProductStandardInfoApi;

    @Test
    public void upsertTest(){
        List<ProductStandardInfoDto> list = new ArrayList<>();
        ProductStandardInfoDto dto = new ProductStandardInfoDto();
        dto.setId(1L);
        dto.setProductId("1");
        dto.setDataSource("test");
        dto.setSmallPackageCode("123");
        dto.setGeneralName("test药品");
        dto.setManufacturerName("test制造商");
        dto.setApprovalNo("test批准文号");
        dto.setSpec("10ml*20只");
        list.add(dto);
        esProductStandardInfoApi.upsert(list);
    }

    @Test
    public void queryByBarCodeTest(){
//        String barCode = "123";
        String barCode = "10ml*20瓶";
        List<ProductStandardInfoDto> list = esProductStandardInfoApi.queryByBarCode(barCode);
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void queryByApprovalNoAndSpecTest(){
//        String approvalNo = "test批准文号";
//        String specification = "10ml*20只";
        String approvalNo = "test批准文号";
        String specification = "10ml*20瓶*100包";
        List<ProductStandardInfoDto> list = esProductStandardInfoApi.queryByApprovalNoAndSpec(approvalNo, specification);
        System.out.println(JSON.toJSONString(list));
    }

    @Test
    public void queryByNameAndSpecTest(){
        String commonName = "test药品";
        String manufacturerName = "test制造";
        String specification = "10ml*20瓶*100包";
        List<ProductStandardInfoDto> list = esProductStandardInfoApi.queryByNameAndSpec(commonName, manufacturerName, specification);
        System.out.println(JSON.toJSONString(list));
    }
}
