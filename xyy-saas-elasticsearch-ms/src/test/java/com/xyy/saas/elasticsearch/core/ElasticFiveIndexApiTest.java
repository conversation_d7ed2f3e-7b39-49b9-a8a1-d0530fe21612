package com.xyy.saas.elasticsearch.core;

import com.alibaba.fastjson.JSON;
import com.xyy.saas.elasticsearc.core.api.ElasticFiveIndexApi;
import com.xyy.saas.elasticsearc.core.dto.buss.ElasticFiveOrderDetailQueryDto;
import com.xyy.saas.elasticsearch.core.service.ElasticFiveOrderDetailService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/24 17:27
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("test3")
public class ElasticFiveIndexApiTest {

    @Autowired
    private ElasticFiveIndexApi elasticFiveIndexApi;


    @Autowired
    private ElasticFiveOrderDetailService elasticFiveOrderDetailService;



    /**
     * 初始化索引
     * */
    @Test
    public void createOrderInfoIndex() throws Exception{
        Boolean result = elasticFiveIndexApi.createOrderInfoShardingIndex(null);
        System.out.println(result);
    }


    @Test
    public void getUnitProductSalesQuery() throws Exception{
        ElasticFiveOrderDetailQueryDto queryDto = JSON.parseObject("{\"endDate\":\"2023-04-26 23:59:59\",\"indexQueryTypeEnum\":\"QUERY\",\"indexResultFieldEnum\":\"QUERY_UNIT_PRODUCT_SALES_RESULT\",\"memberQuery\":true,\"organSign\":\"ZHL00005433\",\"productCodes\":[\"ZHL20652112\",\"ZHL20625867\",\"ZHL20381892CL1\",\"ZHL20861411\",\"ZHL20381892CL\",\"ZHL20382950\",\"ZHL20652278\",\"ZHL20652234\",\"ZHL20381892\",\"ZHL20652365\"],\"startDate\":\"2023-01-01\"}", ElasticFiveOrderDetailQueryDto.class);

        elasticFiveOrderDetailService.getUnitProductSalesQuery(queryDto);
    }





}
