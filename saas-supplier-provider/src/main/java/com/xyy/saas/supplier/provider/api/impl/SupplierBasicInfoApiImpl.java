package com.xyy.saas.supplier.provider.api.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.taobao.api.ApiException;
import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.request.AlibabaAlihealthDrugKytSmyxGetentinfoRequest;
import com.taobao.api.response.AlibabaAlihealthDrugKytSmyxGetentinfoResponse;
import com.xyy.common.module.api.SysConfigApi;
import com.xyy.common.module.dto.SystemConfigDto;
import com.xyy.saas.common.util.BeanUtil;
import com.xyy.saas.common.util.JSONUtils;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.dynamic.config.api.pojo.param.GlobalConfigParam;
import com.xyy.saas.purchase.core.api.PurchaseApi;
import com.xyy.saas.purchase.core.dto.SaasPurchaseBillInfoPoDto;
import com.xyy.saas.supplier.api.SupplierBasicInfoApi;
import com.xyy.saas.supplier.dto.*;
import com.xyy.saas.supplier.provider.config.redis.RedisManager;
import com.xyy.saas.supplier.provider.constans.DictConstant;
import com.xyy.saas.supplier.provider.constans.XyyProvinceProviderInfoConstans;
import com.xyy.saas.supplier.provider.enums.DrugstoreTypeProductEnum;
import com.xyy.saas.supplier.provider.po.*;
import com.xyy.saas.supplier.provider.serivce.*;
import com.xyy.saas.supplier.provider.util.CollateDataUtil;
import com.xyy.saas.supplier.provider.util.DateUtil;
import com.xyy.saas.supplier.provider.util.DrugstoreTypeUtil;
import com.xyy.saas.supplier.provider.util.OptUtils;
import com.xyy.saas.supplier.provider.vo.ProviderBaseinfoVo;
import com.xyy.saas.supplier.provider.vo.ProviderExtVo;
import com.xyy.saas.supplier.provider.vo.XyyProviderInfoVo;
import com.xyy.saas.trace.api.TraceCodeProviderBindApi;
import com.xyy.saas.trace.dto.TraceCodeProviderBindDto;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.api.EmployeeApi;
import com.xyy.user.module.dto.EmployeeDetailModel;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.dto.result.EmployeeDto;
import com.xyy.user.module.dto.result.QueryDrugstoreDto;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import com.xyy.user.module.enums.DrugstoreTypeEnum;
import net.sf.json.JSONArray;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.xyy.saas.supplier.provider.util.OptUtils.isEmpty;
import static com.xyy.saas.supplier.provider.util.OptUtils.notEmpty;

@Service(version = "0.0.1")
public class SupplierBasicInfoApiImpl implements SupplierBasicInfoApi {

    private static final Logger logger = LoggerFactory.getLogger(SupplierBasicInfoApiImpl.class);

    @Autowired
    private ProviderInfoService providerInfoService;

    @Autowired
    private ProviderSalesService providerSalesService;

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;

    @Reference(version = "0.0.1")
    private SysConfigApi sysConfigApi;

    @Autowired
    private RedisManager redis;

    @Autowired
    private XYYApproveRecordService xyyApproveRecordService;

    @Autowired
    private UploadFileService uploadFileService;

    @Reference(version = "0.0.1")
    private PurchaseApi purchaseApi;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Reference(version = "0.0.1")
    private TraceCodeProviderBindApi traceCodeProviderBindApi;


    //taobao码上放心平台，Apollo配置注入
    @Value("${thirdurl}")
    private String serverUrl; //"http://gw.api.taobao.com/router/rest";
    @Value("${appKey}")
    private String appKey; //"31026904";
    @Value("${appSecret}")
    private String appSecret; //"59a758df4ff1204bd4394a0d38513054";
    @Value("${msfx_organsigns}")
    private String msfxOrganSigns;
    @Value("${supplierforbidConfigCode:CG10003}")
    private String supplierforbidConfigCode;
    @Autowired
    CommonBusinessService commonBusinessService;


    @Override
    public ProviderPrefDto providerSaveBaseInfoChain(ProviderVoDto providerDto, Byte bizModel) {
        logger.info("providerSaveBaseInfoChain 入参：providerDto:{},bizModel:{}",providerDto,bizModel);
        //只对新增供应商进行SKU的唯一性判断
        ProviderPrefDto providerPrefDto = new ProviderPrefDto();
        if(providerDto.getId() == null){
            List<String> phaprefs = this.queryProvidersBySkuCcheck(providerDto.getOrganSign(),providerDto.getProviderName(),providerDto.getBusinessLicense(),providerDto.getProviderType());
            if(phaprefs != null && phaprefs.size() > 0){
                logger.error("新增供应商已经存在");
                providerPrefDto.setStatus(-4);
                providerPrefDto.setPharmacyPref(phaprefs.get(0));
                return providerPrefDto;
            }
        }else{ //修改供应商名称且没有开启码上标识则清空数据库中供应商的码上标识  add by zhuzc
            ProviderBaseinfoPo providerBaseinfoPo = providerInfoService.getProviderById(providerDto.getId());
            String newProviderName = providerDto.getProviderName(); //新的供应商名称
            //新加判断, 由于小药药供应商不能修改名称,当是小药药供应商时,不用置空码上放心标识
            if(providerBaseinfoPo != null && notEmpty(newProviderName) && !newProviderName.equals(providerBaseinfoPo.getProviderName())&&!"XYY".equals(providerBaseinfoPo.getPref())) {
                SystemConfigDto systemConfigDto = sysConfigApi.querySystemConfig(providerBaseinfoPo.getOrganSign());
                if(systemConfigDto == null) { //即未开启
                    providerDto.setMsfxRefEntId(""); //清空数据库码上标识
                    providerDto.setMsfxEntId("");
                    logger.info("机构系统配置为空,清空数据库码上标识ID={}", providerDto.getId());
                }else{
                    //待对接获取码上放心开关标识接口
                    Byte productTraceUploadYn = systemConfigDto.getProductTraceUploadYn(); //0未启用  1启用
                    if(productTraceUploadYn == null || productTraceUploadYn != 1) { //未开启
                        providerDto.setMsfxRefEntId(""); //清空数据库码上标识
                        providerDto.setMsfxEntId("");
                        logger.info("清空数据库码上标识ID={}", providerDto.getId());
                    }
                }
            }
        }
        ProviderBaseinfoVo provider = new ProviderBaseinfoVo();
        BeanUtils.copyProperties(providerDto, provider);
        if (!CollectionUtils.isEmpty(providerDto.getProviderSalesDtos())) {
            List<ProviderSalesPo> providerSalesPos = CollateDataUtil.copyListProperties(providerDto.getProviderSalesDtos(), ProviderSalesPo.class);
            provider.setProviderSalesPos(providerSalesPos);
        }
        if (!CollectionUtils.isEmpty(providerDto.getQualificationInfos())) {
            List<QualificationInfoPo> qualificationInfoPos = CollateDataUtil.copyListProperties(providerDto.getQualificationInfos(), QualificationInfoPo.class);
            provider.setQualificationList(qualificationInfoPos);
        }
        ProviderExtVoDto extDto = providerDto.getProviderExtVo();
        ProviderExtVo vo = new ProviderExtVo();
        BeanUtils.copyProperties(extDto, vo);
        provider.setProviderExtVo(vo);
        List<ImgUrlVo> voList = new ArrayList<ImgUrlVo>();
        List<ImgUrlDto> dtoList = providerDto.getImgUrls();
        if (!CollectionUtils.isEmpty(dtoList)) {
            for (ImgUrlDto imgUrlDto : dtoList) {
                if (null != imgUrlDto  ) {
                    if (!StringUtils.isEmpty(imgUrlDto.getImgUrl())) {
                        String url = imgUrlDto.getImgUrl();
                        String[] urls = url.split(",");
                        for (String str : urls) {
                            ImgUrlVo voImg = new ImgUrlVo();
                            BeanUtils.copyProperties(imgUrlDto, voImg);
                            voImg.setImgUrl(str);
                            voList.add(voImg);
                        }
                    }
                }
            }
        }
        provider.setImgUrls(voList);
        providerPrefDto  = providerInfoService.addOrUpdate(provider,bizModel);
        if(providerPrefDto.getStatus()>0 && providerDto.getId()!=null){
            //更新供应商信息时调用码上放心平台接口
            TraceCodeProviderBindDto traceCodeProviderBindDto = new TraceCodeProviderBindDto();
            traceCodeProviderBindDto.setProvidePref(providerDto.getPref());
            traceCodeProviderBindDto.setOrganSign(providerDto.getOrganSign());
            traceCodeProviderBindDto.setUpdateUser(providerDto.getCreateUser());
            traceCodeProviderBindDto.setMsfxEntId(providerDto.getMsfxEntId());
            traceCodeProviderBindDto.setMsfxRefEntId(providerDto.getMsfxRefEntId());
            ResultVO resultVO = traceCodeProviderBindApi.providerBindChange(traceCodeProviderBindDto);
            logger.info("pref:{},更新供应商信息时调用码上放心平台接口返回信息:{}",providerDto.getPref(),JSON.toJSONString(resultVO));
        }
        return providerPrefDto;
    }

    @Override
    public ProviderPrefDto providerSaveBaseInfo(ProviderVoDto providerDto) {
        return providerSaveBaseInfoChain(providerDto,(byte) DrugstoreBizModelEnum.DRUGSTORE.getKey());
    }


    @Override
    public int saveBaseInfo(ProviderVoDto providerDto) {
        logger.info("saveBaseInfo providerDto(maybe from gsp审批)= {}", JSONUtils.obj2JSON(providerDto));
        return this.providerSaveBaseInfo(providerDto).getStatus();
    }

    @Override
    public List<ProviderDto> syncData(Map<String, Object> paramMap) {
        logger.info("syncData 开始调用供应商的同步接口,入参：{}",paramMap);
        String organSign = (String) paramMap.get("organSign");
        //根据机构号获取机构信息
        SaaSDrugstoreDto saaSDrugstoreDto = drugstoreApi.getDrugstoreByOrganSign(organSign);
        if(null != saaSDrugstoreDto && DrugstoreBizModelEnum.CHAIN_STORE.toEquals(saaSDrugstoreDto.getBizModel())){
            organSign = saaSDrugstoreDto.getHeadquartersOrganSign();
            paramMap.put("organSign",organSign);
        }
        List<ProviderBaseinfoPo> providerBaseinfoPos = providerInfoService.syncData(paramMap);
        if (!CollectionUtils.isEmpty(providerBaseinfoPos)) {
            providerBaseinfoPos.parallelStream().forEach(providerBaseinfoPo -> {
                if (providerBaseinfoPo.getTreeInOneType()==null) {
                    providerBaseinfoPo.setTreeInOneType((byte)0);
                }
            });
        }
        return BeanUtil.copyListProperties(providerBaseinfoPos, ProviderDto.class);
    }

    @Override
    public Long getProviderIdByPref(String pref, String organSign) {
        return providerInfoService.getProviderIdByPref(pref, organSign);
    }

    @Override
    public ProviderVoDto getProviderInfoById(Long id, String organSign) {
        logger.info("getProviderInfoById 查询入参：id：{}，organSign：{}",id,organSign);
        ProviderBaseinfoVo vo = providerInfoService.getProviderInfoById(id, organSign);
        ProviderVoDto dto = singleProviderVoToDto(vo);
        logger.info("getProviderInfoById 查询结果：ProviderVoDto：{}",dto);
        return dto;
    }

    @Override
    public ProviderVoDto getProviderById(Long id) {
        logger.info("getProviderById 查询入参：id：{}",id);
        ProviderBaseinfoVo vo = providerInfoService.getProviderById(id);
        ProviderVoDto dto = singleProviderVoToDto(vo);
        logger.info("getProviderById 查询结果：ProviderVoDto：{}",dto);
        return dto;
    }

    @Override
    public int deleteProviderInfoById(Long id, String organSign) {
        return providerInfoService.deleteProviderInfoById(id, organSign);
    }

    @Override
    public int resetProviderIsUsed(Long id, String organSign, Byte used) {
        return providerInfoService.resetProviderIsUsed(id, organSign, used);
    }

    @Override
    public PageInfo queryProvider(PageInfo pageInfo, String name, String organSign) {
        logger.info("queryProvider(PageInfo pageInfo, String name, String organSign) 查询入参：name:{},organSign:{}",name,organSign);
        PageInfo result = providerInfoService.queryProvider(pageInfo, name, organSign);
        List<ProviderBaseinfoPo> list = result.getList();
        List<ProviderDto> dtos = batchProviderPoToDto(list);
        result.setList(dtos);
        return result;
    }

    @Override
    public PageInfo queryProvider(PageInfo pageInfo, String name, Byte used, String organSign) {
        PageInfo result = providerInfoService.queryProvider(pageInfo, name, used, organSign);
        List<ProviderBaseinfoPo> list = result.getList();
        List<ProviderDto> dtos = batchProviderPoToDto(list);
        result.setList(dtos);
        return result;
    }

    @Override
    public PageInfo queryProviderAndShadow(PageInfo pageInfo, String name, Byte used, String organSign) {
        PageInfo result = providerInfoService.queryProvider(pageInfo, name, used, organSign);
        List<ProviderBaseinfoPo> list = result.getList();
        //添加一个影子
        ProviderBaseinfoPo shadowProvider = providerInfoService.queryShadow(name, used, organSign);
        if (!Objects.isNull(shadowProvider)){
            if (CollectionUtils.isEmpty(list)){
                list = new ArrayList<>();
            }
            list.add(0,shadowProvider);
        }
        List<ProviderDto> dtos = batchProviderPoToDto(list);
        result.setList(dtos);
        return result;
    }

    @Override
    public ResultVO findXyySupplier(String organSign){
        ProviderBaseinfoPo pbo = new ProviderBaseinfoPo();
        pbo.setOrganSign(organSign);
        ProviderBaseinfoPo xyySupplier = providerInfoService.findXyySupplier(pbo);
        return ResultVO.createSuccess(xyySupplier);
    }

    @Override
    public PageInfo queryProviderByParams(PageInfo pageInfo, String name, Byte used, Byte status, String organSign) {
        PageInfo result = providerInfoService.queryProvider(pageInfo, name, used, status,organSign);
        List<ProviderBaseinfoPo> list = result.getList();
        //添加一个影子
        ProviderBaseinfoPo shadowProvider = providerInfoService.queryShadow(name, used, organSign);
        if (!Objects.isNull(shadowProvider)){
            if (CollectionUtils.isEmpty(list)){
                list = new ArrayList<>();
            }
            list.add(0,shadowProvider);
        }
        List<ProviderDto> dtos = batchProviderPoToDto(list);
        if(!CollectionUtils.isEmpty(dtos)){
            dtos = dtos.parallelStream().filter(item -> DictConstant.CHECKED_PRODUCT_NO.equals(item.getStatus())).collect(Collectors.toList());
        }
        result.setList(dtos);
        return result;
    }

    @Override
    public ResultVO testQueryProviderNew(String name, Byte used, Byte status, String organSign) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(1);
        pageInfo.setPages(10);
        PageInfo result =this.queryProviderByParams(pageInfo,name,used,status,organSign);
        return ResultVO.createSuccess(result);
    }

    @Override
    public ResultVO<ProviderDto> getProviderInfoOrganSignAndPref(String organSign,String pref) {
        ProviderDto providerDto = new ProviderDto();
        ProviderBaseinfoPo baseinfoPo = providerInfoService.getProviderInfoOrganSignAndPref(organSign,pref);
        if (null != baseinfoPo) {
            BeanUtils.copyProperties(baseinfoPo, providerDto);
        }
        return ResultVO.createSuccess(providerDto);
    }



    @Override
    public PageInfo queryProviderNew(PageInfo pageInfo, String name, Byte used, String organSign, String startTime, String endTime) {
        ProviderBaseinfoPo pbo = new ProviderBaseinfoPo();
        pbo.setMnemonicCode(name);
        pbo.setUsed(used);
        pbo.setOrganSign(organSign);
        pbo.setStartTime(startTime);
        pbo.setEndTime(endTime);
        PageInfo result = providerInfoService.queryProviderNew(pageInfo, pbo);
        List<ProviderBaseinfoPo> list = result.getList();
        List<ProviderDto> dtos = batchProviderPoToDto(list);
        result.setList(dtos);
        return result;
    }

    @Override
    public boolean verifyProvider(String name, Byte used, String organSign) {
        logger.info("SupplierBasicInfoApiImpl->verifyProvider() param: name:{},organSign:{}",name,organSign);
        return providerInfoService.verifyProvider(name, used, organSign);
    }

    /**
     * 0190兼容效验供应商
     * @param name
     * @param pref
     * @param organSign
     * @return
     */
    @Override
    public Integer verifyProviderNew(String name, String pref, String organSign) {
        logger.info("SupplierBasicInfoApiImpl->verifyProvider() param: name:{},pref:{},organSign:{}",name,pref,organSign);
        return providerInfoService.verifyProviderNew(name, pref, organSign);
    }

    @Override
    public PageInfo queryProviderSort(PageInfo pageInfo, String name, Byte used, String organSign, String startTime, String endTime, String sidx, String sord) {
        ProviderBaseinfoPo pbo = new ProviderBaseinfoPo();
        pbo.setMnemonicCode(name);
        pbo.setUsed(used);
        pbo.setOrganSign(organSign);
        pbo.setStartTime(startTime);
        pbo.setEndTime(endTime);
        pbo.setSidx(sidx);
        pbo.setSord(sord);
        PageInfo result = providerInfoService.queryProviderNew(pageInfo, pbo);
        List<ProviderBaseinfoPo> list = result.getList();
        List<ProviderDto> dtos = batchProviderPoToDto(list);
        result.setList(dtos);
        return result;
    }

    @Override
    public PageInfo<ProviderDto> queryProviderSort(PageInfo pageInfo, ProviderQueryDto providerQueryDto, String organSign){
        logger.info("查询供应商列表入参：organSign:{},dto:{} page:{}",organSign,JSON.toJSONString(providerQueryDto),JSON.toJSONString(pageInfo));
        PageInfo<ProviderDto> result = new PageInfo<>();
        result.setList(new ArrayList<>());
        ProviderBaseinfoPo pbo = new ProviderBaseinfoPo();
        BeanUtils.copyProperties(providerQueryDto, pbo);
        //兼用影子供应商
        if(!StringUtils.isEmpty(providerQueryDto.getName()) && providerQueryDto.getName().contains("小药药")){
            providerQueryDto.setName("小药药");
        }
        pbo.setMnemonicCode(providerQueryDto.getName());
        pbo.setOrganSign(organSign);
        PageInfo<ProviderBaseinfoPo> providerBaseinfoPoPageInfo = providerInfoService.queryProviderNew(pageInfo, pbo);
        if(providerBaseinfoPoPageInfo==null){
            result.setPageSize(result.getPageSize()==0?50:result.getPageSize());
            return result;
        }
        List<ProviderBaseinfoPo> list = providerBaseinfoPoPageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            result.setPageSize(result.getPageSize()==0?50:result.getPageSize());
            return result;
        }
        List<ProviderDto> dtos = batchProviderPoToDto(list);
        if (!CollectionUtils.isEmpty(dtos)) {
            for(ProviderDto  dto:dtos){
                if(dto.getExpireYn()==1){
                    dto.setExpireYnStr("是");
                }
                if(dto.getExpireYn()==0){
                    dto.setExpireYnStr("否");
                }
                if(dto.getRelateYn()==1){
                    dto.setRelateYnStr("是");
                }
                if(dto.getRelateYn()==0){
                    dto.setRelateYnStr("否");
                }
            }
        }

        String supplierNo = getProductForbidConfig(organSign);
        if (!StringUtils.isEmpty(supplierNo)){
            for (ProviderDto dto : dtos){
                if (supplierNo.equals(dto.getPharmacyPref())){
                    dto.setProductForbidTag(1);
                }
            }
        }

        BeanUtils.copyProperties(providerBaseinfoPoPageInfo,result);
        result.setList(dtos);
        return result;
    }

    private String getProductForbidConfig(String organSign) {
        List<GlobalConfigParam> globalConfigParams = Lists.newArrayList();
        GlobalConfigParam supplierForbidProductConfig = new GlobalConfigParam();
        supplierForbidProductConfig.setOrganSign(organSign);
        supplierForbidProductConfig.setConfigCode(supplierforbidConfigCode);
        globalConfigParams.add(supplierForbidProductConfig);
        String supplierNo = "";
        ResultVO<Map<String, Map<String, String>>> globalConfig = commonBusinessService.getGlobalConfig(globalConfigParams);
        if (globalConfig.getCode() == 0){
            Map<String, Map<String, String>> configMap = globalConfig.getResult();
            if (MapUtils.isNotEmpty(configMap)){
                Map<String, String> organSignMap = configMap.get(organSign);
                supplierNo = organSignMap.get(supplierforbidConfigCode);
            }
        }
        return supplierNo;
    }

    /** 对外提供的供应商查询接口 曼迪新 */
    @Override
    public PageInfo<ProviderDto> queryProviderSortNEW(ProviderQueryDto queryDto) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(queryDto.getPageNum());
        pageInfo.setPageSize(queryDto.getPageSize());

        ProviderBaseinfoPo pbo = new ProviderBaseinfoPo();
        pbo.setOrganSign(queryDto.getOrganSign());
        PageInfo<ProviderBaseinfoPo> providerBaseinfoPoPageInfo = providerInfoService.queryProviderNew(pageInfo, pbo);

        PageInfo<ProviderDto> result = new PageInfo<>();
        BeanUtils.copyProperties(providerBaseinfoPoPageInfo,result);

        List<ProviderBaseinfoPo> list = providerBaseinfoPoPageInfo.getList();
        List<ProviderDto> dtos = batchProviderPoToDto(list);
        result.setList(dtos);
        return result;
    }

    @Override
    public PageInfo queryProviderForApprove(PageInfo pageInfo, String name, String organSign) {
        PageInfo result = providerInfoService.queryProviderForApprove(pageInfo, name, organSign);
        List<ProviderBaseinfoVo> list = result.getList();
        List<ProviderVoDto> dtos = batchProviderVoToVoDto(list);
        result.setList(dtos);
        return result;
    }

    @Override
    public List<ProviderVoDto> queryProviderByParams(String name, String ids, String organSign) {
        String queryOrganSign = judgeDrugType(organSign);
        List<ProviderBaseinfoVo> list = providerInfoService.queryProviderByParams(name, ids, queryOrganSign);
        List<ProviderVoDto> dtos = batchProviderVoToVoDto(list);
        return dtos;
    }

    @Override
    public List<ProviderVoDto> queryProviderByParams(String name, String ids, Byte used, String organSign) {
        String queryOrganSign = judgeDrugType(organSign);
        List<ProviderBaseinfoVo> list = providerInfoService.queryProviderByParams(name, ids, used, queryOrganSign);
        List<ProviderVoDto> dtos = batchProviderVoToVoDto(list);
        return dtos;
    }

    @Override
    public List<ProviderVoDto> queryProviderByParamsNew(String name, Byte used, String organSign, String startTime, String endTime) {
        ProviderBaseinfoPo pbo = new ProviderBaseinfoPo();
        pbo.setMnemonicCode(name);
        pbo.setUsed(used);
        pbo.setOrganSign(organSign);
        pbo.setStartTime(startTime);
        pbo.setEndTime(endTime);
        List<ProviderVoDto> list = providerInfoService.queryProviderByParamsNew(pbo);
        List<Integer> ids = new ArrayList<>();
        if (list != null && list.size() > 0) {
            for (ProviderVoDto pvd : list) {
                ids.add(Integer.valueOf(pvd.getCreateUser()));
            }
            EmployeeDetailModel edm = new EmployeeDetailModel();
            edm.setOrganSign(organSign);
            edm.setEmployeeIds(ids);
            List<EmployeeDto> employeeDtos = employeeApi.getEmployeeDetailByIds(edm).getResult();
            Map<String, EmployeeDto> employeeDtoMap = new HashMap<>();
            if (employeeDtos != null && employeeDtos.size() > 0) {
                for (EmployeeDto edto : employeeDtos) {
                    employeeDtoMap.put(edto.getId() + "", edto);
                }
            }
            for (ProviderVoDto pvd : list) {
                pvd.setCreateUserName(employeeDtoMap.get(pvd.getCreateUser()) == null ? pvd.getCreateUser() : employeeDtoMap.get(pvd.getCreateUser()).getName());
            }
        }
        return list;
    }

    @Override
    public ProviderVoDto excelProviderInfoById(Long id, String organSign) {
        //根据机构号获取机构信息
        SaaSDrugstoreDto saaSDrugstoreDto = drugstoreApi.getDrugstoreByOrganSign(organSign);
        if(null != saaSDrugstoreDto){
            if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(saaSDrugstoreDto.getBizModel()) && DrugstoreTypeEnum.DRUGSTORE.toEquals(saaSDrugstoreDto.getOrganSignType())){
                organSign = saaSDrugstoreDto.getHeadquartersOrganSign();
            }
        }
        ProviderBaseinfoVo vo = providerInfoService.excelProviderInfoById(id, organSign);
        ProviderVoDto dto = singleProviderVoToDto(vo);

        return dto;
    }

    @Override
    public Map<String, Object> batchInsertProviders(List<List<String>> list, String username, String organSign) {
        return providerInfoService.batchInsertProviders(list, username, organSign);
    }

    @Override
    public List<ProviderDto> queryProviderByPrefOrName(String pref, String name, String organSign) {
        organSign = judgeDrugType(organSign);
        List<ProviderBaseinfoPo> list = providerInfoService.queryProviderByPrefAndName(pref, name, organSign);
        if (list != null && list.size() > 0) {
            for (ProviderBaseinfoPo po : list) {
                if ("XYY".equals(po.getPref()) && "XYY".equals(po.getOrganSign())) {
                    XYYApproveRecordPo xyyMess = xyyApproveRecordService.getXyyMessForOrganSign(po.getPref(), organSign);
                    if (xyyMess != null) {
                        XyyProviderInfoVo xyyVo = XyyProvinceProviderInfoConstans.getXyyProviderInfoVo(xyyMess.getProvince());
                        if (xyyVo != null) {
                            providerInfoService.copyProviderXyyVoToPo(xyyVo, po);
                        }
                    }
                }
            }
        }
        List<ProviderDto> dtos = batchProviderPoToDto(list);
        return dtos;
    }

    @Override
    public List<String> getAllProviderPref() {
        return providerInfoService.getAllProviderPref();
    }

    @Override
    public List<String> getAllProviderPrefByParams(String organSign, String name) {
        return providerInfoService.getAllProviderPrefByParams(organSign, name);
    }

    @Override
    public List<ProviderDto> getProviderByPrefsAndOrganSign(List<String> prefs, String organSign) {
        if (StringUtils.isEmpty(organSign)) {
            return new ArrayList<>();
        }
        String queryOrganSign = judgeDrugType(organSign);
        if (!CollectionUtils.isEmpty(prefs)) {
            prefs = prefs.parallelStream().distinct().filter(pref->!StringUtils.isEmpty(pref)&&!"null".equalsIgnoreCase(pref)).collect(Collectors.toList());
        }
        List<ProviderBaseinfoPo> list = providerInfoService.getProviderByPrefsAndOrganSign(prefs, queryOrganSign);
        logger.info("机构编号：{},getProviderByPrefsAndOrganSign 方法入参：{}",organSign,prefs);
        if (list != null && list.size() > 0) {
            for (ProviderBaseinfoPo po : list) {
                if ("XYY".equals(po.getPref())) {
                    XYYApproveRecordPo xyyMess = xyyApproveRecordService.getXyyMessForOrganSign(po.getPref(), queryOrganSign);
                    if (xyyMess != null) {

                        if (providerInfoService.needNewXyySupplier(queryOrganSign)) {
                            ProviderBaseinfoPo providerBaseinfoPo = providerInfoService.findNewXyySupplier(queryOrganSign);
                            logger.info("同步供应商信息 ProviderDtoListAAA :" + JSONObject.toJSONString(providerBaseinfoPo));
                            providerInfoService.copyProviderPoToPo(providerBaseinfoPo, po);
                        } else {
                            XyyProviderInfoVo xyyVo = XyyProvinceProviderInfoConstans.getXyyProviderInfoVo(xyyMess.getProvince());
                            if (xyyVo != null) {
                                providerInfoService.copyProviderXyyVoToPo(xyyVo, po);
                            }
                        }
                    }
                }
            }
        }
        List<ProviderDto> dtos = batchProviderPoToDto(list);
        return dtos;
    }

    @Override
    public List<ProviderDto> getProviderBaseInfoByPrefsAndOrganSign(List<String> prefs, String organSign) {
        if (StringUtils.isEmpty(organSign)) {
            return new ArrayList<>();
        }
        String queryOrganSign = judgeDrugType(organSign);
        if (!CollectionUtils.isEmpty(prefs)) {
            prefs = prefs.parallelStream().distinct().filter(pref->!StringUtils.isEmpty(pref)&&!"null".equalsIgnoreCase(pref)).collect(Collectors.toList());
        }
        List<ProviderBaseinfoPo> list = providerInfoService.getProviderByPrefsAndOrganSign(prefs, queryOrganSign);
        logger.info("机构编号：{},getProviderByPrefsAndOrganSign 方法入参：{}",organSign,prefs);
        if (list != null && list.size() > 0) {
            for (ProviderBaseinfoPo po : list) {
                if ("XYY".equals(po.getPref())) {
                    XYYApproveRecordPo xyyMess = xyyApproveRecordService.getXyyMessForOrganSign(po.getPref(), queryOrganSign);
                    if (xyyMess != null) {

                        if (providerInfoService.needNewXyySupplier(queryOrganSign)) {
                            ProviderBaseinfoPo providerBaseinfoPo = providerInfoService.findNewXyySupplier(queryOrganSign);
                            logger.info("同步供应商信息 ProviderDtoListAAA :" + JSONObject.toJSONString(providerBaseinfoPo));
                            //是否覆盖小药药备注信息
                            providerInfoService.copyProviderXyyPoToPo(providerBaseinfoPo, po);
                        } else {
                            XyyProviderInfoVo xyyVo = XyyProvinceProviderInfoConstans.getXyyProviderInfoVo(xyyMess.getProvince());
                            if (xyyVo != null) {
                                providerInfoService.copyProviderXyyVoToPo(xyyVo, po);
                            }
                        }
                    }
                }
            }
        }
        List<ProviderDto> dtos = batchProviderPoToDto(list);
        return dtos;
    }

    @Override
    public List<ProviderDto> getProviderByPharmacyPrefsAndOrganSign(List<String> pharmacyPrefs, String organSign) {
        if (StringUtils.isEmpty(organSign)) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(pharmacyPrefs)) {
            return new ArrayList<>();
        }
        List<ProviderBaseinfoPo> list = providerInfoService.getProviderByPharmacyPrefAndOrganSign(pharmacyPrefs, organSign);
        List<ProviderDto> dtos = batchProviderPoToDto(list);
        return dtos;
    }

    @Override
    public ProviderDto getProviderMessForPref(String pref, String organSign) {
        ProviderBaseinfoPo po = providerInfoService.getProviderMessForPref(pref, organSign);
        if (po != null) {
            XYYApproveRecordPo xyyMess = xyyApproveRecordService.getXyyMessForOrganSign(po.getPref(), organSign);
            if (xyyMess != null) {
                switch (xyyMess.getStatus()) {
                    case 1:
                        po.setStatus((byte) 2);
                        break;
                    case 2:
                        po.setStatus((byte) 2);
                        break;
                    case 3:
                        po.setStatus((byte) 3);
                        break;
                    case 4:
                        po.setStatus((byte) 1);
                        break;
                    case 5:
                        po.setStatus((byte) 3);
                        break;
                }
                XyyProviderInfoVo xyyVo = XyyProvinceProviderInfoConstans.getXyyProviderInfoVo(xyyMess.getProvince());
                if (xyyVo != null) {
                    providerInfoService.copyProviderXyyVoToPo(xyyVo, po);
                }
            }
        }
        ProviderDto dto = new ProviderDto();
        if (null != po) {
            BeanUtils.copyProperties(po, dto);
        }
        return dto;
    }

    @Override
    public String getPrefByPharmacyPrefAndOrganSign(String pharmacyPref, String organSign) {
        return providerInfoService.getPrefByPharmacyPrefAndOrganSign(pharmacyPref, organSign);
    }

    @Override
    public List<String> getAllProviderPrefByParamsForCloud(String name) {
        return providerInfoService.getAllProviderPrefByParams(null, name, "cloud");
    }

    /**
     * 检查是否开启了快捷审核 且是否有审核人
     *
     * @param organSign
     * @return -1：未开启快捷审核
     * -2：首营品种一审没有审核人信息
     * -3：首营品种二审没有审核人信息
     * -4：开启了自动审核
     * 0：开启了快捷审核且符合条件
     */
    @Override
    public JSONObject checkselfMotion(String organSign) {
        return providerInfoService.checkselfMotion(organSign);
    }

    @Override
    public String insertProviderData(List<ProviderDto> pbdtos, List<FirstCheckProviderDto> fcpdtos, List<ApproveHistoryDto> approveHistoryDtos, String organSign, String user, Long maxProviderNo) {
        //查询该机构下所有用户信息
        List<EmployeeDto> employeeDtos = employeeApi.queryAllEmployeeByOrganSign(organSign);
        Map<String, Integer> userInfo = new HashMap<>();
        employeeDtos.forEach(item -> userInfo.put(item.getLoginName(), item.getId())
        );
        //查询药店负责人的员工id
        Integer adminEmployeeId = 1;
        ResultVO<List<EmployeeDto>> result = employeeApi.queryEmployeeByRoleName("药店负责人", organSign);
        List<EmployeeDto> employeeDtosAdmin = result.getResult();
        if (employeeDtosAdmin != null && employeeDtosAdmin.size() > 0 && employeeDtosAdmin.get(0) != null) {
            adminEmployeeId = employeeDtosAdmin.get(0).getId();
            userInfo.put("admin", adminEmployeeId);
        }
        String back = providerInfoService.insertProviderData(pbdtos, fcpdtos, approveHistoryDtos, organSign, userInfo, maxProviderNo);
        return back;
    }

    @Override
    public void initXYYProviderApprove() {
        providerInfoService.initXYYProviderApprove();
    }

    @Override
    public void insertXYYProviderForNewAccount(String organSign, Integer id) {
        providerInfoService.insertXYYProviderForNewAccount(organSign, id);
    }

    @Override
    public void insertXYYProviderForNewAccount(String organSign, Integer id, String province) {
        logger.info("insertXYYProviderForNewAccount 入参 organSign:{},id:{},province:{}" ,organSign ,id , province);
        providerInfoService.insertXYYProviderForNewAccount(organSign, id, province);
    }

    @Override
    public ProviderDto syncDataForXYY(Map<String, Object> paramMap) {
        ProviderBaseinfoPo po = providerInfoService.syncDataForXYY(paramMap);
        ProviderDto dto = new ProviderDto();
        if (null != po) {
            BeanUtils.copyProperties(po, dto);
        }
        return dto;
    }

    /**
     * 根据药店标识和查询条件获取供应商编号  根据供应商名称精准查询
     *
     * @param organSign 药店标识
     * @param name      供应商名称
     * @param type      1 精准  2 模糊  3 不等于
     * @return
     */
    @Override
    public List<String> getAllProviderPrefForReport(String organSign, String name, Integer type) {
        return providerInfoService.getAllProviderPrefForReport(organSign, name, type);
    }

    @Override
    public void updateXYYApproveExtMess() {
        providerInfoService.updateXYYApproveExtMess();
    }

    @Override
    public Map<String, String> queryPharmacyPrefByOrganSign(String organSign) {
        String queryOrganSign = judgeDrugType(organSign);
        return providerInfoService.queryPharmacyPrefByOrganSign(queryOrganSign);
    }

    private List<ProviderDto> batchProviderPoToDto(List<ProviderBaseinfoPo> list) {
        List<ProviderDto> dtos = new ArrayList<>();
        if (null != list && list.size() > 0) {
            for (ProviderBaseinfoPo vo : list) {
                ProviderDto dto = new ProviderDto();
                BeanUtils.copyProperties(vo, dto);
                String remark = dto.getRemark();
                if(!StringUtils.isEmpty(remark)){
                    ProviderExtVo providerExtVo = JSONObject.toJavaObject(JSON.parseObject(remark), ProviderExtVo.class);
                    if(providerExtVo!=null){
                        ProviderExtVoDto providerExtVoDto = new ProviderExtVoDto();
                        BeanUtils.copyProperties(providerExtVo,providerExtVoDto);
                        dto.setProviderExtVoDto(providerExtVoDto);
                    }
                }

                String qualificationInfos = vo.getQualificationInfos();
                if(!StringUtils.isEmpty(qualificationInfos)){
                    List<QualificationInfoPo> qualificationInfoPolist = (List<QualificationInfoPo>) JSONArray.toCollection(JSONArray.fromObject(qualificationInfos), QualificationInfoPo.class);
                    List<QualificationInfoDto> qualificationInfoDtos = CollateDataUtil.copyListProperties(qualificationInfoPolist, QualificationInfoDto.class);
                    dto.setQualificationInfos(qualificationInfoDtos);
                }

                try {
                    //处理时间参数
                    try {
                        if(null != dto.getCreateTime()){
                            dto.setCreateTimeStr(DateUtil.parseDateToStr(dto.getCreateTime(), DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
                        }
                    } catch (Exception e) {
                        logger.error("batchProviderPoToDto 异常：",e);
                        dto.setCreateTimeStr("");
                    }
                    if (dto.getExpirationDateType() != null && dto.getExpirationDateType() == 1) {
                        dto.setExpirationDateStr("长期");
                    } else {
                        try {
                            if(null != dto.getExpirationDate()) {
                                dto.setExpirationDateStr(DateUtil.parseDateToStr(dto.getExpirationDate(), DateUtil.DATE_TIME_FORMAT_YYYY年MM月DD日));
                            }
                        } catch (Exception e) {
                            logger.error("batchProviderPoToDto 异常：",e);
                            dto.setExpirationDateStr("");
                        }
                    }
                } catch (Exception e) {
                    logger.error("batchProviderPoToDto 异常：",e);
                    dto.setCreateTimeStr("");
                    dto.setExpirationDateStr("");
                }

                dtos.add(dto);
            }
        }
        return dtos;
    }

    private ProviderVoDto singleProviderVoToDto(ProviderBaseinfoVo vo) {
        ProviderVoDto dto = new ProviderVoDto();
        if (null != vo) {
            BeanUtils.copyProperties(vo, dto);
            ProviderExtVo extVo = vo.getProviderExtVo();
            if (extVo != null) {
                ProviderExtVoDto extDto = new ProviderExtVoDto();
                BeanUtils.copyProperties(extVo, extDto);
                dto.setProviderExtVo(extDto);
            }
            if (!CollectionUtils.isEmpty(vo.getProviderSalesPos())) {
                List<ProviderSalesDto> providerSalesDtos = CollateDataUtil.copyListProperties(vo.getProviderSalesPos(), ProviderSalesDto.class);
                dto.setProviderSalesDtos(providerSalesDtos);
            }
            if (!CollectionUtils.isEmpty(vo.getQualificationList())) {
                List<QualificationInfoDto> qualificationInfoDtos = CollateDataUtil.copyListProperties(vo.getQualificationList(), QualificationInfoDto.class);
                dto.setQualificationInfos(qualificationInfoDtos);
            }
        }
        return dto;
    }

    private List<ProviderVoDto> batchProviderVoToVoDto(List<ProviderBaseinfoVo> list) {
        List<ProviderVoDto> dtos = new ArrayList<>();
        if (null != list && list.size() > 0) {
            for (ProviderBaseinfoVo vo : list) {
                ProviderVoDto dto = new ProviderVoDto();
                BeanUtils.copyProperties(vo, dto);
                dtos.add(dto);
            }
        }
        return dtos;
    }

    @Override
    public String refreshApproveDetailByOrganSigns(List<String> organSigns) {
        if (organSigns == null || organSigns.size() == 0) {
            return "error";
        }
        //遍历机构列表
        for (String organSign : organSigns) {
            //先查询该机构下需要刷数据的主表列表信息
            List<ApproveHistoryPo> results = providerInfoService.queryHistoryApprove(organSign);
            logger.info("fix provider results:" + results.size());
            if (results == null || results.size() == 0) {
                continue;
            }
            for (ApproveHistoryPo po : results) {
                List<ApproveHistoryDetailPo> detailPos = providerInfoService.queryHistoryApproveDetail(organSign, po.getBillNo());
                logger.info("fix provider detailPos:" + detailPos);
                //获取当前机构下质量负责人和企业负责人信息
                SystemConfigDto systemConfig = sysConfigApi.querySystemConfig(organSign);
                String zlfuzeren = systemConfig == null ? "" : systemConfig.getQualityOwners();//质量负责人
                String qyfuzeren = systemConfig == null ? "" : systemConfig.getEnterpriseOwners();//企业负责人
                if (detailPos == null || detailPos.size() == 0) {//审核详情没有数据
                    //质量审批详情数据复制
                    logger.info("fix provider detailPos:detailPos == null || detailPos.size() == 0");
                    ApproveHistoryDetailPo po1 = new ApproveHistoryDetailPo();
                    po1.setBillNo(po.getBillNo());
                    po1.setApproveType(2);//审批类型，首营供应商
                    po1.setUserName(zlfuzeren);//质量负责人
                    po1.setApproveStatus(1);//审批状态是1，通过
                    po1.setApproveRemark("同意");
                    po1.setInitiatorName(po.getInitiatorName());//审批发起人
                    po1.setCreateTime(new Date(po.getCreateTime().getTime() + 1000 * 60 * 10));
                    po1.setCreateUser(po.getInitiatorName());//创建人就是审批发起人
                    po1.setUpdateTime(po1.getCreateTime());
                    po1.setUpdateUser(po1.getCreateUser());
                    po1.setYn((byte) 1);//有效
                    po1.setApproveNodeType(1);//质量负责人审批
                    po1.setBaseVersion(redis.getIncr(DictConstant.APPROVEHISTORY_DETAIL_BASEVERSION_PREF_IDENTIFY) + "");
                    po1.setOrganSign(organSign);
                    po1.setGuid("");
                    providerInfoService.insertApproveDetail(po1);
                    //企业审批详情数据复制
                    ApproveHistoryDetailPo po2 = new ApproveHistoryDetailPo();
                    po2.setBillNo(po.getBillNo());
                    po2.setApproveType(2);//审批类型，首营供应商
                    po2.setUserName(qyfuzeren);//企业负责人
                    po2.setApproveStatus(1);//审批状态是1，通过
                    po2.setApproveRemark("同意");
                    po2.setInitiatorName(po.getInitiatorName());//审批发起人
                    po2.setCreateTime(new Date(po.getCreateTime().getTime() + 1000 * 60 * 20));
                    po2.setCreateUser(po.getInitiatorName());//创建人就是审批发起人
                    po2.setUpdateTime(po2.getCreateTime());
                    po2.setUpdateUser(po2.getCreateUser());
                    po2.setYn((byte) 1);//有效
                    po2.setApproveNodeType(2);//企业负责人审批
                    po2.setBaseVersion(redis.getIncr(DictConstant.APPROVEHISTORY_DETAIL_BASEVERSION_PREF_IDENTIFY) + "");
                    po2.setOrganSign(organSign);
                    po2.setGuid("");
                    providerInfoService.insertApproveDetail(po2);
                } else if (detailPos.size() == 1) {//审核详情数目不准确,出现一条的情况肯定是没有企业负责人审批
                    logger.info("fix provider detailPos.size() == 1");
                    //判断类型是质量审批还是企业审批
                    ApproveHistoryDetailPo poxx = detailPos.get(0);
                    if (poxx.getApproveNodeType() != null && poxx.getApproveNodeType() == 1) {
                        logger.info("fix provider poxx.getApproveNodeType() != null && poxx.getApproveNodeType() == 1");
                        ApproveHistoryDetailPo po2 = new ApproveHistoryDetailPo();
                        po2.setBillNo(po.getBillNo());
                        po2.setApproveType(2);//审批类型，首营供应商
                        po2.setUserName(qyfuzeren);//企业负责人
                        po2.setApproveStatus(1);//审批状态是1，通过
                        po2.setApproveRemark("同意");
                        po2.setInitiatorName(poxx.getInitiatorName());//审批发起人
                        po2.setCreateTime(new Date(poxx.getCreateTime().getTime() + 1000 * 60 * 10));
                        po2.setCreateUser(poxx.getInitiatorName());//创建人就是审批发起人
                        po2.setUpdateTime(po2.getCreateTime());
                        po2.setUpdateUser(po2.getCreateUser());
                        po2.setYn((byte) 1);//有效
                        po2.setApproveNodeType(2);//企业负责人审批
                        po2.setBaseVersion(redis.getIncr(DictConstant.APPROVEHISTORY_DETAIL_BASEVERSION_PREF_IDENTIFY) + "");
                        po2.setOrganSign(organSign);
                        po2.setGuid("");
                        providerInfoService.insertApproveDetail(po2);
                    } else if (poxx.getApproveNodeType() != null && poxx.getApproveNodeType() == 2) {
                        //质量审批详情数据复制
                        ApproveHistoryDetailPo po1 = new ApproveHistoryDetailPo();
                        po1.setBillNo(po.getBillNo());
                        po1.setApproveType(2);//审批类型，首营供应商
                        po1.setUserName(zlfuzeren);//质量负责人
                        po1.setApproveStatus(1);//审批状态是1，通过
                        po1.setApproveRemark("同意");
                        po1.setInitiatorName(poxx.getInitiatorName());//审批发起人
                        po1.setCreateTime(new Date(poxx.getCreateTime().getTime() - 100));
                        po1.setCreateUser(poxx.getInitiatorName());//创建人就是审批发起人
                        po1.setUpdateTime(po1.getCreateTime());
                        po1.setUpdateUser(po1.getCreateUser());
                        po1.setYn((byte) 1);//有效
                        po1.setApproveNodeType(1);//质量负责人审批
                        po1.setBaseVersion(redis.getIncr(DictConstant.APPROVEHISTORY_DETAIL_BASEVERSION_PREF_IDENTIFY) + "");
                        po1.setOrganSign(organSign);
                        po1.setGuid("");
                        providerInfoService.insertApproveDetail(po1);
                    }
                }
            }
        }
        return "success";
    }

    @Override
    public List<ProviderDto> queryProviderByParams(ProviderQueryDto providerQueryDto, String organSign) {
        String queryOrganSign = judgeDrugType(organSign);
        ProviderBaseinfoPo pbo = new ProviderBaseinfoPo();
        BeanUtils.copyProperties(providerQueryDto, pbo);
        pbo.setMnemonicCode(providerQueryDto.getName());
        pbo.setOrganSign(queryOrganSign);
        List<ProviderBaseinfoPo> list = providerInfoService.queryProviderNew(pbo);
        List<ProviderDto> dtos = batchProviderPoToDto(list);
        if (!CollectionUtils.isEmpty(dtos)) {
            List<Integer> ids = dtos.stream().map(a->Integer.valueOf(a.getCreateUser())).collect(Collectors.toList());
            List<EmployeeDto> employeeDtos = employeeApi.queryEmployeeByIds(ids);
            Map<String, EmployeeDto> employeeDtoMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(employeeDtos)) {
                employeeDtoMap = employeeDtos.stream().collect(Collectors.toMap(a->String.valueOf(a.getId()),a->a,(k1,k2)->k1));
            }
            for (ProviderDto pvd : dtos) {
                pvd.setCreateUser(employeeDtoMap.get(pvd.getCreateUser()) == null ? pvd.getCreateUser() : employeeDtoMap.get(pvd.getCreateUser()).getName());
                if(pvd.getExpireYn()==1){
                    pvd.setExpireYnStr("是");
                }
                if(pvd.getExpireYn()==0){
                    pvd.setExpireYnStr("否");
                }
                if(pvd.getRelateYn()==1){
                    pvd.setRelateYnStr("是");
                }
                if(pvd.getRelateYn()==0){
                    pvd.setRelateYnStr("否");
                }
            }
        }
        return dtos;
    }

    @Override
    public List<ProviderDto> queryProviderByOrganSign(String organSign,String startTime,String endTime) {
        logger.info("queryProviderByOrganSign  organSign:" + organSign);
        List<ProviderBaseinfoPo> list = providerInfoService.queryProviderByOrganSign(organSign,startTime,endTime);
        logger.info("queryProviderByOrganSign  list:" + list.size());
        Map<String,List<ProviderBaseinfoPo>> mpDto = new HashMap<>();
        for(ProviderBaseinfoPo dto:list){
            String key = dto.getProviderName() + dto.getRegisteredAddress();
            if(mpDto.containsKey(key)){
                mpDto.get(key).add(dto);
            }else{
                List<ProviderBaseinfoPo> listPo = new ArrayList<>();
                listPo.add(dto);
                mpDto.put(key,listPo);
            }
        }

        List<ProviderDto> listDto = new ArrayList<>();
        for(String key:mpDto.keySet()){
            List<ProviderBaseinfoPo> listPo = mpDto.get(key);
            if(listPo.size() > 1){
                logger.info("retain Provider  size:" + listPo.size());
                int num = 0;
                List<ProviderDto> listBefore = new ArrayList<>();
                for(ProviderBaseinfoPo po :listPo){
                    //根据供应商编码去查询丁荣荣采购接口，获取此供应商是否有采购记录，是否可以删除
                    //这里需要打日志输出待删除的供应商信息，然后把供应商加入待删除id列表
                    SaasPurchaseBillInfoPoDto purchDto = purchaseApi.purchaseSupplier(organSign, po.getPref());
                    if(null == purchDto){
                        num++;
                        ProviderDto dto = new ProviderDto();
                        BeanUtils.copyProperties(po,dto);
                        listBefore.add(dto);
                    }else{
                        if(StringUtils.isEmpty(purchDto.getSupplierNo()) && StringUtils.isEmpty(purchDto.getBillNo())){
                            num++;
                            ProviderDto dto = new ProviderDto();
                            BeanUtils.copyProperties(po,dto);
                            listBefore.add(dto);
                        }else {
                            logger.info("purchaseBill exist:"+ JSONObject.toJSONString(po));
                        }
                    }
                }
                if(num == listPo.size()){
                    listBefore.remove(0);
                    logger.info("retain Provider info:"+ JSONObject.toJSONString(listBefore.get(0)));
                }
                for(ProviderDto dto : listBefore){
                    logger.info("repeat Provider info:"+ JSONObject.toJSONString(dto.toString()));
                }
                listDto.addAll(listBefore);
            }
        }
        return listDto;
    }

    @Override
    public List<String> queryProvidersBySkuCcheck(String organSign, String providerName, String businessLicense, Integer providerType) {
        //很简单，没有字段处理逻辑
        return providerInfoService.queryProvidersBySkuCcheck(organSign,providerName,businessLicense,providerType);
    }

    /**
     * 删除销售人员信息
     *
     * @param id
     * @return
     */
    @Override
    public int deleteProviderSales(Long id) {
        return providerSalesService.deleteProviderSales(id);
    }

    @Override
    public int deleteProviderSales(List<Long> ids) {

         return providerSalesService.deleteProviderSales(ids);
    }

    @Override
    public String getbillNo() {
        return providerInfoService.getbillNo();
    }

    @Override
    public ResultVO getProviderSales(String pref, String organSign) {
        ArrayList<ProviderSalesDto> providerSalesDtos = new ArrayList<>();
        List<ProviderSalesPo> providerSales = providerSalesService.findByProviderPrefAndOrganSign(pref, organSign);
        if(!CollectionUtils.isEmpty(providerSales)){
            for(ProviderSalesPo providerSalesPo:providerSales){
                ProviderSalesDto providerSalesDto = new ProviderSalesDto();
                BeanUtils.copyProperties(providerSalesPo,providerSalesDto);
                providerSalesDtos.add(providerSalesDto);
            }
        }
        return ResultVO.createSuccess(providerSalesDtos) ;
    }

    @Override
    public List<ProviderSalesDto> getProviderSales(List<Long> ids) {
           if(CollectionUtils.isEmpty(ids)){
               return Collections.emptyList();
           }
        ArrayList<ProviderSalesDto> providerSalesDtos = new ArrayList<>();
        List<ProviderSalesPo>  poList=  providerSalesService.getProviderSales(ids);
          if(!CollectionUtils.isEmpty(poList) ){
              for(ProviderSalesPo providerSalesPo:poList){
                  ProviderSalesDto providerSalesDto = new ProviderSalesDto();
                  BeanUtils.copyProperties(providerSalesPo,providerSalesDto);
                  providerSalesDtos.add(providerSalesDto);
              }

          }
        return providerSalesDtos;
    }

    @Override
    public int saveSale(ProviderSalesDto providerSalesDto) {
        ProviderSalesPo providerSalesPo = new ProviderSalesPo();
        BeanUtils.copyProperties(providerSalesDto,providerSalesPo);
        return  providerSalesService.saveSale(providerSalesPo);

    }

    @Override
    public Map<Integer, Integer> queryProvidersCount(String organSign) {
        ProviderBaseinfoPo po = new ProviderBaseinfoPo();
        po.setOrganSign(organSign);
        return providerInfoService.queryProvidersCount(po);
    }

    @Override
    public int updateQualificationInfos(String pref, List<QualificationInfoDto> qualificationInfos,String organSign) {
        return providerInfoService.updateQualificationInfos(pref,qualificationInfos,organSign);
    }

    /**
     * 对接码上放心平台获取码上放心平台唯一标识
     * @param organSign
     * @param pref
     * @param providerName
     * @return
     */
    @Override
    public PUserEntInfoDto getMsfxOrganSignInfo(String organSign, String pref, String providerName) {
        PUserEntInfoDto pUserEntInfoDto = new PUserEntInfoDto();
        if(isEmpty(pref)) { //新增
            reqToTaobaoMsfx(pUserEntInfoDto, providerName);
        }else{
            ProviderBaseinfoPo providerBaseinfoPo = providerInfoService.getProviderByOrganSignPref(pref, organSign);
            if(providerBaseinfoPo != null && notEmpty(providerName) && providerName.equals(providerBaseinfoPo.getProviderName())) {
                //兼容供应商名称没改变，但需要获取码上标识
                if(notEmpty(providerBaseinfoPo.getMsfxRefEntId()) && notEmpty(providerBaseinfoPo.getMsfxEntId())) {
                    pUserEntInfoDto.setMsfxRefEntId(providerBaseinfoPo.getMsfxRefEntId());
                    pUserEntInfoDto.setMsfxEntId(providerBaseinfoPo.getMsfxEntId());
                }else{
                    reqToTaobaoMsfx(pUserEntInfoDto, providerName);
                }
            }else{
                reqToTaobaoMsfx(pUserEntInfoDto, providerName);
            }
        }
        return pUserEntInfoDto;
    }

    /**
     * (单体、联营门店)供应商刷码上放心标识
     * @param organSign
     * @return
     */
    @Override
    public boolean refreshProviderMsfxInfo(String organSign) {
        logger.info("refreshProviderMsfxInfo organSign={}", organSign);
        if(isEmpty(organSign)) {
            return true;
        }
        List<ProviderBaseinfoPo> poList = providerInfoService.getProviderListByOrganSign(organSign);
        if(notEmpty(poList)) {
            //对接马上放心平台(避免过于频繁发生http请求)
            int currentNum = 0;
            for (ProviderBaseinfoPo po : poList) {
                if(isEmpty(po.getMsfxRefEntId()) || isEmpty(po.getMsfxEntId())) {
                    //currentNum++;
                    //if(currentNum % 300 == 0) {
                    //    try {
                    //        Thread.sleep(1000);
                    //    } catch (InterruptedException e) {
                    //        logger.error("refreshProviderMsfxInfo sleep error!", e);
                    //    }
                    //}
                    PUserEntInfoDto pUserEntInfoDto = new PUserEntInfoDto();
                    reqToTaobaoMsfx(pUserEntInfoDto, po.getProviderName());
                    if(notEmpty(pUserEntInfoDto.getMsfxRefEntId())) {
                        po.setMsfxRefEntId(pUserEntInfoDto.getMsfxRefEntId());
                        po.setMsfxEntId(pUserEntInfoDto.getMsfxEntId());
                    }
                }
            }
            providerInfoService.batchUpdateForMsfx(poList);
        }
        return true;
    }

    /**
     * 配合Apollo刷所有指定的机构 Apollo配置项:msfx_organsigns 逗号分隔
     * @return
     */
    @Override
    public boolean refreshProviderMsfxInfoByApollo() {
        logger.info("refreshProviderMsfxInfoByApollo msfxOrganSigns={}", msfxOrganSigns);
        long startTime = System.currentTimeMillis();
        if(notEmpty(msfxOrganSigns)) {
            List<String> msfxOrganSignList = OptUtils.stringToStringList(msfxOrganSigns, ",");
            if(notEmpty(msfxOrganSignList)) {
                for (String msfxOrganSign : msfxOrganSignList) {
                    refreshProviderMsfxInfo(msfxOrganSign);
                }
            }
        }
        logger.info("refreshProviderMsfxInfoByApollo msfxOrganSigns={}, costTime={}ms", msfxOrganSigns, System.currentTimeMillis()-startTime);
        return true;
    }

    @Override
    public List<ProviderDto> matchProviderByParams(List<ProviderMatchQueryDto> providerMatchQueryDtos, String organSign) {
        String queryOrganSign = judgeDrugType(organSign);
        List<ProviderBaseinfoPo> poList = Lists.newArrayList();
        XYYApproveRecordPo xyyProvider = xyyApproveRecordService.getXyyMessForOrganSign("XYY", organSign);
        Byte xyyApproved = Byte.valueOf("0");
        if (xyyProvider.getStatus().equals(4)) {
            xyyApproved = Byte.valueOf("1");
        }
        for (ProviderMatchQueryDto providerMatchQueryDto : providerMatchQueryDtos) {
            ProviderBaseinfoPo pbo = new ProviderBaseinfoPo();
            pbo.setMnemonicCode(providerMatchQueryDto.getName());
            pbo.setOrganSign(queryOrganSign);
            pbo.setStatus((byte) 1);
            pbo.setUsed((byte) 1);
            pbo.setNoxyy(xyyApproved);
            List<ProviderBaseinfoPo> list = providerInfoService.queryProviderNew(pbo);
            if (!CollectionUtils.isEmpty(list)){
                ProviderBaseinfoPo providerBaseinfoPo = list.get(0);
                providerBaseinfoPo.setSourceMatch(providerMatchQueryDto.getName());
                poList.add(list.get(0));
            }
        }
        List<ProviderDto> dtos = batchProviderPoToDto(poList);
        if (!CollectionUtils.isEmpty(dtos)) {
            List<Integer> ids = dtos.stream().map(a->Integer.valueOf(a.getCreateUser())).collect(Collectors.toList());
            List<EmployeeDto> employeeDtos = employeeApi.queryEmployeeByIds(ids);
            Map<String, EmployeeDto> employeeDtoMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(employeeDtos)) {
                employeeDtoMap = employeeDtos.stream().collect(Collectors.toMap(a->String.valueOf(a.getId()),a->a,(k1,k2)->k1));
            }
            for (ProviderDto pvd : dtos) {
                pvd.setCreateUser(employeeDtoMap.get(pvd.getCreateUser()) == null ? pvd.getCreateUser() : employeeDtoMap.get(pvd.getCreateUser()).getName());
                if(pvd.getExpireYn()==1){
                    pvd.setExpireYnStr("是");
                }
                if(pvd.getExpireYn()==0){
                    pvd.setExpireYnStr("否");
                }
                if(pvd.getRelateYn()==1){
                    pvd.setRelateYnStr("是");
                }
                if(pvd.getRelateYn()==0){
                    pvd.setRelateYnStr("否");
                }
            }
        }
        return dtos;
    }

    /**
     * 对接码上放心平台
     * @param pUserEntInfoDto
     * @param providerName
     */
    private void reqToTaobaoMsfx(PUserEntInfoDto pUserEntInfoDto, String providerName){
        logger.info("providerName is providerName={}", providerName);
        if(isEmpty(providerName)) {
            return;
        }
        TaobaoClient client = new DefaultTaobaoClient(serverUrl, appKey, appSecret);
        AlibabaAlihealthDrugKytSmyxGetentinfoRequest req = new AlibabaAlihealthDrugKytSmyxGetentinfoRequest();
        req.setEntName(providerName);
        AlibabaAlihealthDrugKytSmyxGetentinfoResponse rsp = null;
        try {
            rsp = client.execute(req);
            if(rsp != null) {
                logger.info("getMsfxOrganSignInfo client.execute rsp={}", rsp.getBody());
            }
            if(rsp != null && rsp.getResult() != null) {
                AlibabaAlihealthDrugKytSmyxGetentinfoResponse.PUserEntInfoDto pUserEntInfoDtoForTaobao = rsp.getResult().getModel();
                if(pUserEntInfoDtoForTaobao != null) {
                    pUserEntInfoDto.setMsfxRefEntId(pUserEntInfoDtoForTaobao.getRefEntId());
                    pUserEntInfoDto.setMsfxEntId(pUserEntInfoDtoForTaobao.getEntId());
                }
            }
        } catch (ApiException e) {
            logger.error("getMsfxOrganSignInfo client.execute error!providerName=" + providerName, e);
        }
    }

    /**
     * 判断机构类型：单体，连锁（门店，总部），联营(门店，总部)
     * @param organSign
     * @return
     */
    private String judgeDrugType(String organSign){
        QueryDrugstoreDto drugstoreDto = drugstoreApi.queryDrugstoreByOrganSign(organSign).getResult();
        if (drugstoreDto == null) {
            return organSign;
        }
        DrugstoreTypeProductEnum typeEnum = DrugstoreTypeUtil.isDrugstoreTypeEnum(drugstoreDto.getBizModel(), drugstoreDto.getOrganSignType());
        String headerOrganSign = drugstoreDto.getHeadquartersOrganSign();
        String currentOrganSign = organSign;
        String queryOrganSign = null;
        switch (typeEnum) {
            case SINGLE_DRUGSTORE://单体
                queryOrganSign = currentOrganSign;
                break;
            case CHAIN_DRUGSTORE://连锁门店
                queryOrganSign = headerOrganSign;
                break;
            case CHAIN_HEADQUARTERS://连锁总部
                queryOrganSign = currentOrganSign;
                break;
            case JOIN_DRUGSTORE://联营门店
                queryOrganSign = currentOrganSign;
                break;
            case JOIN_HEADQUARTERS://联营总部
                queryOrganSign = currentOrganSign;
                break;
            default:
                queryOrganSign = currentOrganSign;
        }
        return queryOrganSign;
    }

}