package com.xyy.saas.supplier.provider.mq.mapper;


import com.xyy.saas.supplier.provider.mq.domain.MqTransaction;

/**@desc 分布式事务记录
 * <AUTHOR>
 * @time: 2019/11/22 11:36
 */
public interface MqTransactionMapper {
    int deleteByPrimaryKey(String txId);

    int insert(MqTransaction record);

    int insertSelective(MqTransaction record);

    MqTransaction selectByPrimaryKey(String txId);

    int updateByPrimaryKeySelective(MqTransaction record);

    int updateByPrimaryKey(MqTransaction record);
}