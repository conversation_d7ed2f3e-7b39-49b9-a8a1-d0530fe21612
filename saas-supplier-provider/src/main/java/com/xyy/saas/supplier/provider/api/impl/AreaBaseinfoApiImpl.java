package com.xyy.saas.supplier.provider.api.impl;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.dubbo.config.annotation.Service;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.supplier.api.AreaBaseinfoApi;
import com.xyy.saas.supplier.dto.AreaBaseinfoDto;
import com.xyy.saas.supplier.provider.dao.AreaBaseinfoMapper;
import com.xyy.saas.supplier.provider.po.AreaBaseinfoPo;
import com.xyy.saas.supplier.provider.serivce.AreaBaseinfoService;
import com.xyy.saas.supplier.provider.util.CollateDataUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Classname AreaBaseinfoApiImpl
 * @Description 区域信息API实现
 * @Date 2020/5/14 15:42
 */
@Service(version = "0.0.1")
public class AreaBaseinfoApiImpl implements AreaBaseinfoApi {

    @Autowired
    private AreaBaseinfoService areaBaseinfoService;

    @Autowired
    private AreaBaseinfoMapper areaBaseinfoMapper;


    @Override
    @SentinelResource(value = "AreaBaseinfoApiImpl.findAreaBaseinfoByCondition", fallback = "findAreaBaseinfoByConditionError")
    public ResultVO<PageInfo<AreaBaseinfoDto>> findAreaBaseinfoByCondition(AreaBaseinfoDto areaBaseinfoDto) {
        PageInfo<AreaBaseinfoDto> result = new PageInfo<>();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNum(areaBaseinfoDto.getPage() == null ? 1 : areaBaseinfoDto.getPage());
        pageInfo.setPageSize(areaBaseinfoDto.getRows()==null?50:areaBaseinfoDto.getRows());
        AreaBaseinfoPo areaBaseinfoPo = new AreaBaseinfoPo();
        List<AreaBaseinfoPo> baseinfoPoList = areaBaseinfoMapper.findAreaBaseinfoByCondition(areaBaseinfoPo);
        BeanUtils.copyProperties(areaBaseinfoDto,areaBaseinfoPo);
        PageInfo<AreaBaseinfoPo> areaBaseinfoPoPageInfo = areaBaseinfoService.findAreaBaseinfoByCondition(pageInfo,areaBaseinfoPo);
        if (areaBaseinfoPoPageInfo == null) {
            return ResultVO.createSuccess();
        }
        BeanUtils.copyProperties(areaBaseinfoPoPageInfo,result);
        if (CollectionUtils.isNotEmpty(areaBaseinfoPoPageInfo.getList())) {
            List<AreaBaseinfoDto> list =  CollateDataUtil.copyListProperties(areaBaseinfoPoPageInfo.getList(),AreaBaseinfoDto.class);
            if (CollectionUtils.isNotEmpty(baseinfoPoList)) {
                Map<String,String> map = baseinfoPoList.parallelStream()
                        .collect(Collectors.toMap(AreaBaseinfoPo::getAreaCode,AreaBaseinfoPo::getAreaName,(k1,k2)->k1));
                list.parallelStream().forEach(dto->{
                    dto.setParentAreaName(map.get(dto.getParentAreaCode()));
                });
             }
            result.setList(list);
        }
        return ResultVO.createSuccess(result);
    }

}
