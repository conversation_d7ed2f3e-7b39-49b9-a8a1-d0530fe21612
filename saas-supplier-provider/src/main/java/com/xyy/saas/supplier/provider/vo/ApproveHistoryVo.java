package com.xyy.saas.supplier.provider.vo;


import com.xyy.saas.supplier.provider.po.ApproveHistoryDetailPo;
import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
@ToString
@Data
public class ApproveHistoryVo {
    /** */
    private Long id;

    /** 关联的首营商品、首营企业的编号*/
    private String gspBusinessNo;

    /** 关联的业务编号：供应商编号，商品编号，处方编号 */
    private String businessNo;
    /** 单据编号 */
    private String billNo;

    /** 审批类型：1--首营商品，2--首营供应商，3--处方登记 */
    private Integer type;

    /** 回复内容 */
    private String msg;

    /** 审批人 */
    private String username;

    /** 审批状态，1：待审批；2：审批通过；3：审批驳回 */
    private Integer status;

    /** 审批发起人 */
    private String initiatorName;

    /** 创建时间 */
    private Date createTime;

    /** 创建人 */
    private String createUser;

    /** 更新时间 */
    private Date updateTime;

    /** 更新人 */
    private String updateUser;

    /** 逻辑删除 1 有效 0 删除 */
    private Byte yn;

    /** 版本号 */
    private String baseVersion;

    /** 机构标识 */
    private String organSign;

    private Date startDate;
    private Date endDate;

    private String startDateStr;
    private String endDateStr;

    /** 主键唯一标识*/
    private String guid;

    /**
     *需要审核的id串
     */
    private String ids;
    /**
     *需要审核的id串
     */
    private String guids;

    /** 分组商品标志 */
    private Byte isProductHidden;

    /** 审批状态集合，1：待审批；2：审批通过；3：审批驳回 */
    private List<Integer> statusList;

    private List<ApproveHistoryDetailPo> detailList = new ArrayList<ApproveHistoryDetailPo>();

    /** 审批发起人 */
    private List<String> initiatorNamelist;


}

