package com.xyy.saas.supplier.provider.api.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.supplier.api.SupplierAdjustAccountApi;
import com.xyy.saas.supplier.dto.AdjustAccountDto;
import com.xyy.saas.supplier.provider.constans.ResultCodeMessage;
import com.xyy.saas.supplier.provider.po.AdjustAccountPo;
import com.xyy.saas.supplier.provider.serivce.SupplierAdjustAccountService;
import com.xyy.user.module.api.EmployeeApi;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: C.HAO
 * Date: 2018/9/28
 * Description:
 */
@Slf4j
@Service(version = "0.0.1")
public class SupplierAdjustAccountApiImpl implements SupplierAdjustAccountApi {
    private static final Logger logger = LoggerFactory.getLogger(SupplierAdjustAccountApiImpl.class);

    @Reference(version = "0.0.1")
    private EmployeeApi employeeApi;
    @Autowired
    private SupplierAdjustAccountService supplierAdjustAccountService;

    /**
     * 获取应付调整单详情
     * @param adjustAccountDto
     * @return
     */
    @Override
    public ResultVO<AdjustAccountDto> getAdjustAccountByBillNo(AdjustAccountDto adjustAccountDto) {
        AdjustAccountPo adjustAccountPo = new AdjustAccountPo();
        BeanUtils.copyProperties(adjustAccountDto,adjustAccountPo);
        ResultVO<AdjustAccountDto> reslut= supplierAdjustAccountService.getAdjustAccountByBillNo(adjustAccountPo);
        AdjustAccountDto dto = reslut.getResult();
        if(dto!=null&&dto.getAddBillUser()!=null){
            String userName = employeeApi.queryEmployeeById(Integer.valueOf(dto.getAddBillUser())).getResult().getName();
            dto.setAddBillUserName(userName);
        }
        return reslut;
    }
    /**
     * 供应商应付调整单保存接口
     * @param
     * @return
     */
    @Override
    public ResultVO<Boolean> addOrUpdate(AdjustAccountDto adjustAccountDto) {
        ResultVO<Boolean> Result = new ResultVO<>();
        BigDecimal finalAccount = adjustAccountDto.getFinalAccount();
        BigDecimal adjustAccount = adjustAccountDto.getAdjustAccount();
        BigDecimal balance = adjustAccountDto.getBalance();
        if(finalAccount.compareTo(balance.add(adjustAccount))!=0){
            Result.setCode(ResultCodeMessage.PARAMS_ACCOUNT_ERROR_CODE);
            Result.setMsg(ResultCodeMessage.PARAMS_ACCOUNT_ERROR_MESSAGE);
            Result.setResult(false);
            return Result;
        }
        AdjustAccountPo adjustAccountPo = new AdjustAccountPo();
        BeanUtils.copyProperties(adjustAccountDto,adjustAccountPo);
        int res=  supplierAdjustAccountService.addOrUpdate(adjustAccountPo);
        if(res<=0){
            Result.setCode(ResultCodeMessage.INTERFACE_PROCESS_ERROR_CODE);
            Result.setMsg(ResultCodeMessage.INTERFACE_PROCESS_ERROR_MESSAGE);
            Result.setResult(false);
            return Result;
        }
        return  ResultVO.createSuccess(true);
    }
    /**
     * 分页查询供应商应付调整单列表
     * @param adjustAccountDto
     * @return
     */
    @Override
    public ResultVO<PageInfo<AdjustAccountDto>> getAdjustAccountByCondition(AdjustAccountDto adjustAccountDto) {
        logger.info("应付调整单入参adjustAccountDto:{} ", JSONObject.toJSONString(adjustAccountDto));
        AdjustAccountPo adjustAccountPo = new AdjustAccountPo();
        BeanUtils.copyProperties(adjustAccountDto,adjustAccountPo);
        if(!StringUtils.isEmpty(adjustAccountPo.getBeginTimeStr())){
            adjustAccountPo.setBeginTimeStr(adjustAccountPo.getBeginTimeStr()+" 00:00:00");
        }
        if(!StringUtils.isEmpty(adjustAccountPo.getEndTimeStr())){
            adjustAccountPo.setEndTimeStr(adjustAccountPo.getEndTimeStr()+" 23:59:59");
        }
        logger.info("应付调整单入参adjustAccountPo:{} ", JSONObject.toJSONString(adjustAccountPo));
        PageInfo<AdjustAccountPo> pageInfo =supplierAdjustAccountService.getAdjustAccountByCondition(adjustAccountPo);
        List<AdjustAccountPo> list = pageInfo.getList();
        ArrayList<AdjustAccountDto> dtoList = new ArrayList<>();
        logger.info("应付调整单返回结果 dtoList:{}",JSONObject.toJSONString(dtoList));
        if(!CollectionUtils.isEmpty(list)){
            list.stream().forEach((data)->{
                AdjustAccountDto adjustAccountDto1 = new AdjustAccountDto();
                BeanUtils.copyProperties(data,adjustAccountDto1);
                dtoList.add(adjustAccountDto1);
               }
            );
        }
        PageInfo<AdjustAccountDto> dtoPageInfo = new PageInfo<>(dtoList);
        BeanUtils.copyProperties(pageInfo,dtoPageInfo);
        dtoPageInfo.setList(dtoList);
        return ResultVO.createSuccess(dtoPageInfo);
    }
}
