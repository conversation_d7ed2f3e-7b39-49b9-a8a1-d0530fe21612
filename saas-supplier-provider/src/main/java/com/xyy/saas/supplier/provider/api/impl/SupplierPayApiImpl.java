package com.xyy.saas.supplier.provider.api.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.github.pagehelper.PageInfo;
import com.xyy.saas.common.util.ResultCodeEnum;
import com.xyy.saas.common.util.ResultVO;
import com.xyy.saas.supplier.api.SupplierPayApi;
import com.xyy.saas.supplier.dto.BalanceDealAccountDto;
import com.xyy.saas.supplier.dto.BalanceTotalPayDto;
import com.xyy.saas.supplier.dto.SupplierPayDetailDto;
import com.xyy.saas.supplier.enums.BillSourceEnum;
import com.xyy.saas.supplier.provider.po.BalanceDealAccountPo;
import com.xyy.saas.supplier.provider.po.BalanceTotalPayPo;
import com.xyy.saas.supplier.provider.serivce.BalanceDealAccountService;
import com.xyy.saas.supplier.provider.serivce.BalanceTotalPayService;
import com.xyy.saas.supplier.provider.serivce.ProviderInfoService;
import com.xyy.saas.supplier.provider.util.DateUtil;
import com.xyy.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created with IntelliJ IDEA.
 * User: C.HAO
 * Date: 2018/9/28
 * Description:
 */
@Service(version = "0.0.1")
public class SupplierPayApiImpl implements SupplierPayApi {

    @Autowired
    private BalanceTotalPayService balanceTotalPayService;

    @Autowired
    private BalanceDealAccountService balanceDealAccountService;

    @Autowired
    private ProviderInfoService providerInfoService;

    @Override
    public ResultVO<PageInfo<BalanceTotalPayDto>> pageFindTotalPay(PageInfo pageInfo, BalanceTotalPayDto balanceTotalPayDto) {
        if (balanceTotalPayDto == null || StringUtils.isBlank(balanceTotalPayDto.getOrganSign())) {
            return new ResultVO<>(ResultCodeEnum.ERROR, "参数错误");
        }
        BalanceTotalPayPo balanceTotalPay = new BalanceTotalPayPo();
        BeanUtils.copyProperties(balanceTotalPayDto, balanceTotalPay);
        return ResultVO.createSuccess(balanceTotalPayService.pageFindTotalPay(pageInfo, balanceTotalPay));
    }

    @Override
    public ResultVO<Integer>  addTotalpay(BalanceTotalPayDto balanceTotalPayDto) {
        if (balanceTotalPayDto == null || StringUtils.isBlank(balanceTotalPayDto.getOrganSign())) {
            return new ResultVO<>(ResultCodeEnum.ERROR, "参数错误");
        }
        BalanceTotalPayPo balanceTotalPay = new BalanceTotalPayPo();
        BeanUtils.copyProperties(balanceTotalPayDto, balanceTotalPay);
        return ResultVO.createSuccess(balanceTotalPayService.addTotalpay(balanceTotalPay));
    }

    @Override
    public ResultVO<List<String>> addTotalpayList(List<List<String>> list,String userName, String organSign) {
        if (StringUtils.isBlank(organSign)) {
            return new ResultVO<>(ResultCodeEnum.ERROR, "参数错误");
        }
        return ResultVO.createSuccess(balanceTotalPayService.addTotalpayList(list, userName,organSign));
    }

    @Override
    public ResultVO<BalanceTotalPayDto> getTotalPayByPref(String pref, String organSign) {
        if (StringUtils.isBlank(organSign)) {
            return new ResultVO<>(ResultCodeEnum.ERROR, "参数错误");
        }
        return ResultVO.createSuccess(balanceTotalPayService.getTotalPayByPref(pref, organSign));
    }

    @Override
    public ResultVO<List<BalanceTotalPayDto>> exportExcel(BalanceTotalPayDto balanceTotalPayDto) {
        if (balanceTotalPayDto == null || StringUtils.isBlank(balanceTotalPayDto.getOrganSign())) {
            return new ResultVO<>(ResultCodeEnum.ERROR, "参数错误");
        }
        BalanceTotalPayPo balanceTotalPay = new BalanceTotalPayPo();
        BeanUtils.copyProperties(balanceTotalPayDto, balanceTotalPay);
        return ResultVO.createSuccess(balanceTotalPayService.exportExcel(balanceTotalPay));
    }

    @Override
    public ResultVO<PageInfo<BalanceDealAccountDto>> getSupplierPayDetail( SupplierPayDetailDto detailDto) {
        if (detailDto == null|| StringUtils.isBlank(detailDto.getOrganSign())) {
            return new ResultVO<>(ResultCodeEnum.ERROR,"参数错误");
        }
        PageInfo pageInfo=new PageInfo();
        pageInfo.setPageSize(detailDto.getRows()==null?10:detailDto.getRows());
        pageInfo.setPageNum(detailDto.getPage()==null?1:detailDto.getPage());
        String beginTimeStr = detailDto.getBeginTimeStr();
        String endTimeStr = detailDto.getEndTimeStr();
        if(!org.apache.commons.lang3.StringUtils.isEmpty(beginTimeStr)){
            detailDto.setBeginTime(DateUtil.parseStrToDate(beginTimeStr+" 00:00:00", DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
            detailDto.setBeginTimeStr(beginTimeStr);
        }
        if(!org.apache.commons.lang3.StringUtils.isEmpty(endTimeStr)){
            detailDto.setEndTime(DateUtil.parseStrToDate(endTimeStr+" 23:59:59", DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
            detailDto.setEndTimeStr(endTimeStr);
        }
        BalanceDealAccountPo balanceDealAccount = new BalanceDealAccountPo();
        BeanUtils.copyProperties(detailDto, balanceDealAccount);
        if(!StringUtil.isEmpty(balanceDealAccount.getBillNo()) && !CollectionUtils.isEmpty(balanceDealAccount.getBillNoList())){
            balanceDealAccount.setBillNoList(Collections.emptyList());
        }if(balanceDealAccount.getBillSource()!=null && balanceDealAccount.getBillSource()!=0){
            balanceDealAccount.setBillSourceList(Collections.emptyList());
        }else if(CollectionUtils.isEmpty(balanceDealAccount.getBillSourceList())){
            List<Byte> billSourceeList = Arrays.stream(BillSourceEnum.values())
                    .filter(billSourceEnum -> billSourceEnum.getType()!=BillSourceEnum.PURCHASE_SETTLEMENT_BILL.getType())
                    .map(billSourceEnum -> (byte)billSourceEnum.getType())
                    .collect( Collectors.toList());
            balanceDealAccount.setBillSourceList(billSourceeList);
            balanceDealAccount.setBillSource(null);
        }
        if(!StringUtils.isBlank(detailDto.getPharmacyPref())){
            String providerPref = providerInfoService.getPrefByPharmacyPrefAndOrganSign(detailDto.getPharmacyPref(), detailDto.getOrganSign());
            if(!StringUtils.isBlank(providerPref)){
                balanceDealAccount.setProviderPref(providerPref);
            }else{//匹配不到
                return ResultVO.createSuccess(new PageInfo<BalanceDealAccountDto>());
            }
        }
        return ResultVO.createSuccess(balanceDealAccountService.findDealAccount(pageInfo, balanceDealAccount));
    }

}
