package com.xyy.saas.supplier.provider.api.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.xyy.emule.api.SynchDataInfoApi;
import com.xyy.emule.dto.InitSubTaskDto;
import com.xyy.saas.common.api.SystemDictApi;
import com.xyy.saas.common.dto.SystemDictDto;
import com.xyy.saas.common.dto.SystemDictQueryDto;
import com.xyy.saas.rabbitmq.core.api.MessagePushApi;
import com.xyy.saas.supplier.provider.config.redis.RedisManager;
import com.xyy.saas.supplier.provider.config.redis.RedissonManager;
import com.xyy.saas.supplier.provider.constans.DictConstant;
import com.xyy.saas.supplier.provider.serivce.SupplierInfoService;
import com.xyy.user.module.api.DrugstoreApi;
import com.xyy.user.module.dto.restructure.SaaSDrugstoreDto;
import com.xyy.user.module.enums.DrugstoreBizModelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Service(version = "1.0.0" , group = "supplierInfoImport")
public class SupplierInfoImportImpl implements SynchDataInfoApi {

    @Autowired
    private SupplierInfoService supplierInfoService;

    @Reference(version = "0.0.1")
    private DrugstoreApi drugstoreApi;

    @Reference(version = "0.0.1")
    private MessagePushApi messagePushApi;

    @Reference(version = "0.0.1")
    private SystemDictApi systemDictApi;

    @Autowired
    RedissonManager redissonManager;

    @Autowired
    private RedisManager redisManager;

    @Override
    public List<InitSubTaskDto> synchImportData(List<InitSubTaskDto> list) {
        log.info("供应商期初导入，传入数据条数：{}，数据内容：{}",list.size(),list);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        String organSign = list.get(0).getOrganSign();
        try{
            // 获取供应商字典值
            List<Integer> bussIds = Arrays.asList(
                    DictConstant.providerCompanyType,
                    DictConstant.BUSINESS_SCOPE);

            List<SystemDictDto> dictDtos= new ArrayList<>();
            SystemDictQueryDto queryDto = new SystemDictQueryDto();
            queryDto.setBusinessIdList(bussIds);
            queryDto.setPageNo(1);
            queryDto.setPageSize(1000);
            queryDto.setYn((byte)1);
            PageInfo<SystemDictDto> pageByCondition = systemDictApi.findPageByCondition(queryDto, organSign);
            if(pageByCondition!=null&&!org.springframework.util.CollectionUtils.isEmpty(pageByCondition.getList())){
                dictDtos.addAll(pageByCondition.getList());
                int pages = pageByCondition.getPages();
                for (int i=1;i<pages;i++){
                    queryDto.setPageNo(i+1);
                    PageInfo<SystemDictDto> pageInfo = systemDictApi.findPageByCondition(queryDto, organSign);
                    if(pageInfo!=null&&!org.springframework.util.CollectionUtils.isEmpty(pageInfo.getList())) {
                        dictDtos.addAll(pageInfo.getList());
                    }
                }
            }
            if (dictDtos == null) {
                list.parallelStream().forEach(initSubTaskDto -> {
                    initSubTaskDto.setErrorInfo("企业类型和经验范围不能为空");
                    initSubTaskDto.setStatus(2);
                });
                log.info("供应商期初导入，返回数据条数：{}，数据内容：{}",list.size(),list);
                return list;
            }
            //  Map<Integer, Map<String, Integer>> bussinessMap = systemDictApi.findByBussinessIds(bussIds, organSign);
            List<SystemDictDto> providerTypeList = dictDtos.parallelStream().filter(item -> DictConstant.PROVIDER_TYPE.equals(item.getBussinessId())).collect(Collectors.toList());
            List<SystemDictDto> businessScopeList = dictDtos.parallelStream().filter(item -> DictConstant.BUSINESS_SCOPE.equals(item.getBussinessId())).collect(Collectors.toList());

            ConcurrentHashMap<String, Integer> type = new ConcurrentHashMap<>();
            providerTypeList.parallelStream().forEach(item -> {
                type.put(item.getName(),item.getId());
            });
            ArrayList<Integer> parentIdList = new ArrayList<>();
            ConcurrentHashMap<String, Integer> businessScopeMap = new ConcurrentHashMap<>();
            ConcurrentHashMap<Integer, SystemDictDto> businessScopeMap2 = new ConcurrentHashMap<>();
            businessScopeList.stream().forEach(item -> {
              if(item.getParentId()!=null && item.getParentId()!=0){
                  parentIdList.add(item.getParentId());
              }

            });
            //过滤拥有子级的经营范围（不可以导入）
            businessScopeList.parallelStream().forEach(item -> {
               if(!parentIdList.contains(item.getId())){
                   businessScopeMap.put(item.getName(),item.getId());
                   businessScopeMap2.put(item.getId(),item);
               }

            });

            // 供应商编号缓存判断是否存在
            Set<String> strSet = businessScopeMap.keySet();
            list.stream().forEach(initSubTaskDto->{
                supplierInfoService.synchImportData(initSubTaskDto,organSign,type,businessScopeMap,strSet,businessScopeMap2);
            });
            pushProviderMessToMQ( organSign);
            return list;
        }catch (Exception e){
            log.error("供应商期初导入异常：",e);
            list.parallelStream().forEach(initSubTaskDto -> {
                initSubTaskDto.setErrorInfo("导入异常，请联系工作人员");
            });
        }
        return list;
    }

    private void pushmess(String organSign){
        JSONObject json = new JSONObject();
        String[] tables = {"saas_provider_baseinfo"};
        json.put("code", "sync");
        json.put("tables", tables);
        messagePushApi.sendMsgByOrganSign(organSign, json.toJSONString());
    }

    private void pushProviderMessToMQ(String organSign) {
        //根据机构号获取机构信息
        SaaSDrugstoreDto saaSDrugstoreDto = drugstoreApi.getDrugstoreByOrganSign(organSign);
        if(DrugstoreBizModelEnum.CHAIN_STORE.toEquals(saaSDrugstoreDto.getBizModel())){
            List<SaaSDrugstoreDto> drugs = drugstoreApi.getDrugstoreByHeadquartersOrganSign(organSign);//获取门店列表
            if(!org.springframework.util.CollectionUtils.isEmpty(drugs)){
                for(SaaSDrugstoreDto dto : drugs){
                    pushmess(dto.getOrganSign());
                }
            }
        }else{
            pushmess(organSign);
        }
    }



}
