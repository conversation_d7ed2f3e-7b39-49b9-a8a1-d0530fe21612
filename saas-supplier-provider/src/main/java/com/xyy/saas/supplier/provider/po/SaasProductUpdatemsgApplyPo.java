package com.xyy.saas.supplier.provider.po;

import java.io.Serializable;
import java.util.Date;

public class SaasProductUpdatemsgApplyPo implements Serializable {

    private Integer id;
    private String organSign;
    private String recallNo;
    private String guid;
    private String pref;
    private Integer updateType;
    private Integer yn;
    private String applyUser;
    private Integer applyUserId;
    private Date applyTime;
    private String commonName;
    private String updateContent;

    private String auditUser;
    private Integer auditUserId;
    private Date auditTime;
    private Integer auditState;
    private String auditRemark;
    private String createUser;
    private Date createTime;
    private String updateUser;
    private Date updateTime;
    private Integer todoRoleId;//当前代办人角色id
    private String todoRoleName;//当前代办人角色

    public Integer getTodoRoleId() {
        return todoRoleId;
    }

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    public void setTodoRoleId(Integer todoRoleId) {
        this.todoRoleId = todoRoleId;
    }

    public String getTodoRoleName() {
        return todoRoleName;
    }

    public void setTodoRoleName(String todoRoleName) {
        this.todoRoleName = todoRoleName;
    }

    public String getUpdateContent() {
        return updateContent;
    }

    public void setUpdateContent(String updateContent) {
        this.updateContent = updateContent;
    }

    public String getCommonName() {
        return commonName;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRecallNo() {
        return recallNo;
    }

    public void setRecallNo(String recallNo) {
        this.recallNo = recallNo;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getPref() {
        return pref;
    }

    public void setPref(String pref) {
        this.pref = pref;
    }

    public Integer getUpdateType() {
        return updateType;
    }

    public void setUpdateType(Integer updateType) {
        this.updateType = updateType;
    }

    public Integer getApplyUserId() {
        return applyUserId;
    }

    public void setApplyUserId(Integer applyUserId) {
        this.applyUserId = applyUserId;
    }

    public Integer getYn() {
        return yn;
    }

    public void setYn(Integer yn) {
        this.yn = yn;
    }

    public String getApplyUser() {
        return applyUser;
    }

    public void setApplyUser(String applyUser) {
        this.applyUser = applyUser;
    }

    public Date getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public String getAuditUser() {
        return auditUser;
    }

    public void setAuditUser(String auditUser) {
        this.auditUser = auditUser;
    }

    public Integer getAuditUserId() {
        return auditUserId;
    }

    public void setAuditUserId(Integer auditUserId) {
        this.auditUserId = auditUserId;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public Integer getAuditState() {
        return auditState;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public void setAuditState(Integer auditState) {
        this.auditState = auditState;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
