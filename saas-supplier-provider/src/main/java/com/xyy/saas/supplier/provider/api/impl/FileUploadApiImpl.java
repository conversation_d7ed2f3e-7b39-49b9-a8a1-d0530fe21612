package com.xyy.saas.supplier.provider.api.impl;

import com.alibaba.dubbo.config.annotation.Service;

import com.xyy.saas.supplier.api.FileUploadApi;
import com.xyy.saas.supplier.dto.ImgUrlDto;
import com.xyy.saas.supplier.provider.po.ImgUrlPo;
import com.xyy.saas.supplier.provider.serivce.UploadFileService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Service(version = "0.0.1")
public class FileUploadApiImpl implements FileUploadApi {

    @Autowired
    private UploadFileService uploadFileService;
    
    @Override
    public List<ImgUrlDto> findListByPid(String pref, String type, String organSign) {
        List<ImgUrlPo> list = uploadFileService.findListByPref(pref, type, organSign);
        List<ImgUrlDto> dtos = new ArrayList<>();
        if (null != list && list.size() > 0){
            list.forEach(item -> {
                ImgUrlDto dto = new ImgUrlDto();
                BeanUtils.copyProperties(item, dto);
                dtos.add(dto);
            });
        }
        return dtos;
    }

    @Override
    public void saveImgs(ImgUrlDto imgUrlDto) {
        ImgUrlPo po = new ImgUrlPo();
        BeanUtils.copyProperties(imgUrlDto, po);
        uploadFileService.saveImgs(po);
    }

    @Override
    public void updateImgs(ImgUrlDto imgUrlDto) {
        ImgUrlPo po = new ImgUrlPo();
        BeanUtils.copyProperties(imgUrlDto, po);
        uploadFileService.updateImgs(po);
    }

    @Override
    public List<ImgUrlDto> findListByRelationPref(String pref, String type, String organSign) {
        return findListByPid(pref, type, organSign);
    }
}
