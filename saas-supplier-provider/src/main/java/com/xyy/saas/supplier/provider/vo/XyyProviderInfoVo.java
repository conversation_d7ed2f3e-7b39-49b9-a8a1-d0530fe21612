package com.xyy.saas.supplier.provider.vo;


import java.util.Date;

public class XyyProviderInfoVo {

    private String providerName;//公司名称
    private String legalRepresentative;//法定代表人
    private Integer providerType;//供应商类别447
    private String registeredAddress;//注册地址
    private String businessScope;//经营范围
    private String businessLicense;//营业执照号
    private String licenceAuthority;//发证机关
    private Date registeredDate;//注册日期
    private String depositBank;//开户银行
    private String bankAccount;//银行账号
    private String accountName;//开户户名
    private String province;//省市名称
    private String registeredDateStr;//注册日期
    private Integer expirationDateType;//1长期，2手动
    private String expirationDate;//营业执照有效期
    private ProviderExtVo remarkExts;//供应商扩展信息
    private Date expirationDateTime;//有效期日期格式

    public Date getExpirationDateTime() {
        return expirationDateTime;
    }

    public void setExpirationDateTime(Date expirationDateTime) {
        this.expirationDateTime = expirationDateTime;
    }

    public ProviderExtVo getRemarkExts() {
        return remarkExts;
    }

    public void setRemarkExts(ProviderExtVo remarkExts) {
        this.remarkExts = remarkExts;
    }

    public String getRegisteredDateStr() {
        return registeredDateStr;
    }

    public void setRegisteredDateStr(String registeredDateStr) {
        this.registeredDateStr = registeredDateStr;
    }

    public Integer getExpirationDateType() {
        return expirationDateType;
    }

    public void setExpirationDateType(Integer expirationDateType) {
        this.expirationDateType = expirationDateType;
    }

    public String getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getDepositBank() {
        return depositBank;
    }

    public void setDepositBank(String depositBank) {
        this.depositBank = depositBank;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getProviderName() {
        return providerName;
    }

    public void setProviderName(String providerName) {
        this.providerName = providerName;
    }

    public String getLegalRepresentative() {
        return legalRepresentative;
    }

    public void setLegalRepresentative(String legalRepresentative) {
        this.legalRepresentative = legalRepresentative;
    }

    public Integer getProviderType() {
        return providerType;
    }

    public void setProviderType(Integer providerType) {
        this.providerType = providerType;
    }

    public String getRegisteredAddress() {
        return registeredAddress;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public String getBusinessLicense() {
        return businessLicense;
    }

    public void setBusinessLicense(String businessLicense) {
        this.businessLicense = businessLicense;
    }

    public String getLicenceAuthority() {
        return licenceAuthority;
    }

    public void setLicenceAuthority(String licenceAuthority) {
        this.licenceAuthority = licenceAuthority;
    }

    public Date getRegisteredDate() {
        return registeredDate;
    }

    public void setRegisteredDate(Date registeredDate) {
        this.registeredDate = registeredDate;
    }
}
