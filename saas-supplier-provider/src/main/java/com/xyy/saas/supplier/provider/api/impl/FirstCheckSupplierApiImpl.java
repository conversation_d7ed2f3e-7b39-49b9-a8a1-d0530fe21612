package com.xyy.saas.supplier.provider.api.impl;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.xyy.saas.common.api.SaasAreaApi;
import com.xyy.saas.common.dto.SaasRegionBusinessDto;
import com.xyy.saas.common.dto.XyySaasRegionParamsDto;
import com.xyy.saas.common.enums.BusinessFlowEnum;
import com.xyy.saas.supplier.api.FirstCheckSupplierApi;
import com.xyy.saas.supplier.dto.*;
import com.xyy.saas.supplier.provider.po.ApproveHistoryPo;
import com.xyy.saas.supplier.provider.po.FirstCheckProviderPo;
import com.xyy.saas.supplier.provider.po.ProviderBaseinfoPo;
import com.xyy.saas.supplier.provider.po.QualificationInfoPo;
import com.xyy.saas.supplier.provider.serivce.ApproveHistoryService;
import com.xyy.saas.supplier.provider.serivce.FirstCheckProviderService;
import com.xyy.saas.supplier.provider.util.DateUtil;
import com.xyy.saas.supplier.provider.vo.ApproveHistoryVo;
import com.xyy.saas.supplier.provider.vo.FirstCheckProviderVo;
import com.xyy.saas.workflow.api.WorkflowHistoryService;
import com.xyy.saas.workflow.exception.WorkflowException;
import com.xyy.saas.workflow.model.dto.WorkflowAuditLogQueryDto;
import com.xyy.saas.workflow.model.meta.TenantIdEnum;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service(version = "0.0.1")
public class FirstCheckSupplierApiImpl implements FirstCheckSupplierApi {
    private static final Logger logger = LoggerFactory.getLogger(FirstCheckSupplierApi.class);

    @Autowired
    private FirstCheckProviderService firstCheckProviderService;

    @Autowired
    private ApproveHistoryService approveHistoryService;

    @Reference(version = "1.0.0")
    private WorkflowHistoryService workflowHistoryService;

    @Reference(version = "0.0.1")
    private SaasAreaApi saasAreaApi;

    @Override
    public PageInfo<FirstCheckProviderVoDto> findPageInfo(PageInfo pageInfo, FirstCheckProviderDto firstCheckProviderPo) {
        FirstCheckProviderPo po = new FirstCheckProviderPo();
        BeanUtils.copyProperties(firstCheckProviderPo, po);
        if (null != firstCheckProviderPo.getProviderinfo()){
            if (null != firstCheckProviderPo.getProviderinfo()){
                ProviderBaseinfoPo ppo = new ProviderBaseinfoPo();
                BeanUtils.copyProperties(firstCheckProviderPo.getProviderinfo(), ppo);
                po.setProviderinfo(ppo);
            }
        }
        PageInfo info =  firstCheckProviderService.findPageInfo(pageInfo, po);
        List<FirstCheckProviderPo> list = info.getList();
        List<FirstCheckProviderVoDto> dtos = new ArrayList<>();
        if (null != list && list.size() > 0){
            for (FirstCheckProviderPo vo : list) {
                FirstCheckProviderVoDto dto = new FirstCheckProviderVoDto();
                BeanUtils.copyProperties(vo, dto);
                ProviderDto pdto = new ProviderDto();
                BeanUtils.copyProperties(vo.getProviderinfo(), pdto);
                dto.setProviderinfo(pdto);
                dtos.add(dto);
            }
        }
        info.setList(dtos);
        return info;
    }

    @Override
    public FirstCheckProviderDto getProviderInfoById(String guid,String organ) {
        FirstCheckProviderPo po = firstCheckProviderService.getProviderInfoById(guid,organ);
        FirstCheckProviderDto dto = new FirstCheckProviderDto();
        ProviderBaseinfoPo provider = po.getProviderinfo();
        BeanUtils.copyProperties(po, dto);
        ProviderDto pdto = new ProviderDto();
        BeanUtils.copyProperties(provider, pdto);
        dto.setProviderinfo(pdto);
        return dto;
    }

    @Override
    public FirstCheckProviderVoDto getFirstCheckProviderByPref(String providerNo, String organSign) {
        FirstCheckProviderPo po = firstCheckProviderService.getFirstCheckProviderByPref(providerNo, organSign);
        FirstCheckProviderVoDto dto = new FirstCheckProviderVoDto();
        BeanUtils.copyProperties(po, dto);
        ProviderBaseinfoPo providerinfo = po.getProviderinfo();
        ProviderDto providerDto = new ProviderDto();
        BeanUtils.copyProperties(providerinfo, providerDto);
        List<String> imgs = firstCheckProviderService.selectImgsByPrimaryKey(dto.getGuid(),organSign);
        String imgUrl = Joiner.on(",").join(imgs);
        providerDto.setImgUrl(imgUrl);
        dto.setProviderinfo(providerDto);
        return dto;
    }

    @Override
    public FirstCheckProviderVoDto getProviderInfoByIdForGSP(String guid, String organSign) {
        logger.info("getProviderInfoByIdForGSP 查询入参：guid:{},organSign:{}",guid,organSign);
        FirstCheckProviderVo po = firstCheckProviderService.getProviderInfoByIdForGSP(guid, organSign);
        FirstCheckProviderVoDto dto = new FirstCheckProviderVoDto();
        if (null != po){
            BeanUtils.copyProperties(po, dto);
            ProviderBaseinfoPo provider = po.getProviderinfo();
            if (null != provider){
                ProviderDto pdto = new ProviderDto();
                BeanUtils.copyProperties(provider, pdto);
                dto.setProviderinfo(pdto);
            }
        }
        logger.info("getProviderInfoByIdForGSP 查询结果：dto:{}",dto);
        return dto;
    }

    @Override
    public PageInfo<FirstCheckProviderDto> findHeadquartersPageInfo(PageInfo<FirstCheckProviderDto> pageInfo, FirstCheckProviderDto firstProvider) {

        FirstCheckProviderPo po = new FirstCheckProviderPo();
        BeanUtils.copyProperties(firstProvider, po);

        PageInfo info =  firstCheckProviderService.findHeadquartersPageInfo(pageInfo, po);

        List<FirstCheckProviderPo> list = info.getList();
        List<FirstCheckProviderDto> dtos = new ArrayList<>();
        if (null != list && list.size() > 0){
            for (FirstCheckProviderPo vo : list) {
                FirstCheckProviderDto dto = new FirstCheckProviderDto();
                BeanUtils.copyProperties(vo, dto);
                ProviderDto pdto = new ProviderDto();
                BeanUtils.copyProperties(vo.getProviderinfo(), pdto);
                if (pdto.getExpirationDateType() != null && pdto.getExpirationDateType() == 1) {
                    pdto.setExpirationDateStr("长期");
                } else {
                    try {
                        if(pdto.getExpirationDate()!=null){
                            pdto.setExpirationDateStr(DateUtil.parseDateToStr(pdto.getExpirationDate(), DateUtil.DATE_TIME_FORMAT_YYYY年MM月DD日));
                        }
                    } catch (Exception e) {
                        logger.error("findHeadquartersPageInfo 异常：",e);
                        pdto.setExpirationDateStr("");
                    }
                }
                dto.setProviderinfo(pdto);

                dtos.add(dto);
            }
        }
        info.setList(dtos);
        return info;
    }

    @Override
    public ApproveHistoryDto getApproveHistoryByProviderPrefOrganSign(String providerPref, String organSign) {

        return firstCheckProviderService.getApproveHistoryByProviderPrefOrganSign(providerPref,organSign);
    }

    @Override
    public List<FirstCheckProviderDto> findAllHeadquarters(FirstCheckProviderDto firstCheckProviderDto) {
        FirstCheckProviderPo po = new FirstCheckProviderPo();
        BeanUtils.copyProperties(firstCheckProviderDto, po);
        List<FirstCheckProviderPo> list =  firstCheckProviderService.findAllHeadquarters(po);
        List<FirstCheckProviderDto> dtos = new ArrayList<>();
        if (null != list && list.size() > 0){
            for (FirstCheckProviderPo vo : list) {
                FirstCheckProviderDto dto = new FirstCheckProviderDto();
                BeanUtils.copyProperties(vo, dto);
                ProviderDto pdto = new ProviderDto();
                BeanUtils.copyProperties(vo.getProviderinfo(), pdto);
                if (pdto.getExpirationDateType() != null && pdto.getExpirationDateType() == 1) {
                    pdto.setExpirationDateStr("长期");
                } else {
                    try {
                        pdto.setExpirationDateStr(DateUtil.parseDateToStr(pdto.getExpirationDate(), DateUtil.DATE_TIME_FORMAT_YYYY年MM月DD日));
                    } catch (Exception e) {
                        logger.error("findAllHeadquarters 异常：",e);
                        pdto.setExpirationDateStr("");
                    }
                }
                dto.setProviderinfo(pdto);
                dtos.add(dto);
            }
        }
        return dtos;
    }

    @Override
    public FirstCheckProviderVoDto getFirstCheckProviderChainByPref(String providerNo, String organSign) {
        FirstCheckProviderPo po = firstCheckProviderService.getFirstCheckProviderByPref(providerNo, organSign);
        FirstCheckProviderVoDto dto = new FirstCheckProviderVoDto();
        BeanUtils.copyProperties(po, dto);
        ProviderBaseinfoPo providerinfo = po.getProviderinfo();
        if(null != providerinfo){
            ProviderDto providerDto = new ProviderDto();
            BeanUtils.copyProperties(providerinfo, providerDto);
            if (providerDto.getExpirationDateType() != null && providerDto.getExpirationDateType() == 1) {
                providerDto.setExpirationDateStr("长期");
            } else {
                try {
                    providerDto.setExpirationDateStr(DateUtil.parseDateToStr(providerDto.getExpirationDate(), DateUtil.DATE_TIME_FORMAT_YYYY年MM月DD日));
                } catch (Exception e) {
                    logger.error("setExpirationDateStr 异常：",e);
                    providerDto.setExpirationDateStr("");
                }
            }
            String registeredAddressCode = providerDto.getRegisteredAddressCode();
            StringBuffer stringBuffer = new StringBuffer();
            try {
                //TODO 优化地区码
               String[] split = registeredAddressCode.split(",");
               for(String s:  split){
                XyySaasRegionParamsDto xyySaasRegionParamsDto = new XyySaasRegionParamsDto();
                xyySaasRegionParamsDto.setAreaCode(Integer.parseInt(s));
                SaasRegionBusinessDto saasRegionBusinessDto = saasAreaApi.queryRegionByAreaCode(xyySaasRegionParamsDto);
                stringBuffer.append( saasRegionBusinessDto.getAreaName());
               }
            } catch (Exception e) {
                logger.error("获取省市区名称数据异常：",e);
            }
            stringBuffer.append(providerDto.getRegisteredAddress());
            providerDto.setRegisteredAddress(stringBuffer.toString());
            dto.setProviderinfo(providerDto);
            ApproveHistoryVo historyVo = new ApproveHistoryVo();
            historyVo.setBusinessNo(providerinfo.getPref());
            historyVo.setOrganSign(organSign);
            List<ApproveHistoryPo> list = approveHistoryService.queryPrescriptionProductInfos(historyVo);
            logger.error("获取供应商审核主表记录：" + JSON.toJSONString(list));
            List<WorkflowAuditLogQueryDto> workflowLoglist = new ArrayList<>();
            if(list!=null&& list.size()>0){
                for(ApproveHistoryPo historyPo:list) {
                    logger.info("审批主表记录，信息：" + historyPo.getCreateTime());
                    try {
                        List<WorkflowAuditLogQueryDto> workflowLogQueryDtos = workflowHistoryService.queryHistory(historyPo.getBillNo(), BusinessFlowEnum.SAAS_Commodity_FirstSale_Supplier.getValue(), TenantIdEnum.SAAS.getValue());
                        Collections.reverse(workflowLogQueryDtos);
                        workflowLoglist.addAll(workflowLogQueryDtos);
                        logger.error("调用审批流获取审批记录信息：" + JSON.toJSONString(workflowLogQueryDtos));
                    } catch (WorkflowException e) {
                        logger.error("调用审批流自获取审批记录接口失败，信息：" + e.getMessage());
                    }
                }
            }
            dto.setRecordList(workflowLoglist);
            String qualificationInfos = providerinfo.getQualificationInfos();
            if (StringUtils.isNotBlank(qualificationInfos)) {
                List<QualificationInfoDto> qualificationInfoDtos = JSON.parseArray(qualificationInfos, QualificationInfoDto.class);
                dto.getProviderinfo().setQualificationInfos(qualificationInfoDtos);
            }

        }
        return dto;
    }
}
