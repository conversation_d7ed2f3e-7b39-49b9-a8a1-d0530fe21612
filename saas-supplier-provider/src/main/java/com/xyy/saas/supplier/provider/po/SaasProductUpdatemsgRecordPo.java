package com.xyy.saas.supplier.provider.po;

import java.io.Serializable;
import java.util.Date;

public class SaasProductUpdatemsgRecordPo implements Serializable {

    private Integer id;

    public String getOrganSign() {
        return organSign;
    }

    public void setOrganSign(String organSign) {
        this.organSign = organSign;
    }

    private String organSign;
    private String guid;
    private String updatemsgGuid;
    private String updateContent;

    private String createUser;
    private Date createTime;
    private String updateUser;
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getUpdatemsgGuid() {
        return updatemsgGuid;
    }

    public void setUpdatemsgGuid(String updatemsgGuid) {
        this.updatemsgGuid = updatemsgGuid;
    }

    public String getUpdateContent() {
        return updateContent;
    }

    public void setUpdateContent(String updateContent) {
        this.updateContent = updateContent;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
