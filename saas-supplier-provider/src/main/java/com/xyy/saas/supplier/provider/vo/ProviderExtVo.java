package com.xyy.saas.supplier.provider.vo;

import lombok.ToString;

/**
* <p>Title: ProviderExtVo</p>  
* <p>Description: 供应商信息额外字段</p>  
* <AUTHOR>  
* @date 2018年4月26日  
*/
@ToString
public class ProviderExtVo {

	/**
	 * 委托人姓名
	 */
	private String proxyName;
	
	/**
	 * 委托书编号
	 */
	private String proxyCode;
	
	/**
	 * 委托书效期
	 */
	private String proxyExpiredDate;
	
	/**
	 * 药品经营许可证
	 */
	private String drugBusinessPermit;

	/**
	 * 药品经营许可证发证机关
	 */
	private String drugBusinessOrgan;

	/**
	 * 药品经营许可注册日期
	 */
	private String drugBusinessExpiredStart;

	/**
	 * 药品经营许可有效期至
	 */
	private String drugBusinessExpiredDate;

	/**
	 * 药品经营许可证经营范围
	 */
	private String drugBusinessScope;

	
	/**
	 * 质量保证协议
	 */
	private String qualityAgreement;
	
	/**
	 * 质量保证协议效期
	 */
	private String qualityAgreementExpiredDate;
	
	
	/**
	 * GSP证书
	 */
	private String GSPCertificate;


	/**
	 * GSP证书发证机关
	 */
	private String gSPCertificateOrgan;

	/**
	 * GSP证书注册日期
	 */
	private String gSPCertificateExpiredStart;

	/**
	 * GSP证书有效期至
	 */
	private String gSPCertificateExpiredDate;
	
	
	/**
	 * 卫生许可证
	 */
	private String hygienicPermit;
	
	
	/**
	 * 卫生许可证效期
	 */
	private String hygienicLicenseExpiredDate;
	
	/**
	 * 食品流通许可证
	 */
	private String foodCirculationPermit;
	
	/**
	 * 食品流通许可效期
	 */
	private String foodCirculationPermitExpiredDate;
	
	
	/**
	 * 医疗器械经营许可证
	 */
	private String medicalDeviceBusinessPermit;

	/**
	 * 医疗器械经营许可证效期
	 */
	private String medicalDeviceBusinessPermitExpiredDate;
	
	/**
	 * 食品经营许可证
	 */
	private String foodBusinessPermit;
	
	/**
	 * 食品经营许可证效期
	 */
	private String foodBusinessPermitExpiredDate;
	
	/**
	 * 二类器械备案凭证
	 */
	private String secondDeviceRecord;
	
	/**
	 * 二类器械备案凭证效期
	 */
	private String secondDeviceRecordExpiredDate;
	
	
	/**
	 * GMP证书
	 */
	private String GMPPCertificate;
	
	/**
	 * GMP证书有效期
	 */
	private String GMPPCertificateExpiredDate;
	
	/**
	 * 药品生产许可证
	 */
	private String drugProducePermit;
	
	/**
	 * 药品生产许可证效期
	 */
	private String drugProducePermitExpiredDate;

	/**
	 * 邮政编码
	 */
	private String postcode;

	/**
	 * 企业负责人
	 */
	private String responsiblePerson;

	/**
	 * 质量负责人
	 */
	private String qualityPerson;

	/**
	 * 供货单位销售人员
	 */
	private String salesPersonnel;

	/**
	 * 身份证号
	 */
	private String identityCard;

	/**
	 * 联系电话
	 */
	private String telephone;

	/**
	 * 营业执照发证机关
	 */
	private String businessLicenseOrgan;

	/**
	 * 邮箱
	 */
	private String email;
	
	public ProviderExtVo() {
		super();
	}

	public ProviderExtVo(String proxyName, String proxyCode, String proxyExpiredDate, String drugBusinessPermit, String drugBusinessOrgan, String drugBusinessExpiredStart, String drugBusinessExpiredDate, String drugBusinessScope, String qualityAgreement, String qualityAgreementExpiredDate, String GSPCertificate, String gSPCertificateOrgan, String gSPCertificateExpiredStart, String gSPCertificateExpiredDate, String hygienicPermit, String hygienicLicenseExpiredDate, String foodCirculationPermit, String foodCirculationPermitExpiredDate, String medicalDeviceBusinessPermit, String medicalDeviceBusinessPermitExpiredDate, String foodBusinessPermit, String foodBusinessPermitExpiredDate, String secondDeviceRecord, String secondDeviceRecordExpiredDate, String GMPPCertificate, String GMPPCertificateExpiredDate, String drugProducePermit, String drugProducePermitExpiredDate, String postcode, String responsiblePerson, String qualityPerson, String salesPersonnel, String identityCard, String telephone, String businessLicenseOrgan, String email) {
		this.proxyName = proxyName;
		this.proxyCode = proxyCode;
		this.proxyExpiredDate = proxyExpiredDate;
		this.drugBusinessPermit = drugBusinessPermit;
		this.drugBusinessOrgan = drugBusinessOrgan;
		this.drugBusinessExpiredStart = drugBusinessExpiredStart;
		this.drugBusinessExpiredDate = drugBusinessExpiredDate;
		this.drugBusinessScope = drugBusinessScope;
		this.qualityAgreement = qualityAgreement;
		this.qualityAgreementExpiredDate = qualityAgreementExpiredDate;
		this.GSPCertificate = GSPCertificate;
		this.gSPCertificateOrgan = gSPCertificateOrgan;
		this.gSPCertificateExpiredStart = gSPCertificateExpiredStart;
		this.gSPCertificateExpiredDate = gSPCertificateExpiredDate;
		this.hygienicPermit = hygienicPermit;
		this.hygienicLicenseExpiredDate = hygienicLicenseExpiredDate;
		this.foodCirculationPermit = foodCirculationPermit;
		this.foodCirculationPermitExpiredDate = foodCirculationPermitExpiredDate;
		this.medicalDeviceBusinessPermit = medicalDeviceBusinessPermit;
		this.medicalDeviceBusinessPermitExpiredDate = medicalDeviceBusinessPermitExpiredDate;
		this.foodBusinessPermit = foodBusinessPermit;
		this.foodBusinessPermitExpiredDate = foodBusinessPermitExpiredDate;
		this.secondDeviceRecord = secondDeviceRecord;
		this.secondDeviceRecordExpiredDate = secondDeviceRecordExpiredDate;
		this.GMPPCertificate = GMPPCertificate;
		this.GMPPCertificateExpiredDate = GMPPCertificateExpiredDate;
		this.drugProducePermit = drugProducePermit;
		this.drugProducePermitExpiredDate = drugProducePermitExpiredDate;
		this.postcode = postcode;
		this.responsiblePerson = responsiblePerson;
		this.qualityPerson = qualityPerson;
		this.salesPersonnel = salesPersonnel;
		this.identityCard = identityCard;
		this.telephone = telephone;
		this.businessLicenseOrgan = businessLicenseOrgan;
		this.email = email;
	}

	public String getDrugBusinessScope() {
		return drugBusinessScope;
	}

	public void setDrugBusinessScope(String drugBusinessScope) {
		this.drugBusinessScope = drugBusinessScope;
	}

	public String getProxyName() {
		return proxyName;
	}

	public void setProxyName(String proxyName) {
		this.proxyName = proxyName;
	}

	public String getProxyCode() {
		return proxyCode;
	}

	public void setProxyCode(String proxyCode) {
		this.proxyCode = proxyCode;
	}

	public String getProxyExpiredDate() {
		return proxyExpiredDate;
	}

	public void setProxyExpiredDate(String proxyExpiredDate) {
		this.proxyExpiredDate = proxyExpiredDate;
	}

	public String getDrugBusinessPermit() {
		return drugBusinessPermit;
	}

	public void setDrugBusinessPermit(String drugBusinessPermit) {
		this.drugBusinessPermit = drugBusinessPermit;
	}
	public String getQualityAgreement() {
		return qualityAgreement;
	}

	public void setQualityAgreement(String qualityAgreement) {
		this.qualityAgreement = qualityAgreement;
	}

	public String getQualityAgreementExpiredDate() {
		return qualityAgreementExpiredDate;
	}

	public void setQualityAgreementExpiredDate(String qualityAgreementExpiredDate) {
		this.qualityAgreementExpiredDate = qualityAgreementExpiredDate;
	}

	public String getGSPCertificate() {
		return GSPCertificate;
	}

	public void setGSPCertificate(String gSPCertificate) {
		GSPCertificate = gSPCertificate;
	}

	public String getHygienicPermit() {
		return hygienicPermit;
	}

	public void setHygienicPermit(String hygienicPermit) {
		this.hygienicPermit = hygienicPermit;
	}

	public String getHygienicLicenseExpiredDate() {
		return hygienicLicenseExpiredDate;
	}

	public void setHygienicLicenseExpiredDate(String hygienicLicenseExpiredDate) {
		this.hygienicLicenseExpiredDate = hygienicLicenseExpiredDate;
	}

	public String getFoodCirculationPermit() {
		return foodCirculationPermit;
	}

	public void setFoodCirculationPermit(String foodCirculationPermit) {
		this.foodCirculationPermit = foodCirculationPermit;
	}

	public String getFoodCirculationPermitExpiredDate() {
		return foodCirculationPermitExpiredDate;
	}

	public void setFoodCirculationPermitExpiredDate(String foodCirculationPermitExpiredDate) {
		this.foodCirculationPermitExpiredDate = foodCirculationPermitExpiredDate;
	}

	public String getMedicalDeviceBusinessPermit() {
		return medicalDeviceBusinessPermit;
	}

	public void setMedicalDeviceBusinessPermit(String medicalDeviceBusinessPermit) {
		this.medicalDeviceBusinessPermit = medicalDeviceBusinessPermit;
	}

	public String getMedicalDeviceBusinessPermitExpiredDate() {
		return medicalDeviceBusinessPermitExpiredDate;
	}

	public void setMedicalDeviceBusinessPermitExpiredDate(String medicalDeviceBusinessPermitExpiredDate) {
		this.medicalDeviceBusinessPermitExpiredDate = medicalDeviceBusinessPermitExpiredDate;
	}

	public String getFoodBusinessPermit() {
		return foodBusinessPermit;
	}

	public void setFoodBusinessPermit(String foodBusinessPermit) {
		this.foodBusinessPermit = foodBusinessPermit;
	}

	public String getFoodBusinessPermitExpiredDate() {
		return foodBusinessPermitExpiredDate;
	}

	public void setFoodBusinessPermitExpiredDate(String foodBusinessPermitExpiredDate) {
		this.foodBusinessPermitExpiredDate = foodBusinessPermitExpiredDate;
	}

	public String getSecondDeviceRecord() {
		return secondDeviceRecord;
	}

	public void setSecondDeviceRecord(String secondDeviceRecord) {
		this.secondDeviceRecord = secondDeviceRecord;
	}

	public String getSecondDeviceRecordExpiredDate() {
		return secondDeviceRecordExpiredDate;
	}

	public void setSecondDeviceRecordExpiredDate(String secondDeviceRecordExpiredDate) {
		this.secondDeviceRecordExpiredDate = secondDeviceRecordExpiredDate;
	}

	public String getGMPPCertificate() {
		return GMPPCertificate;
	}

	public void setGMPPCertificate(String gMPPCertificate) {
		GMPPCertificate = gMPPCertificate;
	}

	public String getGMPPCertificateExpiredDate() {
		return GMPPCertificateExpiredDate;
	}

	public void setGMPPCertificateExpiredDate(String gMPPCertificateExpiredDate) {
		GMPPCertificateExpiredDate = gMPPCertificateExpiredDate;
	}

	public String getDrugProducePermit() {
		return drugProducePermit;
	}

	public void setDrugProducePermit(String drugProducePermit) {
		this.drugProducePermit = drugProducePermit;
	}

	public String getDrugProducePermitExpiredDate() {
		return drugProducePermitExpiredDate;
	}

	public void setDrugProducePermitExpiredDate(String drugProducePermitExpiredDate) {
		this.drugProducePermitExpiredDate = drugProducePermitExpiredDate;
	}

	public String getPostcode() {
		return postcode;
	}

	public void setPostcode(String postcode) {
		this.postcode = postcode;
	}

	public String getResponsiblePerson() {
		return responsiblePerson;
	}

	public void setResponsiblePerson(String responsiblePerson) {
		this.responsiblePerson = responsiblePerson;
	}

	public String getQualityPerson() {
		return qualityPerson;
	}

	public void setQualityPerson(String qualityPerson) {
		this.qualityPerson = qualityPerson;
	}

	public String getSalesPersonnel() {
		return salesPersonnel;
	}

	public void setSalesPersonnel(String salesPersonnel) {
		this.salesPersonnel = salesPersonnel;
	}

	public String getIdentityCard() {
		return identityCard;
	}

	public void setIdentityCard(String identityCard) {
		this.identityCard = identityCard;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public String getBusinessLicenseOrgan() {
		return businessLicenseOrgan;
	}

	public void setBusinessLicenseOrgan(String businessLicenseOrgan) {
		this.businessLicenseOrgan = businessLicenseOrgan;
	}

	public String getDrugBusinessOrgan() {
		return drugBusinessOrgan;
	}

	public void setDrugBusinessOrgan(String drugBusinessOrgan) {
		this.drugBusinessOrgan = drugBusinessOrgan;
	}

	public String getDrugBusinessExpiredStart() {
		return drugBusinessExpiredStart;
	}

	public void setDrugBusinessExpiredStart(String drugBusinessExpiredStart) {
		this.drugBusinessExpiredStart = drugBusinessExpiredStart;
	}

	public String getgSPCertificateOrgan() {
		return gSPCertificateOrgan;
	}

	public void setgSPCertificateOrgan(String gSPCertificateOrgan) {
		this.gSPCertificateOrgan = gSPCertificateOrgan;
	}

	public String getgSPCertificateExpiredStart() {
		return gSPCertificateExpiredStart;
	}

	public void setgSPCertificateExpiredStart(String gSPCertificateExpiredStart) {
		this.gSPCertificateExpiredStart = gSPCertificateExpiredStart;
	}

	public String getDrugBusinessExpiredDate() {
		return drugBusinessExpiredDate;
	}

	public void setDrugBusinessExpiredDate(String drugBusinessExpiredDate) {
		this.drugBusinessExpiredDate = drugBusinessExpiredDate;
	}

	public String getgSPCertificateExpiredDate() {
		return gSPCertificateExpiredDate;
	}

	public void setgSPCertificateExpiredDate(String gSPCertificateExpiredDate) {
		this.gSPCertificateExpiredDate = gSPCertificateExpiredDate;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
}
