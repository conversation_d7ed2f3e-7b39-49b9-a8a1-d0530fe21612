package com.xyy.saas.supplier.provider.vo;

import com.xyy.saas.supplier.provider.po.ProviderAreaInfoPo;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * saas_provider_area_info
 * <AUTHOR>
@Data
@ToString
public class ProviderAreaInfoVo extends ProviderAreaInfoPo {

    /**
     * 类型: 1-河北药监，2-河北器械'
     */
    private Byte type;
    /**
     * 供应商名称
     */
    private String providerName;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 注册地址
     */
    private String registeredAddress;

    /**
     * 营业执照编码
     */
    private String businessLicense;

    /**
     * 供应商编码
     */
    private String pharmacyPref;

    /**
     * 供应商编码
     */
    private List<String> prefList;




}