package com.xyy.saas.supplier.provider.po;

import lombok.ToString;

/**
 * 同步给九州通供应商数据
 */

@ToString
public class JiuZhouTongProviderBaseinfo {
    /** 主键id*/
    private int LIUS_NO;

    /** 供应商编号*/
    private String Ssa_No;

    /**客户id*/
    private String Ssa_Id;

    /** 助记码*/
    private String Mnemonic_Code;

    /** 供应商名称*/
    private String Ssa_Name;

    /** 供应商简称*/
    private String Ssa_Shortname;

    /** 药店地址*/
    private String Ssa_Addr_Phone;

    /** 业务员*/
    private String Yew_Staff;

    /** 业务员电话*/
    private String  Yew_Staff_Phone;

    /** 地址简称*/
    private String  Address_Shortname;

    /** 客户标示默认2   1:上游，2:下游  */
    private String Ssa_Sign;

    /** 是否医院客户默认N */
    private String Is_Hospital_Customer;

    /** 是否活动默认Y*/
    private String Is_Active;

    /** 申请人 4adf01dd94214d1cb6a1d29356684ea4 */
    private String Creator;

    /** 创建时间*/
    private String Created_Time;
    /** 修改时间*/
    private String Updated_Time;

    /** 详细地址*/
    private String Address;

    /** 状态 默认1 */
    private String Status;


    /** 联系人 企业负责人*/
    private String Contact_Name;

    /** 联系电话*/
    private String Contact_Phone;


    /** 是否默认配送地址　默认Y*/
    private String Is_Default;

    /** 委托方ID 7ac915c10ef44eee84f05cd6bf7eecef*/
    private String Con_Id;

    /** 单据下传方 默认1 */
    private String Download_Side;

    /** 银行账号*/
    private String Bankaccount;

    /** 营业执照号*/
    private String Business_Licence_No;

    /** 营业执照有效期至*/
    private String Business_Licence_No_Valid_Until;

    /** 生产/经营许可证号*/
    private String Production_License_No;

    /** 生产/经营许可证号有效期至*/
    private String Production_License_No_Valid_Until;

    public int getLIUS_NO() {
        return LIUS_NO;
    }

    public void setLIUS_NO(int LIUS_NO) {
        this.LIUS_NO = LIUS_NO;
    }

    public String getSsa_No() {
        return Ssa_No;
    }

    public void setSsa_No(String ssa_No) {
        Ssa_No = ssa_No;
    }

    public String getSsa_Id() {
        return Ssa_Id;
    }

    public void setSsa_Id(String ssa_Id) {
        Ssa_Id = ssa_Id;
    }

    public String getMnemonic_Code() {
        return Mnemonic_Code;
    }

    public void setMnemonic_Code(String mnemonic_Code) {
        Mnemonic_Code = mnemonic_Code;
    }

    public String getSsa_Name() {
        return Ssa_Name;
    }

    public void setSsa_Name(String ssa_Name) {
        Ssa_Name = ssa_Name;
    }

    public String getSsa_Shortname() {
        return Ssa_Shortname;
    }

    public void setSsa_Shortname(String ssa_Shortname) {
        Ssa_Shortname = ssa_Shortname;
    }

    public String getSsa_Addr_Phone() {
        return Ssa_Addr_Phone;
    }

    public void setSsa_Addr_Phone(String ssa_Addr_Phone) {
        Ssa_Addr_Phone = ssa_Addr_Phone;
    }

    public String getYew_Staff() {
        return Yew_Staff;
    }

    public void setYew_Staff(String yew_Staff) {
        Yew_Staff = yew_Staff;
    }

    public String getYew_Staff_Phone() {
        return Yew_Staff_Phone;
    }

    public void setYew_Staff_Phone(String yew_Staff_Phone) {
        Yew_Staff_Phone = yew_Staff_Phone;
    }

    public String getAddress_Shortname() {
        return Address_Shortname;
    }

    public void setAddress_Shortname(String address_Shortname) {
        Address_Shortname = address_Shortname;
    }

    public String getSsa_Sign() {
        return Ssa_Sign;
    }

    public void setSsa_Sign(String ssa_Sign) {
        Ssa_Sign = ssa_Sign;
    }

    public String getIs_Hospital_Customer() {
        return Is_Hospital_Customer;
    }

    public void setIs_Hospital_Customer(String is_Hospital_Customer) {
        Is_Hospital_Customer = is_Hospital_Customer;
    }

    public String getIs_Active() {
        return Is_Active;
    }

    public void setIs_Active(String is_Active) {
        Is_Active = is_Active;
    }

    public String getCreator() {
        return Creator;
    }

    public void setCreator(String creator) {
        Creator = creator;
    }



    public String getAddress() {
        return Address;
    }

    public void setAddress(String address) {
        Address = address;
    }

    public String getStatus() {
        return Status;
    }

    public void setStatus(String status) {
        Status = status;
    }

    public String getContact_Name() {
        return Contact_Name;
    }

    public void setContact_Name(String contact_Name) {
        Contact_Name = contact_Name;
    }

    public String getContact_Phone() {
        return Contact_Phone;
    }

    public void setContact_Phone(String contact_Phone) {
        Contact_Phone = contact_Phone;
    }

    public String getIs_Default() {
        return Is_Default;
    }

    public void setIs_Default(String is_Default) {
        Is_Default = is_Default;
    }

    public String getCon_Id() {
        return Con_Id;
    }

    public void setCon_Id(String con_Id) {
        Con_Id = con_Id;
    }

    public String getDownload_Side() {
        return Download_Side;
    }

    public void setDownload_Side(String download_Side) {
        Download_Side = download_Side;
    }

    public String getBankaccount() {
        return Bankaccount;
    }

    public void setBankaccount(String bankaccount) {
        Bankaccount = bankaccount;
    }

    public String getBusiness_Licence_No() {
        return Business_Licence_No;
    }

    public void setBusiness_Licence_No(String business_Licence_No) {
        Business_Licence_No = business_Licence_No;
    }

    public String getProduction_License_No() {
        return Production_License_No;
    }

    public void setProduction_License_No(String production_License_No) {
        Production_License_No = production_License_No;
    }

    public String getCreated_Time() {
        return Created_Time;
    }

    public void setCreated_Time(String created_Time) {
        Created_Time = created_Time;
    }

    public String getUpdated_Time() {
        return Updated_Time;
    }

    public void setUpdated_Time(String updated_Time) {
        Updated_Time = updated_Time;
    }

    public String getBusiness_Licence_No_Valid_Until() {
        return Business_Licence_No_Valid_Until;
    }

    public void setBusiness_Licence_No_Valid_Until(String business_Licence_No_Valid_Until) {
        Business_Licence_No_Valid_Until = business_Licence_No_Valid_Until;
    }

    public String getProduction_License_No_Valid_Until() {
        return Production_License_No_Valid_Until;
    }

    public void setProduction_License_No_Valid_Until(String production_License_No_Valid_Until) {
        Production_License_No_Valid_Until = production_License_No_Valid_Until;
    }
}